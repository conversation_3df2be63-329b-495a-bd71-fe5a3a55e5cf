#!/bin/bash

# select_target 함수: 변경된 파일 목록과 full_deploy 여부를 리턴
# $1: source_owner_dir (기준 디렉토리)

select_target() {
  # origin/main과 HEAD(현재 체크아웃된 브랜치의 최신 커밋)의 notes 디렉토리 비교.
  local source_owner_dir=$1
  git fetch origin main

  echo "Running git diff..."
  dags=$(git diff --name-status origin/main...HEAD -- ${source_owner_dir})
  full_deploy=false
  file_output=()
  echo "dags: $dags"

  if [[ -n $dags ]]; then
    while IFS= read -r line; do
      status=$(echo $line | cut -d' ' -f1) # A, M, D, R...
      path=$(echo $line | cut -d' ' -f2) # 기준 path
      latest_commit_id=$(git log --all --pretty=format:"%H" -- $path | tail -n 1)
      object=$(git cat-file -t ${latest_commit_id}:${path} 2>/dev/null) # output: tree, blob, ...

      # file의 A(추가),M(수정)일때만 개별 작업 배포 대상.
      if [[ ("$status" == "A" || "$status" == "M") && "$object" == "blob" ]]; then
        file_output+=("$path")
      else
        full_deploy=true
        echo "Owner dag 전체 배포"
        break
      fi
    done <<< "$dags"
  else
    echo "변경 사항이 없습니다."
    exit 1
  fi

  echo "file_output=${file_output[@]}"
  echo "full_deploy=${full_deploy}"

}

# 함수 실행
select_target "$1"