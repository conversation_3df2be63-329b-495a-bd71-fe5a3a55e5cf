from airflow import templates
from airflow.plugins_manager import AirflowPlugin
from airflow.settings import TIMEZONE


class AirplantFilterPlugin(AirflowPlugin):
    name = "airplant_filter"

    custom_filters = {
        "basetime": lambda value: value.astimezone(TIMEZONE) if value else None,
        "basetime_str": lambda value: (
            value.astimezone(TIMEZONE).strftime("%Y-%m-%d %H:%M:%S") if value else None
        ),
        "yyyy": lambda value: value.strftime("%Y") if value else None,
        "mm": lambda value: value.strftime("%m") if value else None,
        "dd": lambda value: value.strftime("%d") if value else None,
        "time": lambda value: value.strftime("%H:%M:%S") if value else None,
        "time_nocolon": lambda value: value.strftime("%H%M%S") if value else None,
        "hh24": lambda value: value.strftime("%H") if value else None,
        "mi": lambda value: value.strftime("%M") if value else None,
        "ss": lambda value: value.strftime("%S") if value else None,
    }

    templates.FILTERS.update(custom_filters)
