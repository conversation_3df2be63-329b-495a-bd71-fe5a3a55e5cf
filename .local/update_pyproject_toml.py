# /// script
# dependencies = [
#   "requests",
#   "tomlkit",
# ]
# ///

import tomlkit


def fetch_constraints(url):
    print(f"Fetching constraints from {url}")
    import requests
    response = requests.get(url)
    lines = response.text.splitlines()
    constraints = [line.strip() for line in lines if line.strip() and not line.startswith("#")]
    return constraints

def main():
    # Read pyproject.toml
    with open("pyproject.toml") as file:
        pyproject = tomlkit.parse(file.read())

    # Extract python version and airflow version
    python_version = pyproject["project"]["requires-python"].replace("=", "").replace(">", "").split(".")[:2]
    python_version = ".".join(python_version)

    dependencies = pyproject["project"]["dependencies"]
    airflow_version = next(dep.split("==")[1] for dep in dependencies if dep.startswith("apache-airflow=="))

    constraints_url = f"https://raw.githubusercontent.com/apache/airflow/constraints-{airflow_version}/constraints-{python_version}.txt"
    constraints = fetch_constraints(constraints_url)

    if 'tool' not in pyproject:
        pyproject['tool'] = tomlkit.table()
    if 'uv' not in pyproject['tool']:
        pyproject['tool']['uv'] = tomlkit.table()

    pyproject['tool']['uv']['constraint-dependencies'] = tomlkit.array(constraints)

    with open("pyproject.toml", "w") as file:
        file.write(tomlkit.dumps(pyproject))

if __name__ == "__main__":
    main()
