= AirPlant ETL

Airplant 를 운영하기위한 dags 와 plugin 등을 관리합니다.

== 개발 환경 구성

IDE 구성을 진행하기 전에 terminal 에서 make 를 통해 구성하는 것을 권장합니다.

> 한줄 요약: `make install` 이후 IDE 구성 진행

내부적으로 uv 를 사용하지만 이를 편하게 사용하기 위한 `Makefile` 을 제공합니다.

`make` 또는 `make help` 를 통해 사용법을 확인할 수 있습니다.

> make 가 설치되어 있지 않을 경우: https://command-not-found.com/make

[source,bash]
----
$ make
Usage: make <TARGET>

  help                 Show this help.
  install              Install python packages (include venv) / optional: pr=(airplant-pr-number)
  venv                 Install uv and setup the virtual environment
  lint                 Check source code
  format               Format source code
  clean                remove all environment and build files
----

=== 의존성 설치

`make install` 으로 시스템 패키지부터, venv, python package 까지 모두 설치한다.

시스템 설치는 `sudo` 를 통해 진행하므로 패스워드 입력이 필요하다.
* 이 부분 맥북에서 make install 작업전 source $HOME/.local/bin/env 이 설정을 먼저 적용해야 할때가 있다. 

[source,bash]
----
make install

--> Detect OS and Installing system dependencies
Detected Linux. Checking distribution...
Detected RHEL/CentOS. Installing system dependencies...
[sudo] password for user:
Last metadata expiration check: 0:14:17 ago on Fri 22 Nov 2024 02:58:52 AM KST.
Package gcc-8.5.0-22.el8_10.x86_64 is already installed.
Package gcc-c++-8.5.0-22.el8_10.x86_64 is already installed.
Package clang-17.0.6-1.module+el8.10.0+727+4e2b2675.x86_64 is already installed.
Package cyrus-sasl-gssapi-2.1.27-6.el8_5.x86_64 is already installed.
Package cyrus-sasl-lib-2.1.27-6.el8_5.x86_64 is already installed.
Package cyrus-sasl-plain-2.1.27-6.el8_5.x86_64 is already installed.
Package krb5-workstation-1.18.2-30.el8_10.x86_64 is already installed.
Package openssl-devel-1:1.1.1k-14.el8_6.x86_64 is already installed.
Dependencies resolved.
Nothing to do.
Complete!
Ensuring uv is installed...
uv is already installed.
--> Install uv and setup virtualenv
uv venv
Using CPython 3.12.6
Creating virtual environment at: .venv
Activate with: source .venv/bin/activate
--> Install python packages
uv pip install --link-mode=copy -r pyproject.toml
Resolved 192 packages in 578ms
Installed 192 packages in 4m 18s
----

=== lint, formatting

`make` 를 통해 `uvx ruff` 를 사용하도록 구성되어 있다.

[source,bash]
----
make lint
make format
----

=== 가상 환경

`make venv` 로 가상 환경을 설치하면 자동으로 activate 된다.
(`make install` 실행 시 `venv` 도 같이 수행되므로 최초 구성 시에는 별도로 수행할 필요 없다.)

수동으로 activate 하려면 아래와 같이 수행한다.

[source,bash]
----
source .venv/bin/activate

deactivate
----

종료는 활성화 상태에서 `deactivate` 로 한다.


==== IDE 설정

`make install` 를 통해 패키지 설치가 완료된 이후에 진행

===== Visual Studio Code (권장)

아래와 같이 이미 설정되어 있으므로 특별히 설정할 것은 없다.

`.vscode/settings.json`
[source,json]
----
{
  "python.analysis.autoImportCompletions": true,
  "python.autoComplete.extraPaths": [".venv/lib/python3.12/site-packages"],
  "python.analysis.extraPaths": [".venv/lib/python3.12/site-packages"],
}
----

===== Intellij IDEA / PyCharm

IDEA 는 [프로젝트 설정] 메뉴가 별도로 존재
구버전은 [파일] 메뉴 하위에, 신 버전은 화면 우측 상단의 톱니 아이콘을 통해 접근 가능

PyCharm 은 [설정] 하위에 프로젝트 메뉴 존재

* [설정 - 프로젝트]
** [Python 인터프리터] : [Python 인터프리터 추가 - 로컬 인터프리터 추가 - Virtualenv 환경]
*** [기존] `.venv/bin/python`
*** [새로 만들기] 권장하지 않음
** [프로젝트 구조] `airplant/common/dags`, `.venv/lib/python3.12/site-packages` 를 `소스` 로 지정

== DAG 배포 정책

* dev 환경
- PR Push 변경사항이 있을 때 배포됩니다
- dev 환경은 수시로 배포되어야 하기 때문에 push 시점을 감지합니다
- 다른 팀의 DAG 가 엎어치는 문제를 막기위해 자기 팀의 라벨링을 선택해야 배포 됩니다 (예시: `owner:addata`)

* test 환경
- PR Merge 변경사항이 있을 때 배포됩니다
- test 환경은 dev 에서 검증된 코드가 배포된다고 보기 때문에 merge 시점에 감지합니다
- 다른 팀의 DAG 가 엎어치는 문제를 막기위해 자기 팀의 라벨링을 선택해야 배포 됩니다 (예시: `owner:addata`)

* prod 환경
- prod 환경은 https://oss.navercorp.com/Ngela/airplant-etl/releases[Release] 를 생성시 배포 됩니다.
- 릴리즈에서는 태그명을 통해 판단합니다 | `v년월일-오너명+패치순번`
.. 릴리즈에서는 라벨링을 할 수 없어서 태그명을 토대로 합니다
.. (사용예시) v20230101-addata+1

== Plugin / ETC 배포 정책

DAGS 폴더는 owner 별로 배포 되어야 합니다.

하지만, plugin, variable, connection 과 같은 정보는 통합 관리 배포 되어야 하는 특징이 있습니다.

룰은 단순하게 가져가는것이 좋기 때문에, `owner:admin` 이라는 가상의 오너명을 사용하면 배포가 되는 구조로 정리합니다.

이렇게 기준을 정하면, dev/test/prod 의 배포 정책에서 `owner:admin` 은 DAGS 가 아닌 그 이외의 시스템에 영향을 주는 파일을 배포함을 제안합니다


== 폴더 관리 정책

[cols="1h,4a,3a,3a"]
|===
|
|Git (Current repo)
|Nubes
|Pod

|`common`
|
[source,bash]
----
airplant
└── common
    ├── dags
    │   └── [ owner ]
    ├── plugins
----
|
[source]
----
airplant
└── common
    ├── dags
    │   └── [ owner ]
    ├── plugins
----
|
[source]
----
airplant
└── common
    ├── dags
    │   └── [ owner ]
    ├── plugins
----


|`env`
|
[source,bash]
----
    └ env
        ├── dev
        │   └── krb5
        │   └── pod_template
        ├── prod
        └── test
----
|
[source]
----
    └── env
        └── krb5
        └── pod_template
----
|
[source]
----
    └ env
        └── krb5
        └── pod_template
----

|===

== common
link:common[common] 하위의 파일들은 모두 지정된 nubes 에 upload 되며, k8s pod 에서 해당 경로를 mount 해서 동작한다.

이때 link:common/dags[common/dags] 하위의 최상위 디렉토리는 owner 이름이 되며,
배포 시 해당 경로 하위의 `DAG` 에는 owner 이름과 같은 role 을 가진 `access_control` 이 추가된다.

== env

> 이와 관련된 정책은 아직 미완이며, 차후 변경 가능성이 높다.

link:env[env] 하위에는 환경(profile)별 디렉토리가 있고, 그 하위에 kerberos, hadoop 관련 설정이 존재한다.

배포 시에는 대상 환경에 해당하는 파일들만 nubes 에 upload 되고, 그 역시 k8s pod 에서 mount 해서 사용한다.

== Table/Column Naming Rule
==== Common Rule
- underscore를 사용하여 가독성을 높인다
- 소문자를 사용한다
- 테이블명은 단수형 사용한다 (user (O), users (X))
- 상태를 나타내는 컬럼은 status 또는 is_active, has_reference 형태를 사용한다
- 가능한한 전체 단어를 사용한다(약어 최소화 / customer(O), cust (X))
- 긴 단어의 경우 일반적으로 인정되는 줄임말, 축약어를 사용할 수 있다(API (O), Application Programming Interface (X))
- 단순해야한다
- 직관적이고 친숙해야 한다 (delete (O), erase (X))
- 일관적이어야 한다
- 미국 영어를 사용한다 (color (O), colour (X))
- PK 컬럼을 id로 명명하지 말고 full name을 쓴다 (customer_id (O), id(X))

==== Naver 광고 프로덕트 Rule
- 로그 스키마와 동일하게 한다
- 날짜 컬럼은 지정된 형태로 쓴다
- 실적 테이블에서 stat은 제외한다
- 실적 테이블의 집계 주기는 hourly, daily 등 full name으로 쓴다 (hourly (O), tdly(X))
- 테이블과 view를 구분했던 t_, v_는 사용하지 않는다 (customer (O), t_customer (X))
