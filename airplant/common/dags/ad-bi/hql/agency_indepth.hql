USE ad_bi__db_dm{{params.db_postfix}};

CREATE TABLE IF NOT EXISTS agency_indepth (
  year                             STRING   COMMENT '연도'
  , quarter                        STRING   COMMENT '분기'
  , month                          STRING   COMMENT '월'
  , week_number                    STRING   COMMENT '주'
  , partner_category               STRING   COMMENT '파트너사_카테고리'
  , agency_id                      STRING   COMMENT '에이전시id'
  , agency_number                  STRING   COMMENT '에이전시_사업자번호'
  , proxy_agency_id                STRING   COMMENT '대대행사id'
  , cust_tax_num                   STRING   COMMENT '광고주_사업자번호'
  , platform_customer_id           STRING   COMMENT '광고주id'
  , cust_name                      STRING   COMMENT '광고주명'
  , category_l                     STRING   COMMENT '업종'
  , dsp                            STRING   COMMENT '플랫폼'
  , product_type_name              STRING   COMMENT '광고상품'
  , ad_placement                   STRING   COMMENT '지면'
  , cost                           DOUBLE   COMMENT '매출'
)

COMMENT '에이전시 인뎁스 대시보드'

PARTITIONED BY (
  ymd      STRING COMMENT '날짜')

STORED AS ORC
TBLPROPERTIES (
  "orc.compress" = "ZLIB",
  "external.table.purge" = "true"
);


ALTER TABLE agency_indepth DROP IF EXISTS PARTITION(ymd = '{{logical_date | basetime | ds}}');

CREATE TEMPORARY TABLE tmp_agency_indepth AS

SELECT
    SUBSTR(c.ymd, 1, 4) AS year,
    QUARTER(c.ymd) AS quarter,
    SUBSTR(c.ymd, 6, 2) AS month,
    WEEKOFYEAR(c.ymd) AS week_number,
    c.partner_category,
    CASE WHEN
    	c.rep_agency_id > 0 THEN c.rep_agency_id -- 렙사가 개입한 매출은 렙사의 매출로 산입
      ELSE c.agency_id
      END AS agency_id,
    CASE WHEN
    	c.rep_agency_id > 0 THEN c.rep_num
      ELSE c.agency_num
      END AS agency_number,
    c.proxy_agency_id,
	  c.cust_tax_num AS customer_tax_number,
    c.platform_customer_id AS customer_id,
    c.customer_name AS customer_name,
    CASE
      WHEN c.cross_biz_large_category_name IS NULL AND c.substitute_category IS NOT NULL THEN c.substitute_category
      WHEN c.cross_biz_large_category_name IS NULL THEN '미등록'
      WHEN c.cross_biz_large_category_name = '-' AND c.substitute_category IS NOT NULL THEN c.substitute_category
      WHEN c.cross_biz_large_category_name = '-' THEN '미등록'
      WHEN c.cross_biz_large_category_name = '생활용품/서비스' THEN '생활용품'
      WHEN c.cross_biz_large_category_name IN ('전자/가전', 'ICT') THEN '전자/ICT'
      WHEN c.cross_biz_large_category_name = '교육/취업' THEN '교육/서비스'
      ELSE c.cross_biz_large_category_name
      END AS category_l,
	    c.dsp,
      CASE
          WHEN c.dsp = 'GFA' THEN 'GFA'
          WHEN c.dsp = 'NDP'
               AND c.product_type_large_name NOT IN ('검색', '세금계산서보정') THEN 'NDP'
          WHEN c.product_type_middle_name IN ('서칭뷰', '브랜드검색', '신제품검색') THEN c.product_type_middle_name
          WHEN c.product_type_id IN ('4:5', '4:8') THEN '브랜드검색'
          WHEN c.product_type_id = '4:11' THEN '신제품검색'
          WHEN c.product_type_id LIKE '1%' THEN '파워링크'
          WHEN c.product_type_id LIKE '2%' THEN '쇼핑검색광고'
          WHEN c.product_type_id LIKE '3%' THEN '파워컨텐츠'
          WHEN c.product_type_id LIKE '6%' THEN '플레이스광고'
      END AS product_type_name,
      c.product_type_large_name AS ad_placement,
      SUM(c.cost) AS cost
FROM (
    SELECT stat.*,
      ag.business_tax_num AS agency_num,
  	  rep.business_tax_num AS rep_num,
      mc.business_tax_num AS cust_tax_num,
   	  mc.customer_name,
  	  bc.cross_biz_large_category_name AS cross_biz_large_category_name,
  	  sub.cross_biz_large_category_name AS substitute_category,
  	  pd.product_type_large_name AS product_type_large_name,
  	  pd.product_type_middle_name,
  	  CASE
  			WHEN stat.rep_agency_id > 0 THEN '렙사' ELSE '파트너사'
  			END AS partner_category
    FROM addata__db_dm.sales_bi_ad_performance_daily AS stat
    LEFT JOIN addata__db_meta.sales_bi_agency AS ag
        ON stat.agency_id = ag.agency_id
  	  	AND stat.dsp = ag.dsp
  	LEFT JOIN addata__db_meta.sales_bi_agency AS rep
  		  ON stat.rep_agency_id = rep.agency_id
  		  AND stat.dsp = rep.dsp
    LEFT JOIN addata__db_meta.sales_bi_customer AS mc
        ON stat.platform_customer_id = mc.platform_customer_id
 		    AND stat.dsp = mc.dsp
  	LEFT JOIN addata__db_meta.sales_bi_product AS pd
  	  	ON stat.product_type_id = pd.product_type_id
  	  	AND stat.dsp = pd.dsp
  	LEFT JOIN addata__db_meta.sales_bi_business_channel AS bc
  	  	ON stat.business_channel_id = bc.business_channel_id
  	  	AND stat.dsp = bc.dsp
  	LEFT JOIN
  		(SELECT
		  customer_number,
    	  cross_biz_large_category_name,
    	  total_cost
			  FROM (
    		SELECT
  			    mc.business_tax_num AS customer_number,
            CASE
                WHEN bc.cross_biz_large_category_name = '생활용품/서비스' THEN '생활용품'
                WHEN bc.cross_biz_large_category_name IN ('전자/가전','ICT') THEN '전자/ICT'
                WHEN bc.cross_biz_large_category_name = '교육/취업' THEN '교육/서비스'
                WHEN bc.cross_biz_large_category_name = '-' THEN '미등록'
              	WHEN bc.cross_biz_large_category_name IS NULL THEN '미등록'
                ELSE bc.cross_biz_large_category_name
            END AS cross_biz_large_category_name,
   		      SUM(cost) AS total_cost,
   	        ROW_NUMBER() OVER (
            PARTITION BY mc.business_tax_num
            ORDER BY SUM(cost) DESC
            ) AS rn
             FROM addata__db_dm.sales_bi_ad_performance_daily AS stat
                  LEFT JOIN addata__db_meta.sales_bi_customer AS mc
                    ON stat.platform_customer_id = mc.platform_customer_id
                    AND stat.dsp = mc.dsp
                  LEFT JOIN addata__db_meta.sales_bi_business_channel AS bc
                    ON stat.business_channel_id = bc.business_channel_id
                    AND stat.dsp = bc.dsp
                    AND bc.cross_biz_large_category_name IS NOT NULL
             WHERE
                 ymd BETWEEN '{{ (logical_date - macros.timedelta(days=30)).strftime("%Y-%m-%d") }}' AND '{{ logical_date.strftime("%Y-%m-%d") }}'
                 AND stat.business_channel_id IS NOT NULL
                 AND bc.cross_biz_large_category_name IS NOT NULL
                 AND stat.cost > 0
                 AND stat.dsp = 'NCC'
             GROUP BY
              mc.business_tax_num,
              CASE
                WHEN bc.cross_biz_large_category_name = '생활용품/서비스' THEN '생활용품'
                WHEN bc.cross_biz_large_category_name IN ('전자/가전','ICT') THEN '전자/ICT'
                WHEN bc.cross_biz_large_category_name = '교육/취업' THEN '교육/서비스'
                WHEN bc.cross_biz_large_category_name = '-' THEN '미등록'
              	WHEN bc.cross_biz_large_category_name IS NULL THEN '미등록'
                ELSE bc.cross_biz_large_category_name
            END
              ) AS t
	 	    WHERE rn = 1) AS sub
          ON mc.business_tax_num = sub.customer_number
      WHERE stat.ymd = '{{logical_date | basetime | ds}}'
      AND (
        ag.business_tax_num IN (
        '647-86-01398','220-88-80926','669-81-01428','204-81-39066','581-81-00925','220-87-48138','119-86-37294','824-86-00910','119-86-06525','109-86-39941','220-87-07893','264-81-02917','605-86-03316','214-87-45437','257-81-03674','120-88-17220','220-86-60192','220-88-91971','220-87-03688','211-86-98485','214-86-56053','214-87-64680','144-81-25649','220-87-01996','120-87-58506','107-86-93497','101-86-52182','114-87-03025','886-81-02115','703-86-00909','211-88-76923','211-88-15173','144-81-15705','214-84-06262','144-81-27690','211-88-24084','261-81-24396','120-86-46264','212-86-05752','212-81-84558','120-87-74302','211-88-21544','211-88-70292','114-87-07171','120-87-01134','119-87-06634','106-81-93204','264-81-37022','211-88-14382','101-86-49275','109-86-20253','215-86-78948','220-86-00933','113-86-41728','211-87-59290','120-87-41500','109-86-17438','545-81-00889','807-86-01888','742-81-02251','388-87-01142','104-86-12229','204-81-84372','211-87-51624','118-81-20586','117-81-40065','229-81-37198','220-86-93067','120-86-14175','113-86-27124','120-86-42588','129-86-43885','117-81-48164','106-86-49566','128-86-53736','429-81-00608','318-87-00431','120-86-39351','120-81-88352','120-86-58794','770-81-00660','214-88-13475','220-81-80363','832-87-01824','102-81-43774','183-87-00290','345-86-01551','518-87-01817','495-86-02326'
	    )
        OR rep.business_tax_num IN (
        '120-86-39351','120-81-88352','120-86-58794','770-81-00660','214-88-13475','220-81-80363','832-87-01824','102-81-43774'
     ) -- 25년 8대 렙사
    )
) AS c
GROUP BY
  SUBSTR(c.ymd, 1, 4),
	QUARTER(c.ymd),
	SUBSTR(c.ymd, 6, 2),
  WEEKOFYEAR(c.ymd),
  c.partner_category,
  CASE WHEN
    c.rep_agency_id > 0 THEN c.rep_agency_id
        ELSE c.agency_id
        END,
  CASE WHEN
    c.rep_agency_id > 0 THEN c.rep_num
        ELSE c.agency_num
        END,
  c.proxy_agency_id,
	c.cust_tax_num,
  c.platform_customer_id,
  c.customer_name,
  CASE
      WHEN c.cross_biz_large_category_name IS NULL AND c.substitute_category IS NOT NULL THEN c.substitute_category
      WHEN c.cross_biz_large_category_name IS NULL THEN '미등록'
      WHEN c.cross_biz_large_category_name = '-' AND c.substitute_category IS NOT NULL THEN c.substitute_category
      WHEN c.cross_biz_large_category_name = '-' THEN '미등록'
      WHEN c.cross_biz_large_category_name = '생활용품/서비스' THEN '생활용품'
      WHEN c.cross_biz_large_category_name IN ('전자/가전', 'ICT') THEN '전자/ICT'
      WHEN c.cross_biz_large_category_name = '교육/취업' THEN '교육/서비스'
      ELSE c.cross_biz_large_category_name
      END,
	c.dsp,
  CASE
        WHEN c.dsp = 'GFA' THEN 'GFA'
        WHEN c.dsp = 'NDP'
             AND c.product_type_large_name NOT IN ('검색', '세금계산서보정') THEN 'NDP'
        WHEN c.product_type_middle_name IN ('서칭뷰', '브랜드검색', '신제품검색') THEN c.product_type_middle_name
        WHEN c.product_type_id IN ('4:5', '4:8') THEN '브랜드검색'
        WHEN c.product_type_id = '4:11' THEN '신제품검색'
        WHEN c.product_type_id LIKE '1%' THEN '파워링크'
        WHEN c.product_type_id LIKE '2%' THEN '쇼핑검색광고'
        WHEN c.product_type_id LIKE '3%' THEN '파워컨텐츠'
        WHEN c.product_type_id LIKE '6%' THEN '플레이스광고'
    END,
	c.product_type_large_name
;

INSERT OVERWRITE TABLE agency_indepth PARTITION(ymd = '{{logical_date | basetime | ds}}')

SELECT *
FROM tmp_agency_indepth;

