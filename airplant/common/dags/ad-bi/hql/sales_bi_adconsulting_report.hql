USE ad_bi__db_dm{{params.db_postfix}};

CREATE TABLE IF NOT EXISTS adconsulting_dashboard_v3 (
  week_number                    INT COMMENT '주차'
  ,year                          INT COMMENT '연도'
  ,month                         INT COMMENT '월'
  ,platform_customer_id          STRING COMMENT '플랫폼아이디'
  ,business_channel_id           STRING COMMENT '비즈채널아이디'
  ,brand_no                      STRING COMMENT '브랜드넘버'
  ,product_type_id               STRING COMMENT '상품타입아이디'
  ,impressions                   BIGINT COMMENT '노출'
  ,clicks                        BIGINT COMMENT '클릭'
  ,cost                          DOUBLE COMMENT '유상매출(VAT제외)'
  ,conversions                   INT COMMENT '전환수'
  ,conversion_revenue            BIGINT COMMENT '전환매출'
  ,dsp                           STRING COMMENT 'DSP'
  ,customer_name                 STRING COMMENT '광고주명'
  ,cross_biz_large_category_name STRING COMMENT '대업종분류'
  ,brand_key                     STRING COMMENT '브랜드매칭KEY'
  ,business_tax_num              STRING COMMENT '사업자번호'
  ,product_type_name             STRING COMMENT '상품이름'
  ,is_conv_activated             BOOLEAN COMMENT '전환추적여부'
)

COMMENT '1차 초도진단 (광컨) 테이블'
PARTITIONED BY (
  ymd      STRING COMMENT '날짜'
)

STORED AS ORC
TBLPROPERTIES (
  "orc.compress" = "ZLIB",
  "transactional" = "true"
);

ALTER TABLE   adconsulting_dashboard_v3 DROP IF EXISTS PARTITION(ymd = '{{logical_date | basetime | ds}}');


CREATE TEMPORARY TABLE tmp_adconsulting_dashboard_v3  AS
SELECT
  t1.week_number,
  t1.year,
  t1.month,
  t1.platform_customer_id,
  t1.business_channel_id,
  t1.brand_no,
  t1.product_type_id,
  t1.impressions,
  t1.clicks,
  t1.cost,
  t1.conversions,
  t1.conversion_revenue,
  t1.dsp,
  t1.customer_name,
  t1.cross_biz_large_category_name,
  t1.brand_key,
  t1.business_tax_num,
  t1.product_type_name,
  t1.is_conv_activated
FROM indepth_summary_dashboard AS t1
-- 광고주 인뎁스 테이블과 다른 부분
LEFT SEMI JOIN ad_bi__db_user_private.kr22690_ka_customer_am AS t2
  ON t1.business_tax_num = t2.customer_tax_number
WHERE t1.ymd = '{{logical_date | basetime | ds}}'
;

INSERT OVERWRITE TABLE adconsulting_dashboard_v3 PARTITION(ymd = '{{logical_date | basetime | ds}}')
SELECT * FROM tmp_adconsulting_dashboard_v3;

