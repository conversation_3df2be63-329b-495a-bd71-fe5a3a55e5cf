import datetime
from datetime import timedelta
import pendulum
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.sensors.time_delta import TimeDeltaSensorAsync
from airflow.providers.airplant.operators.hive import HiveOperator


with DAG(
    dag_id="indepth_summary_dashboard",
    start_date=datetime.datetime(2024, 1, 1 , tzinfo=pendulum.timezone("Asia/Seoul")),
    #end_date=datetime.datetime(2024, 1, 10 , tzinfo=pendulum.timezone("Asia/Seoul")), # prod 배포시 수정 필요
    schedule="@daily",
):
    from airflow.models import Variable
    db_postfix = Variable.get("db_postfix").strip()

    start = TimeDeltaSensorAsync(
        task_id="start",
        delta=timedelta(hours=8),  # 8시간 대기 후 wait task 시작
        timeout=3600 * 9,  # delta보다 큰 값으로 설정 필요
    )

    table = HiveOperator(
        task_id="table",
        hql="hql/indepth_summary_dashboard.hql",
        hiveconf_jinja_translate=True,
        hive_cli_conn_id="hivecli_terranova_bi",
        params={"db_postfix": db_postfix},
        queue="ad-bi",
    )

    end = EmptyOperator(task_id="end")

    start >> table >> end

