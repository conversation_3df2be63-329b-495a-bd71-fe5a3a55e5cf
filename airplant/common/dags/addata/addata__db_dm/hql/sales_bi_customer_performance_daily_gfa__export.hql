SET hive.exec.orc.default.compress=ZLIB;

-- GFA 광고주 상세 실적
INSERT OVERWRITE DIRECTORY "{{ params.source_location  }}/ymd={{ logical_date | basetime | ds }}/dsp=GFA"
STORED AS ORC
 SELECT
       CAST(stat.adaccountno AS STRING)          AS platform_customer_id
     , CAST(camp.biz_channel_no  AS STRING)      AS business_channel_id
     , CAST(stat.brandno AS STRING)              AS brand_no
     , stat.operatingaccountid                   AS agency_id
     , NULL                                      AS rep_agency_id
     , NULL                                      AS proxy_agency_id
     , stat.adunit			                    AS product_type_id
     , stat.campaignno							AS campaign_id
     , stat.campaignobjective					AS campaign_type
     , adsetno									AS adgroup_id
     , creativetype								AS adgroup_type
     , creativeno								AS ad_id
     , NULL										AS search_keyword
     , NULL										AS ad_keyword
     , NULL										AS matching_type
     , stat.adunit                               AS media_area_unit_id
     , 1 										AS rank
     , NULL										AS ba
     , NULL										AS qi
     ,  COALESCE(targeting_type_list, 'non-targeting') as targeting_type_list
     , stat.userids['nidNo']
     , stat.userids['buid']
     , stat.convtype                             AS conversion_type
     , SUM(NVL(stat.impcount, 0))                AS impressions
     , SUM(NVL(stat.clickcount, 0))              AS clicks
     , SUM(NVL(stat.videoimpcount, 0))           AS video_impressions
     , SUM(NVL(stat.vplaycount, 0))              AS video_views
     , SUM(CASE WHEN  NVL(stat.spend, 0) = 0 THEN 0
         ELSE (NVL(stat.spend, 0)- NVL(stat.vatspend, 0) - NVL(stat.unpaidspend, 0))/ 100000.0 END)    AS cost -- 유상 매출
     , SUM(NVL(stat.convcount, 0))               AS conversions
     , SUM(NVL(stat.convspend, 0)) / 100000.0    AS conversion_revenue
   FROM
     gfa.raw_stats_log stat
   LEFT JOIN
     gfa.v_campaign camp ON stat.campaignno = camp.campaign_no
   LEFT JOIN
     (  SELECT ymd
               , ad_set_no
               , CONCAT_WS(',',
               	SPLIT(
               		TRIM(
                  			CONCAT(
                  				IF(is_gender_targeted, 'gender ', ''),
                  				IF(is_age_targeted, 'age ', ''),
                  				IF(is_interest_targeted, 'interest ', ''),
                  				IF(is_device_targeted, 'device ', ''),
                  				IF(is_device_mobile_targeted, 'device_mobile ', ''),
                  				IF(is_device_pc_targeted, 'device_pc ', ''),
                  				IF(is_location_targeted, 'location ', ''),
                  				IF(is_adid_targeted, 'adid ', ''),
                  				IF(is_mat_targeted, 'mat ', ''),
                  				IF(is_lookalike_targeted, 'lookalike ', ''),
                  				IF(is_schedule_targeted, 'schedule ', ''),
                  				IF(is_purchase_intent_targeted, 'purchase_intent ', ''),
                  				IF(is_interest_intersection_targeted, 'interest_intersection ', ''),
                  				IF(is_aipl_targeted, 'aipl ', ''),
                  				IF(is_parents_targeted, 'parents ', ''),
                  				IF(is_shopping_news_targeted, 'shopping_news ', ''),
                  				IF(is_contextual_category_targeted, 'contextual_category ', ''),
                  				IF(is_search_keyword_targeted, 'search_keyword ', ''),
                  				IF(is_website_targeted, 'website ', ''),
                  				case
                  					when location_targeting_type = 'INTEREST_INCLUDED' then 'interest_location '
                  					ELSE ''
                  				END,
                  				case
                  					when location_targeting_type = 'EXPAND_NEARBY' then 'interest_location_expanded '
                  					ELSE ''
                  				END,
                  				IF(is_shopping_crm_targeted, 'shopping_crm ', ''),
                  				IF(is_store_targeted, 'store ', ''),
                  				IF(is_external_audience_targeted, 'external_audience', '')
                             ) ), ' ')) as targeting_type_list
            FROM gfa.ad_set_targeting
            WHERE ymd = '{{ logical_date | basetime | ds }}'
       )t ON (stat.adsetno= t.ad_set_no AND stat.ymd = t.ymd)
   WHERE
     stat.ymd = '{{ logical_date | basetime | ds }}'
     AND ( stat.impcount>0 OR stat.clickcount>0 OR stat.videoimpcount>0 OR stat.vplaycount>0 OR stat.spend>0 OR stat.convcount>0 )
  group by  CAST(stat.adaccountno AS STRING)
     , CAST(camp.biz_channel_no  AS STRING)
     , CAST(stat.brandno AS STRING)
     , stat.operatingaccountid
     , stat.campaignno
     , stat.campaignobjective
     , adsetno
     , creativetype
     , creativeno
     , stat.adunit
     , COALESCE(targeting_type_list, 'non-targeting')
     , stat.convtype
     , stat.userids['nidNo']
     , stat.userids['buid'];
