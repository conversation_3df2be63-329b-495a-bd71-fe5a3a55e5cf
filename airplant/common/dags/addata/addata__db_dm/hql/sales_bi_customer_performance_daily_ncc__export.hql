SET hive.exec.orc.default.compress=ZLIB;

-- NCC 광고주 상세 실적
INSERT OVERWRITE DIRECTORY "{{ params.source_location  }}/ymd={{ logical_date | basetime | ds }}/dsp=NCC"
STORED AS ORC
  SELECT
      platform_customer_id
    , business_channel_id
    , NULL AS brand_no
    , cust_stat.agncy_id                         AS agency_id
	, NULL                                       AS rep_agency_id
    , CASE WHEN agent_grp.is_proxy_agncy IS TRUE THEN cust_stat.agnt_grp_id
          ELSE NULL END                            AS proxy_agency_id
    , product_type_id
    , campaign_id
    , campaign_type
    , adgroup_id
    , adgroup_type
    , ad_id
    , search_keyword
    , ad_keyword
    , matching_stage_id
    , media_area_unit_id
    , rank
    , ba
    , qi
	, COALESCE(targeting_type, 'non-targeting') as targeting_type
    , nid_no
    , buid
    , conversion_type
    , SUM(impressions)           AS impressions
    , SUM(clicks)                AS clicks
    , SUM(video_impressions)     AS video_impressions
    , SUM(video_views)           AS video_views
    , SUM(cost)                  AS cost
    , SUM(conversions)           AS conversions
    , SUM(conversion_revenue)    AS conversion_revenue
  FROM (
        SELECT
        CAST(stat.cust_id AS STRING)               AS platform_customer_id
      , CAST(stat.biz_chnl_id AS STRING)           AS business_channel_id
      , CONCAT(stat.cmp_tp, ':', stat.adgrp_tp)    AS product_type_id
      , stat.cmp_id                                AS campaign_id
      , stat.cmp_tp                                AS campaign_type
      , stat.adgrp_id                              AS adgroup_id
      , stat.adgrp_tp                              AS adgroup_type
      , stat.ad_id                                 AS ad_id
      , CASE WHEN criterion_array is null or size(criterion_Array) = 0 THEN 'non-targeting' ELSE  concat_ws(',', SORT_ARRAY(COLLECT_SET(i.dictionary_type ))) END as targeting_type
      , stat.sch_kwd                               AS search_keyword
      , stat.ad_kwd                                AS ad_keyword
      , stat.matching_stage_id                     AS matching_stage_id
      , stat.chnl_id                               AS media_area_unit_id
      , stat.total_rnk                             AS rank
      , stat.ba                                    AS ba
      , stat.qi                                    AS qi
      , stat.nid_no                                AS nid_no
      , stat.buid                                  AS buid
	    , NULL									   AS conversion_type
      , SUM(CASE WHEN pos = 0 OR pos is null THEN stat.imp_cnt ELSE 0 END)       AS impressions
      , SUM(CASE WHEN pos = 0 OR pos is null THEN stat.clk_cnt ELSE 0 END)                  AS clicks
      , 0                                          AS video_impressions
      , SUM(CASE WHEN pos = 0 OR pos is null THEN stat.view_cnt ELSE 0 END)                 AS video_views
      , SUM(CASE WHEN pos = 0 OR pos is null THEN stat.sales_amt ELSE 0 END)                AS cost
      , 0                 AS conversions
      , 0                 AS conversion_revenue
    FROM
      primitive.t_sa_tdly_stat stat LATERAL VIEW OUTER posexplode(criterion_array) pe AS pos, i
    WHERE stat.ymd = '{{ logical_date | basetime | ds }}'
    	AND (stat.imp_cnt > 0 OR stat.clk_all_cnt > 0 )
    	AND ad_sales_chnl='SA'
    GROUP BY
        stat.cust_id
      , stat.biz_chnl_id
      , stat.cmp_id
      , stat.cmp_tp
      , stat.adgrp_id
      , stat.adgrp_tp
      , stat.ad_id
      , stat.sch_kwd
      , stat.ad_kwd
      , stat.matching_stage_id
      , stat.chnl_id
      , stat.total_rnk
      , stat.ba
      , stat.qi
      , stat.nid_no
      , stat.buid
      , criterion_array

    UNION ALL

	-- 1-2) NCC 전환
	SELECT
        CAST(stat.cust_id AS STRING)               AS platform_customer_id
      , CAST(stat.biz_chnl_id AS STRING)           AS business_channel_id
      , CONCAT(stat.cmp_tp, ':', COALESCE(stat.adgrp_tp, '1'))    AS product_type_id
      , stat.cmp_id                                AS campaign_id
      , stat.cmp_tp                                AS campaign_type
      , stat.adgrp_id                              AS adgroup_id
      , stat.adgrp_tp                              AS adgroup_type
      , stat.ad_id                                 AS ad_id
      , CASE WHEN criterion_array is null or size(criterion_Array) = 0 THEN 'non-targeting' ELSE  concat_ws(',', SORT_ARRAY(COLLECT_SET(i.dictionary_type ))) END as targeting_type
      , stat.sch_kwd                               AS search_keyword
      , stat.ad_kwd                                AS ad_keyword
      , stat.matching_stage_id                     AS matching_stage_id
      , stat.chnl_id                               AS media_area_unit_id
      , stat.total_rnk                             AS rank
      , NULL                                       AS ba
      , NULL                                       AS qi
      , stat.naver_login_id_hash                   AS nid_no
      , stat.buid                                  AS buid
      , cnv_tp									   AS conversion_type
      , 0                  						   AS impressions
      , 0               						   AS clicks
      , 0                                          AS video_impressions
      , 0                 						   AS video_views
      , 0                						   AS cost
      , sum(CASE WHEN pos = 0 OR pos is null THEN stat.cnv_cnt ELSE 0 END)             			   AS conversions
      , sum(CASE WHEN pos = 0 OR pos is null THEN stat.cnv_amt ELSE 0 END)             			   AS conversion_revenue
    FROM
      cts.t_ncc_cnv_bi_tdly stat LATERAL VIEW OUTER posexplode(criterion_array) pe AS pos, i
    WHERE stat.ymd = '{{ logical_date | basetime | ds }}'
    	AND error_cd=100
		AND cnv_cnt > 0
    GROUP BY
        stat.cust_id
      , stat.biz_chnl_id
      , stat.cmp_id
      , stat.cmp_tp
      , stat.adgrp_id
      , stat.adgrp_tp
      , stat.ad_id
      , stat.sch_kwd
      , stat.ad_kwd
      , stat.matching_stage_id
      , stat.chnl_id
      , stat.total_rnk
      , stat.naver_login_id_hash
      , stat.buid
      , stat.cnv_tp
      , criterion_array

	UNION ALL

    -- 1-3) NCC 실적: 브랜드검색/신제품검색 노출/클릭/매출
    SELECT
        CAST(stat.cust_id AS STRING)               AS platform_customer_id
      , CAST(stat.biz_chnl_id  AS STRING)          AS business_channel_id
      , CONCAT(stat.cmp_tp, ':', stat.adgrp_tp)    AS product_type_id
      , NULL                                       AS campaign_id
      , stat.cmp_tp                                AS campaign_type
      , NULL                                       AS adgroup_id
      , stat.adgrp_tp                              AS adgroup_type
      , NULL                                       AS ad_id
	    , NULL									   AS targeting_type
      , NULL                                       AS search_keyword
      , stat.ad_kwd                                AS ad_keyword
      , NULL                                       AS matching_stage_id
      , stat.chnl_id                               AS media_area_unit_id
      , 1                                          AS rank
      , NULL                                       AS ba
      , NULL                                       AS qi
      , NULL                                       AS nid_no
      , NULL                                       AS buid
      , NULL                                       AS conversion_type
      , 0                                          AS impressions
      , 0                                          AS clicks
      , 0                                          AS video_impressions
      , 0                                          AS video_views
      , SUM(NVL(stat.sales_amt, 0)) / 1.1          AS cost                -- dw.t_brnd_kwd_sales_dly_stat stat 매출은 VAT 포함
      , 0                                          AS conversions
      , 0                                          AS conversion_revenue
    FROM
      dw.t_brnd_kwd_sales_dly_stat stat
    WHERE
      stat.ymd = '{{ logical_date | basetime | ds }}'
    GROUP BY
        stat.cust_id
      , biz_chnl_id
      , stat.cmp_tp
      , stat.adgrp_tp
      , stat.ad_kwd
      , stat.chnl_id
  ) ncc_stat  LEFT JOIN
    meta.t_cust_stat_attr_his cust_stat ON (ncc_stat.platform_customer_id = cust_stat.cust_id AND cust_stat.ymd = '{{ logical_date | basetime | ds }}' )
    LEFT JOIN meta.t_agnt_grp agent_grp ON cust_stat.agnt_id = agent_grp.agnt_grp_id
  GROUP BY
      platform_customer_id
    , business_channel_id
    , cust_stat.agncy_id
    , CASE WHEN agent_grp.is_proxy_agncy IS TRUE THEN cust_stat.agnt_grp_id  ELSE NULL END
    , product_type_id
    , campaign_id
    , campaign_type
    , adgroup_id
    , adgroup_type
    , ad_id
    , search_keyword
    , ad_keyword
    , matching_stage_id
    , media_area_unit_id
    , rank
    , ba
    , qi
	  , COALESCE(targeting_type, 'non-targeting')
    , nid_no
    , buid
    , conversion_type
;
