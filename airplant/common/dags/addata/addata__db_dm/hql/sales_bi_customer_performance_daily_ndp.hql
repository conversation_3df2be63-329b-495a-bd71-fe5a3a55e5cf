USE addata__db_dm{{params.db_postfix}};

-- DDL
CREATE EXTERNAL TABLE IF NOT EXISTS sales_bi_customer_performance_daily (
		--------------------------- ## 광고주 X 대행사 정보 ## ----------------------------
		platform_customer_id    string         comment '[K]플랫폼별 광고주 ID'
		,business_channel_id    string         comment '[K]비즈니스 채널 ID'
		,brand_no               string         comment '[K]브랜드 NO(NCC:NULL)'
		,agency_id              string         comment '[K]대행사 ID'
		,rep_agency_id          string         comment '[K]렙사 ID(NCC:NULL)'
		,proxy_agency_id        string         comment '[K]대대행사 ID'
		--------------------------- ## 상품 X 미디어(지면) ## ----------------------------
		,product_type_id        string         comment '[K]통합 상품유형ID(NCC cmp_tp:adgrp_tp (1:1 파워링크:사이트) composite 형태로 생성, NDP sale_unit_id, GFA adunit_id)'
    ,campaign_id            string         comment '[K]캠페인 ID'
  	,campaign_type          string         comment '캠페인 타입'
   	,adgroup_id             string         comment '[K]광고그룹'
   	,adgroup_type           string         comment '광고그룹 타입'
	  ,ad_id                  string         comment '[K]광고 ID'
 	  ,search_keyword         string         comment '[K]검색 키워드명'
    ,ad_keyword             string         comment '[K]등록 키워드명'
    ,matching_stage_id      string         comment 'matching stage ID'
    ,media_area_unit_id     string         comment '[K]통합 미디어구분 ID(NCC chnl_id, NDP ectn_unit_id, GFA adunit_id)'
    ,rank                   BIGINT         comment '[K]광고 노출 순위'
    ,ba                     string         comment '[K]입찰가'
    ,qi                     string         comment '[K]품질지수'
		,targeting_type_list    string         comment '[K]타게팅유형리스트'
    ,nid_no                 string         comment '[K]네이버 login ID의 hash 값'
    ,buid                   string         comment '[K]사용자구분ID'
	  --------------------------- ## 전환 ## ----------------------------
		,conversion_type        string         comment '[k]전환 유형'
    	--------------------------- ## metric ## ----------------------------
		,impressions            BIGINT         comment '노출수'
		,clicks                 BIGINT         comment '클릭수'
		,video_impressions      BIGINT         comment '비디오 노출수'
		,video_views            BIGINT         comment '비디오 조회수'
		,cost                   DECIMAL(20,2)  comment '비용'
		,conversions            BIGINT         comment '전환수'
		,conversion_revenue     DECIMAL(20,2)  comment '전환매출'
)
COMMENT 'SALES BI 광고주 및 대행사별 일별 실적'
PARTITIONED BY ( ymd string COMMENT '날짜, yyyy-MM-dd', dsp string COMMENT '[K]DSP 플랫폼 유형')
STORED AS ORC
TBLPROPERTIES ("orc.compress"="ZLIB");

-- 파티션 OVERWRITE
-- NDP
LOAD DATA INPATH '{{ params.target_location }}/ymd={{ logical_date | basetime | ds }}/dsp=NDP'
OVERWRITE INTO TABLE sales_bi_customer_performance_daily PARTITION (ymd='{{ logical_date | basetime | ds }}', dsp='NDP');
