import datetime

import pendulum
from airflow import DAG
from airflow.operators.bash import <PERSON><PERSON><PERSON>perator
from airflow.providers.airplant.operators.hive_operator import HiveOperator
from airflow.providers.airplant.sensors.named_hive_partition_sensor import NamedHivePartitionSensor
from airflow.providers.airplant.sensors.treadmill_sensor import TreadmillSensor
from airflow.sensors.time_delta import TimeDeltaSensorAsync
from airflow.utils.task_group import TaskGroup

default_args = {
    'owner': "addata",
    'email': ['<EMAIL>', '<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': datetime.timedelta(seconds=30),
    'retry_exponential_backoff': True,
    'provide_context': True,
}

with DAG(
    dag_id="sales_bi_nx_sa_keyword_daily",
    start_date=datetime.datetime(2024, 1, 1, 0, 0, tzinfo=pendulum.timezone("Asia/Seoul")),
    schedule="@daily",
    tags=["sales-bi"],
    default_args=default_args,
):
    from airflow.models import Variable

    ngela = Variable.get("nameservice_ngela")
    terranova = Variable.get("nameservice_terranova")
    db_postfix = Variable.get("db_postfix").strip()

    start = TimeDeltaSensorAsync(
        task_id="start",
        delta=datetime.timedelta(hours=8),  # 8시간 대기
        timeout=3600 * 9
    )

    with TaskGroup(group_id="wait") as wait:
        """
        treadmill 에서 연관된 meta 테이블 체크
        """
        for job_id, table_name in [(3006, "bi_mart.t_sa_kwd_nx_dly")]:
            TreadmillSensor(
                task_id=f"{table_name.replace('.', '__')}",
                job_id=job_id,
                http_conn_id="treadmill_default",
                mode="reschedule",
                poke_interval=60 * 5, # 5분 마다 체크
                timeout= 60 * 60 * 3, # 3시간 센싱
                queue="addata",
            )

        NamedHivePartitionSensor(
            task_id="fact_wait",
            partition_names=[
                "bi_mart.t_sa_kwd_nx_dly/ymd={{ logical_date | basetime | ds }}"
            ],
            timeout=60 * 60 * 2,  # 2시간 센싱
            poke_interval=60 * 5,  # 5분 마다 체크
            metastore_conn_id="metastore_ngela",
            queue="addata",
        )

    tgt_db = "addata__db_dm"
    tgt_tbl = "sales_bi_nx_sa_keyword_daily"
    append_path = "ymd={{ds}}"
    source_location = f"hdfs://{ngela}//user/addata/export/{tgt_db}.db/{tgt_tbl}"
    target_location = f"hdfs://{terranova}/user/addata/import/{tgt_db}.db/{tgt_tbl}"

    export = HiveOperator(
        task_id="export",
        hql=f"hql/{tgt_tbl}__export.hql",
        hive_cli_conn_id="hivecli_ngela",
        params={"source_location": source_location},
        queue="addata",
    )

    distcp = BashOperator(
        task_id="distcp",
        bash_command=f"c3s-distcp {source_location}/ymd={{{{logical_date | basetime | ds}}}} "
        f"{target_location}/ymd={{{{logical_date | basetime | ds}}}}",
        queue="addata",
        env={"HADOOP_PROXY_USER": "addata", "CLUSTER_NAME": "ngela"},
        append_env=True,
    )

    table = HiveOperator(
        task_id="table",
        hql=f"hql/{tgt_tbl}.hql",
        hive_cli_conn_id="hivecli_terranova_batch",
        params={
            "target_location": target_location,  # 파티션 경로 미포함 경로
            "db_postfix": db_postfix
        },
        queue="addata",
    )

    end = BashOperator(
        task_id="end",
        bash_command=f"hadoop fs -rm -f -r {source_location}/ymd={{{{logical_date | basetime | ds}}}}",
        queue="addata",
        env={"HADOOP_PROXY_USER": "addata", "CLUSTER_NAME": "ngela"},
        append_env=True,
    )

    start >> wait >> export >> distcp >> table >> end
