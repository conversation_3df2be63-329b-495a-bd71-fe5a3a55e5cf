USE addata__db_meta{{params.db_postfix}};

CREATE EXTERNAL TABLE IF NOT EXISTS sales_bi_media_area (
  --------------------------- ## 플랫폼 정보 ## ------------------------
   dsp                       STRING  COMMENT '[K]DSP 플랫폼 유형'
  --------------------------- ## 상품 정보 ## -------------------------
  ,media_area_unit_id        STRING  COMMENT '[K]미디어 영역 유닛 ID (NCC chnl_id, GFP adunit_id, NDP ectn_unit_id)'
  ,media_area_large_id       STRING  COMMENT '미디어 영역 유닛 ID 대분류'
  ,media_area_middle_id      STRING  COMMENT '미디어 영역 유닛 ID 대분류'
  ,media_area_small_id       STRING  COMMENT '미디어 영역 유닛 ID 중분류'
  ,media_area_large_name     STRING  COMMENT '미디어 영역 유닛 ID 소분류'
  ,media_area_middle_name    STRING  COMMENT '미디어 영역 유닛 이름 대분류'
  ,media_area_small_name     STRING  COMMENT '미디어 영역 유닛 이름 중분류'
  ,media_area_unit_name      STRING  COMMENT '미디어 영역 유닛 이름 소분류'
  ,device_type               STRING  COMMENT '디바이스 타입(PC/모바일)'
  ,is_naver_media            BOOLEAN COMMENT '네이버 미디어 여부'
)
COMMENT '미디어 영역 통합 테이블'
STORED AS ORC
TBLPROPERTIES (
  "external.table.purge"="true",
  "orc.compress"="ZLIB"
);

LOAD DATA INPATH '{{ params.target_location }}/ymd={{logical_date | basetime | ds}}' OVERWRITE INTO TABLE sales_bi_media_area;
