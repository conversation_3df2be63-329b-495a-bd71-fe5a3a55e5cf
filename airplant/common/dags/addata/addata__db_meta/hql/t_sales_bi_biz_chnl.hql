USE addata__db_meta;

/* DDL
CREATE EXTERNAL TABLE IF NOT EXISTS sales_bi_business_channel (
        dsp                   string   comment '[K] DSP 플랫폼 유형'
		  	,business_channel_id 	string	comment '[K] 업종 구분, URL type 구분을 위한 광고 엔티티 NCC biz_chnl_id, GFA  biz_chnl_id, NDP ac_id'
		    ,business_channel_type 	string	comment '비즈채널 유형 NCC biz_chnl_tp, GFA channel_type, NDP 소재'
		    ,business_channel_key 	string	comment '비즈채널키 NCC 채널키(url), GFA url, NDP url'
		    ,business_channel_name 	string	comment 'NCC 채널키(url), GFA url, NDP 브랜드명'
		    ,url_type 				string	comment '비즈채널정보 기반의 URL 분류(브랜드스토어/스마트스토어/쇼핑라이브/.../아웃링크/기타랜딩)'
		    ,platform_customer_id string         comment '[k]플랫폼별 광고주 ID'
        ,cross_biz_large_category_name     string   comment '통합업종 대분류명'
        ,cross_biz_middle_category_name    string   comment '통합업종 중분류명'
        ,cross_biz_full_category_name  	   string   comment '통합업종 전체명'
)
COMMENT '업종 구분 DSP 통합 비즈채널 테이블'
STORED AS ORC
TBLPROPERTIES (
  "external.table.purge"="true",
  "orc.compress"="ZLIB"
); */

LOAD DATA INPATH '{{ params.target_location }}/ymd={{logical_date | basetime | ds}}' OVERWRITE INTO TABLE addata__db_meta.sales_bi_business_channel;
