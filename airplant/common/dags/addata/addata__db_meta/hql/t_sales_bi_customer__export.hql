SET hive.exec.orc.default.compress=ZLIB;

-- NCC 광고주
INSERT OVERWRITE DIRECTORY "{{ params.source_location  }}/ymd={{ logical_date | basetime | ds }}"
STORED AS ORC
SELECT
  'NCC' AS platform_type
, CASE WHEN LENGTH(REGEXP_REPLACE(tax_id, '-', '')) > 0 AND tax_id!='000-00-00000' THEN REGEXP_REPLACE(tax_id, '-', '') ELSE cust_id END AS representative_id -- 1순위: 사업자번호, 2순위: 광고주id
, cust_id AS customer_id -- 광고주 id, login_id는 개인정보 이슈 있음
, cust_co_nm AS customer_name -- 광고주 이름은 개인 이름인 경우가 있어서 사업자(회사) 이름으로 함
, tax_id AS customer_co_tax_num -- 광고주의 사업자번호
, CASE WHEN LENGTH(REGEXP_REPLACE(tax_id, '-', '')) > 0 AND tax_id!='000-00-00000' THEN '사업자번호' ELSE '광고주계정' END AS representative_type
, CAST(TO_DATE(reg_ymdt) as string) AS registration_date
--, cust_co_nm AS brand_name -- NCC의 경우 브랜드가 따로 없기 때문에 사업자명으로 대체함
FROM
  meta.t_cust customer


-- NDP 광고주: - 캠페인 테이블에서 전체 광고주 리스트 추출
UNION ALL
SELECT
    'NDP' AS platform_type
  , CASE WHEN LENGTH(REGEXP_REPLACE(customer.corpreg_no, '-', '')) > 0 AND customer.corpreg_no!='000-00-00000' THEN REGEXP_REPLACE(customer.corpreg_no, '-', '') ELSE corp_id END AS representative_id -- 1순위: 사업자번호, 2순위: 광고주id
  , customer.corp_id AS customer_id
  , customer.corp_nm AS customer_nm
  , CONCAT(SUBSTR(customer.corpreg_no,1,3),'-',SUBSTR(customer.corpreg_no,4,2),'-',SUBSTR(customer.corpreg_no,7,5)) AS corp_co_tax_num
  , CASE WHEN LENGTH(REGEXP_REPLACE(customer.corpreg_no, '-', '')) > 0 AND customer.corpreg_no!='000-00-00000' THEN '사업자번호' ELSE '광고주계정' END AS representative_type
  , cast(TO_DATE(CONCAT(substr(customer.reg_dt, 1, 4),'-',substr(customer.reg_dt, 5, 2),'-', substr(customer.reg_dt, 7, 2))) as string) AS registration_date
FROM
(
  SELECT sponsor_id FROM da_meta.t_dim_campaign GROUP BY sponsor_id
) camp_corp_list
JOIN da_meta.t_dim_corporation customer ON camp_corp_list.sponsor_id = customer.corp_id


-- GFA 광고주
UNION ALL
SELECT
  'GFA' AS platform_type
  , CASE WHEN corp_type != '국내개인(비사업자)' THEN REGEXP_REPLACE(customer.corpreg_no,'-','') ELSE customer.ad_account_no END AS representative_id -- 1순위: 사업자번호, 2순위: 광고주id
  , cast(customer.ad_account_no as string) AS customer_id
  , customer.corp_name AS customer_name
  , CASE WHEN corp_type != '국내개인(비사업자)' THEN customer.corpreg_no ELSE '-' END AS customer_co_tax_num
  , CASE WHEN LENGTH(REGEXP_REPLACE(customer.corpreg_no, '-', '')) > 0 AND customer.corpreg_no!='000-00-00000' THEN '사업자번호' ELSE '광고주계정' END AS representative_type
  , cast(TO_DATE(CONCAT(substr(customer.reg_date, 1, 4),'-',substr(customer.reg_date, 5, 2),'-', substr(customer.reg_date, 7, 2))) as string) AS registration_date
FROM
  gfa.ad_account customer
WHERE
  ymd = '{{logical_date | basetime | ds}}'

-- CARD 광고주
UNION ALL
SELECT
  'CARD'    AS platform_type
  , customer.crd_card_co_tax_id    AS representative_id
  , customer.crd_card_company_no    AS customer_id
  , customer.crd_card_company_nm    AS customer_name
  , customer.crd_card_co_tax_id    AS customer_co_tax_num
  , CASE WHEN LENGTH(REGEXP_REPLACE(customer.crd_card_co_tax_id, '-', '')) > 0 THEN '사업자번호' ELSE '광고주계정' END    AS representative_type
  , ''    AS registration_date
FROM
  meta.t_crd_card_company customer

-- SHOPPING 광고주
UNION ALL
SELECT
    'SHOPPING'    AS platform_type
  , CASE WHEN tax_num IS NULL OR length(tax_num) = 0 THEN mall_seq ELSE tax_num END    AS representative_id
  , mall_seq    AS customer_id
  , mall_nm    AS customer_name
  , CASE WHEN length(tax_num ) >= 10 THEN concat(substr(tax_num,1,3),'-',substr(tax_num,4,2),'-',substr(tax_num,6)) ELSE '' END    AS customer_co_tax_num
  , CASE WHEN length(tax_num ) >= 10 THEN '사업자번호' ELSE '광고주계정' END    AS representative_type
  , '' AS    registration_date
FROM
  meta.t_shop_mall_info
;
