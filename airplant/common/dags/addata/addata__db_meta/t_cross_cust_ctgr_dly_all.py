import datetime

import pendulum
from airflow import DAG
from airflow.operators.bash import BashOperator
from airflow.operators.empty import EmptyOperator
from airflow.providers.airplant.operators.hive import HiveOperator
from airflow.sensors.time_delta import TimeDeltaSensorAsync

with DAG(
    dag_id="t_cross_cust_ctgr_dly_all",
    start_date=datetime.datetime(2025, 1, 1, 0, 0, tzinfo=pendulum.timezone("Asia/Seoul")),
    schedule="@daily",
    tags=["sales-bi", "mmm"],
    max_active_runs=1,  # 동시에 하나의 DAG만 실행
    catchup=False,  # 지난 스케줄을 건너뛰고 마지막 스케줄만 실행
):
    start = TimeDeltaSensorAsync(
        task_id="start",
        delta=datetime.timedelta(hours=6),  # 6시간 대기
        timeout=3600 * 7
    )

    src_db = "meta"
    src_tbl = "t_cross_cust_ctgr_dly_all"
    tgt_db = "addata__db_meta"
    tgt_tbl = "t_cross_cust_ctgr_dly_all"
    source = (
        f"hdfs://{{{{var.value.nameservice_ngela}}}}/warehouse/tablespace/external/hive/{src_db}.db/{src_tbl}"
    )
    target = f"hdfs://{{{{var.value.nameservice_terranova}}}}/user/addata/warehouse/{tgt_db}.db/{tgt_tbl}"
    distcp = BashOperator(
        task_id="distcp",
        bash_command=f"c3s-distcp {source} {target}",
        queue="addata",
        env={"HADOOP_PROXY_USER": "addata", "CLUSTER_NAME": "ngela"},
        append_env=True,
    )
    table = HiveOperator(
        task_id="table",
        hql=f"hql/{tgt_tbl}.hql",
        hiveconf_jinja_translate=True,
        hive_cli_conn_id="hivecli_terranova_batch",
        queue="addata",
    )

    end = EmptyOperator(task_id="end")

    start >> distcp >> table >> end
