import datetime

import pendulum
from airflow import DAG
from airflow.operators.bash import Ba<PERSON>Operator
from airflow.providers.airplant.operators.hive import HiveOperator
from airflow.providers.airplant.sensors.treadmill import TreadmillSensor
from airflow.sensors.time_delta import TimeDeltaSensorAsync
from airflow.utils.task_group import TaskGroup

default_args = {
    "owner": "addata",
    "email": ["<EMAIL>", "<EMAIL>"],
    "email_on_failure": True,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": datetime.timedelta(seconds=60),
    "retry_exponential_backoff": True,
    "provide_context": True,
}

with DAG(
    dag_id="t_sales_bi_business_channel",
    start_date=datetime.datetime(2025, 3, 24, 0, 0, tzinfo=pendulum.timezone("Asia/Seoul")),
    schedule="@daily",
    tags=["sales-bi"],
    default_args=default_args,
):
    from airflow.models import Variable

    ngela = Variable.get("nameservice_ngela")
    terranova = Variable.get("nameservice_terranova")
    db_postfix = Variable.get("db_postfix").strip()

    start = TimeDeltaSensorAsync(
        task_id="start",
        delta=datetime.timedelta(hours=6),  # 6시간 대기
        timeout=3600 * 7
    )

    with TaskGroup(group_id="wait") as wait:
        """
        treadmill 에서 연관된 meta 테이블 체크
        //todo: gfa.manager_account 의 의존성은 어떻게 걸지?
        """
        for job_id, table_name in [
            (129, "meta.t_biz_chnl"),
            (2837, "da_meta.t_dim_creative"),
        ]:
            TreadmillSensor(
                task_id=f"{table_name.replace('.', '__').replace(',', '___')}",
                job_id=job_id,
                http_conn_id="treadmill_default",
                mode="reschedule",
                poke_interval=60 * 60,
                timeout=12 * 60 * 60,
                queue="addata",
            )

    tgt_db = "addata__db_meta"
    tgt_tbl = "sales_bi_business_channel"

    source_location = f"hdfs://{ngela}//user/addata/export/{tgt_db}.db/{tgt_tbl}"
    target_location = f"hdfs://{terranova}/user/addata/import/{tgt_db}.db/{tgt_tbl}"

    export = HiveOperator(
        task_id="export",
        hql=f"hql/{tgt_tbl}__export.hql",
        hive_cli_conn_id="hivecli_ngela",
        params={"source_location": source_location},
        queue="addata",
    )

    distcp = BashOperator(
        task_id="distcp",
        bash_command=f"c3s-distcp {source_location}/ymd={{{{logical_date | basetime | ds}}}} "
        f"{target_location}/ymd={{{{logical_date | basetime | ds}}}}",
        queue="addata",
        env={"HADOOP_PROXY_USER": "addata", "CLUSTER_NAME": "ngela"},
        append_env=True,
    )

    table = HiveOperator(
        task_id="table",
        hql=f"hql/{tgt_tbl}.hql",
        hive_cli_conn_id="hivecli_terranova_batch",
        params={
            "target_location": target_location,  # 파티션 경로 미포함 경로
            "db_postfix": db_postfix
        },
        queue="addata",
    )

    end = BashOperator(
        task_id="end",
        bash_command=f"hadoop fs -rm -f -r {source_location}/ymd={{{{logical_date | basetime | ds}}}}",
        queue="addata",
        env={"HADOOP_PROXY_USER": "addata", "CLUSTER_NAME": "ngela"},
        append_env=True,
    )

    start >> wait >> export >> distcp >> table >> end
