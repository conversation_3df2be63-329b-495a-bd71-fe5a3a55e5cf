{{
    config(
        alias='test_asset_suggestion_provisioning_result',
        materialized='incremental',
        partition_by=['ymd'],
        incremental_strategy='insert_overwrite'
    )
}}

{% set ymd_value = get_ymd_value() %}

SELECT
    get_json_object(message, '$.eventType') AS event_type,
    get_json_object(message, '$.sessionId') AS session_id,
    get_json_object(message, '$.requestId') AS request_id,
    CAST(get_json_object(message, '$.customerId') AS INT) AS customer_id,
    get_json_object(message, '$.campaignId') AS campaign_id,
    get_json_object(message, '$.adgroupId') AS adgroup_id,
    CAST(get_json_object(message, '$.linkType') AS INT) AS link_type,
    get_json_object(message, '$.text') AS text,
    get_json_object(message, '$.uniqueId') AS unique_id,
    CAST(get_json_object(message, '$.providedYn') AS INT) AS provided_yn,
    CAST('{{ ymd_value }}' AS STRING) AS ymd
FROM
    {{ source('rsa', 'asset_suggestion_raw_kafka') }}
WHERE
    `__timestamp` >=  1000 * unix_timestamp('{{ ymd_value }} 00:00:00', 'yyyy-MM-dd HH:mm:ss')
    and `__timestamp` <  1000 * (unix_timestamp('{{ ymd_value }} 23:59:59', 'yyyy-MM-dd HH:mm:ss') + 1)
    and GET_JSON_OBJECT(message, '$.eventType') = "ASSET_SUGGESTION_PROVISIONING_RESULT"
