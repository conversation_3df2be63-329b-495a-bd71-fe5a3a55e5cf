-- update kwdattr.t_adscore
INSERT OVERWRITE TABLE kwdattr.t_adscore PARTITION(ymd='{{ logical_date | basetime | ds }}')
SELECT
  sch_kwd,
  ad_sales_amt_30d AS ad_sales_amt,
  collection_clk_cnt_30d AS collection_clk_cnt,
  CASE
  WHEN (ad_sales_amt_12m > 10000) THEN ROUND((ad_sales_amt_30d + 2500) / (collection_clk_cnt_30d + 100), 2)  -- 연 매출 1만원 초과
    ELSE ROUND((ad_sales_amt_30d + 100) / (collection_clk_cnt_30d + 100), 2)  -- 연 매출 1만원 이하
  END AS ad_score
FROM (
  SELECT
    sch_kwd,
    SUM(mly_ad_sales_amt) AS ad_sales_amt_12m,
    SUM(dly_ad_sales_amt) AS ad_sales_amt_30d,
    SUM(dly_collection_clk_cnt) AS collection_clk_cnt_30d
  FROM (
    SELECT
      sch_kwd,
      ad_sales_amt AS mly_ad_sales_amt,
      0 AS dly_ad_sales_amt,
      0 AS dly_collection_clk_cnt
    FROM kwdattr.t_sch_kwd_sales_mly_stat
    WHERE ymd BETWEEN ADD_MONTHS(TRUNC('{{ logical_date | basetime | yyyy }}-{{ logical_date | basetime | mm }}-01', 'MM'), -12)
          AND ADD_MONTHS(TRUNC('{{ logical_date | basetime | yyyy }}-{{ logical_date | basetime | mm }}-01', 'MM'), -1)  -- 기준일 직전 12개월 기간 동안의 매출

    UNION ALL

    SELECT
      sch_kwd,
      0 AS mly_ad_sales_amt,
      sales_amt AS dly_ad_sales_amt,
      0 AS dly_collection_clk_cnt
    FROM primitive.t_sa_dly_stat
    WHERE data_ctgr = 'ad'
      AND ymd BETWEEN DATE_SUB('{{ logical_date | basetime | ds }}', 29) AND '{{ logical_date | basetime | ds }}'  -- 기준일 포함 이전 30일 기간 동안의 매출
      AND cmp_tp = '1'
      AND chnl_id IN ('8793', '237596', '4124060', '4124069')
      AND sales_amt > 0

    UNION ALL

    SELECT
      kwd AS sch_kwd,  -- 공백이 포함된 sch_kwd가 아닌 공백 제거된 kwd 기준으로 집계
      0 AS mly_ad_sales_amt,
      0 AS dly_ad_sales_amt,
      clk_cnt AS dly_collection_clk_cnt
    FROM dw.t_sch_collection_allkwd_stat
    WHERE ymd BETWEEN DATE_SUB('{{ logical_date | basetime | ds }}', 29) AND '{{ logical_date | basetime | ds }}'  -- 기준일 포함 이전 30일 기간 동안의 클릭수
      AND collection IN ('kdc', 'kin', 'rvw', 'ink', 'web', 'itb', 'ksn')
      AND clk_cnt > 0
  ) t1
  GROUP BY sch_kwd
  HAVING SUM(dly_ad_sales_amt) > 0
) t2
