{{
  config(
    materialized = 'incremental',
    partition_by=['ymd'],
    incremental_strategy='insert_overwrite'
  )
}}

{% set ymd_value = get_ymd_value() %}

WITH imp_data AS (
  SELECT
    NVL(event_type, '2')                              AS event_tp
    ,is_valid
    ,ymd
  FROM {{ source('log', 't_ncc_imp') }}
  WHERE ymd = '{{ ymd_value }}'
), clk_data AS (
  SELECT
    NVL(event_type, '1')                              AS event_tp
    ,is_valid
    ,ymd
  FROM {{ source('log', 't_ncc_clk') }}
  WHERE ymd = '{{ ymd_value }}'
)

SELECT
  event_tp
  ,sum(case when is_valid  = '1' then 1 else 0 end)   AS valid_cnt
  ,sum(case when is_valid <> '1' then 1 else 0 end)   AS invalid_cnt
  ,ymd
FROM (
  SELECT * FROM imp_data
  UNION ALL
  SELECT * FROM clk_data
) AS all_aggregated
GROUP BY event_tp, ymd
