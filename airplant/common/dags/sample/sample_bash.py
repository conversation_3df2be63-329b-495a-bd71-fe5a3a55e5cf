import datetime

import pendulum
from airflow import DAG
from airflow.operators.bash import Bash<PERSON>perator
from airflow.operators.empty import EmptyOperator

with DAG(
    dag_id="sample_bash",
    start_date=datetime.datetime(2024, 5, 1, tzinfo=pendulum.timezone("Asia/Seoul")),
    schedule=None,
    tags=["sample", "BashOperator", "EmptyOperator"],
):
    """
    일 단위 스케쥴 기본
    """
    start = EmptyOperator(task_id="start")

    run_hostname_kubernetes = BashOperator(
        task_id="host_name", bash_command="hostname", queue="addata"
    )

    end = EmptyOperator(task_id="end")

    start >> [run_hostname_kubernetes] >> end
