#!/bin/bash

source ~/.bashrc

sparkling_home=$(pwd)
aggregator="com.navercorp.gfp.biz.abtest.ABTestDailyAggregator"

dateTime=$1
execTime=$(date '+%Y-%m-%dT%H:%M:%S.%3N')

echo sparkling_home=${sparkling_home}
echo dateTime=${dateTime}
echo aggregator=${aggregator}
echo execTime=${execTime}

spark-submit --class ${aggregator} \
	--name ${aggregator}-${dateTime}-${execTime} \
	--master yarn --deploy-mode cluster \
	--queue biz_gep \
	--num-executors 1 \
	--executor-cores 5 \
	--executor-memory 512m \
	--conf spark.hadoop.mapred.output.compress=true \
	--conf spark.hadoop.mapred.output.compression.codec=org.apache.hadoop.io.compress.GzipCodec \
	--conf "spark.driver.extraJavaOptions=-Dhdp.version=*******-78 -XX:+UseG1GC -Dfile.encoding=utf-8" \
	--conf "spark.executor.extraJavaOptions=-XX:+UseG1GC" \
	--conf spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922 \
	--conf spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro \
	--conf spark.hadoop.dfs.nameservices=bizcloud \
	--conf spark.kerberos.access.hadoopFileSystems=hdfs://bizcloud \
	--conf spark.kerberos.keytab=/home1/irteam/apps/c3/gfp-data.keytab \
	--conf spark.kerberos.principal=<EMAIL> \
	--conf spark.serializer=org.apache.spark.serializer.KryoSerializer \
	--conf spark.sql.caseSensitive=true \
	--conf spark.sql.parquet.mergeSchema=true \
	--conf spark.sql.warehouse.dir=hdfs://bizcloud/user/gfp-data/apps/spark/warehouse \
	--conf spark.eventLog.enabled=true \
	--conf spark.eventLog.dir=hdfs://bizcloud/user/gfp-data/spark-history/ \
	--conf spark.yarn.am.extraJavaOptions=-Dhdp.version=*******-78 \
	--conf spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922 \
	--conf spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro \
	--conf spark.yarn.archive=hdfs://bizcloud/user/gfp-data/apps/spark/3.2.1/spark3.2.1_jars.tar.gz \
	${sparkling_home}/jar/sparkling-s.jar ${dateTime} "${@:2}"
