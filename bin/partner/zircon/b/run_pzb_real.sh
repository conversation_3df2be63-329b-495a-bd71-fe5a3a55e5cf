#!/bin/bash

# 블럭 사이즈 확인
# hdfs fsck /user/gfp-data/partner/zircon/b/2025/06/01/20250601.orc -files -blocks

# 실행 스크립트
# ./partner/zircon/b/run_pzb_real.sh /home1/irteam/projects/data-hub/sparkling-s com.navercorp.gfp.biz.partner.zircon.b.PartnerZirconBCompactor 20250601

sparkling_home=$1
aggregator=$2
date=$3
exec_time=$(date '+%Y-%m-%dT%H:%M:%S.%3N')

echo sparkling_home="${sparkling_home}"
echo aggregator="${aggregator}"
echo date="${date}"
echo exec_time="${exec_time}"

# 셔플 최적화
# --conf spark.sql.adaptive.shuffle.targetPostShuffleInputSize=134217728 \
#--conf spark.sql.adaptive.coalescePartitions.initialPartitionNum=8 \
#--conf spark.sql.adaptive.shuffle.targetPostShuffleInputSize=134217728 \
#	--conf spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version=2 \
#	--conf spark.sql.files.maxPartitionBytes=128mb \

spark-submit --class ${aggregator} \
	--name "${aggregator}"-"${date}"-"${exec_time}" \
	--executor-memory 6g \
	--num-executors 4 \
	--executor-cores 4 \
	--driver-memory 4g \
	--conf spark.executor.extraJavaOptions="-XX:+UseG1GC -XX:G1HeapRegionSize=16m -XX:MaxGCPauseMillis=200" \
	--conf spark.executor.memoryOverhead=2g \
    --conf "spark.driver.extraJavaOptions=-Dhdp.version=*******-78 -XX:+UseG1GC -XX:G1HeapRegionSize=16m -Dfile.encoding=utf-8" \
	--conf spark.driver.maxResultSize=2g  \
	--conf spark.sql.files.maxPartitionBytes=134217728 \
	--conf spark.sql.files.openCostInBytes=4194304 \
	--conf spark.sql.parquet.columnarReaderBatchSize=2048 \
	--conf spark.sql.sources.parallelPartitionDiscovery.threshold=16  \
    --conf spark.sql.sources.parallelPartitionDiscovery.parallelism=4  \
    --conf spark.sql.adaptive.enabled=true \
    --conf spark.sql.adaptive.coalescePartitions.enabled=true \
    --conf spark.sql.adaptive.shuffle.targetPostShuffleInputSize=134217728 \
    --conf spark.sql.shuffle.partitions=120 \
    --deploy-mode cluster \
    --master yarn \
    --queue biz_gep \
    --conf spark.eventLog.dir=hdfs://pgcm/user/gfp-data/spark-history/ \
    --conf spark.eventLog.enabled=true \
    --conf spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922 \
    --conf spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro \
    --conf spark.hadoop.dfs.nameservices=pgcm,pg01,pg07 \
    --conf spark.kerberos.access.hadoopFileSystems=hdfs://pgcm,hdfs://pg01,hdfs://pg07 \
    --conf spark.kerberos.keytab=/home1/irteam/apps/c3/gfp-data.keytab \
    --conf spark.kerberos.principal=<EMAIL> \
    --conf spark.serializer=org.apache.spark.serializer.KryoSerializer \
    --conf spark.sql.caseSensitive=true \
    --conf spark.sql.parquet.mergeSchema=false \
    --conf spark.sql.warehouse.dir=hdfs://pg07/user/gfp-data/apps/spark/warehouse \
    --conf spark.yarn.am.extraJavaOptions=-Dhdp.version=*******-78 \
    --conf spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro \
    --conf spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922 \
    --conf spark.yarn.archive=hdfs://pg07/user/gfp-data/apps/spark/3.2.4/spark3.2.4_jars.tar.gz \
    --conf spark.yarn.submit.waitAppCompletion=true \
    --conf spark.yarn.historyServer.address=https://gfp-data--spark-history-server--shs--18080.proxy-pan.c3s.navercorp.com \
	"${sparkling_home}"/jar/sparkling-s.jar "${date}"
