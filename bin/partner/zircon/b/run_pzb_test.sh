#!/bin/bash

# [ test ]
# 네이버서비스		5b74d94bc36eef272090ca56
# SNOW				5f61e7b0c5adac0018e2b5c1
# BILLING_TEST		6294ded6088af6001da86ef2
# Google GMA		5bb6cf1277bd856e489cd132

# [ real ]
# 네이버서비스		5b8f669428b373001fe56ea8
# 퍼스널커뮤니티	5bfd00d166de9300257b0bcb
# 오픈커뮤니티		5d9ef168e933ef00170334a1
# SNOW 				5f5f0901a4ad0b0031bd4cbf
# 네이버TV			5f07c40db85575001dd194aa

# 블럭 사이즈 확인
# hdfs fsck /user/gfp-data/silver/er-ssp-client/20240115-22/publisherId=5f61e7b0c5adac0018e2b5c1 -files -blocks

# 실행 스크립트
# ./partner/zircon/b/run_pzb_test.sh /home1/irteam/projects/data-hub/sparkling-s com.navercorp.gfp.biz.partner.zircon.b.PartnerZirconBCompactor 20250601
#
# 3GB 데이터 처리 최적화 적용:
# - 메모리 증설 및 GC 튜닝
# - 파티션 수 최적화
# - ORC 압축 설정

sparkling_home=$1
aggregator=$2
date=$3
exec_time=$(date '+%Y-%m-%dT%H:%M:%S.%3N')

echo sparkling_home="${sparkling_home}"
echo aggregator="${aggregator}"
echo date="${date}"
echo exec_time="${exec_time}"

spark-submit --class ${aggregator} \
	--name "${aggregator}"-"${date}"-"${exec_time}" \
	--executor-memory 6g \
	--num-executors 4 \
	--executor-cores 2 \
	--driver-memory 4g \
	--conf spark.sql.shuffle.partitions=8 \
	--deploy-mode cluster \
	--master yarn \
	--queue biz_gep \
	--conf spark.eventLog.dir=hdfs://bizcloud/user/gfp-data/spark-history/ \
	--conf spark.eventLog.enabled=true \
	--conf spark.executor.extraJavaOptions="-XX:+UseG1GC -XX:G1HeapRegionSize=16m -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions -XX:+UseG1GC -XX:+ParallelRefProcEnabled" \
	--conf spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922 \
	--conf spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro \
	--conf "spark.driver.extraJavaOptions=-Dhdp.version=*******-78 -XX:+UseG1GC -XX:G1HeapRegionSize=16m -XX:MaxGCPauseMillis=200 -Dfile.encoding=utf-8" \
	--conf spark.hadoop.dfs.nameservices=bizcloud \
	--conf spark.kerberos.access.hadoopFileSystems=hdfs://bizcloud \
	--conf spark.kerberos.keytab=/home1/irteam/apps/c3/gfp-data.keytab \
	--conf spark.kerberos.principal=<EMAIL> \
	--conf spark.serializer=org.apache.spark.serializer.KryoSerializer \
	--conf spark.sql.caseSensitive=true \
	--conf spark.sql.parquet.mergeSchema=false \
	--conf spark.sql.warehouse.dir=hdfs://bizcloud/user/gfp-data/apps/spark/warehouse \
	--conf spark.sql.adaptive.enabled=true \
	--conf spark.sql.adaptive.coalescePartitions.enabled=true \
	--conf spark.sql.adaptive.coalescePartitions.minPartitionNum=1 \
	--conf spark.sql.adaptive.coalescePartitions.initialPartitionNum=8 \
	--conf spark.sql.adaptive.shuffle.targetPostShuffleInputSize=134217728 \
	--conf spark.sql.execution.arrow.pyspark.enabled=false \
	--conf spark.executor.memoryFraction=0.8 \
	--conf spark.executor.memoryStorageLevel=MEMORY_AND_DISK_SER \
	--conf spark.sql.files.maxPartitionBytes=134217728 \
	--conf spark.sql.files.openCostInBytes=4194304 \
	--conf spark.sql.parquet.columnarReaderBatchSize=2048 \
	--conf spark.yarn.am.extraJavaOptions=-Dhdp.version=*******-78 \
	--conf spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro \
	--conf spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922 \
	--conf spark.yarn.archive=hdfs://bizcloud/user/gfp-data/apps/spark/3.2.4/spark3.2.4_jars.tar.gz \
	--conf spark.yarn.submit.waitAppCompletion=true \
	--conf spark.yarn.historyServer.address=http://spark3-his.nam-batch.svc.ad1.io.navercorp.com:18080 \
	"${sparkling_home}"/jar/sparkling-s.jar "${date}"
