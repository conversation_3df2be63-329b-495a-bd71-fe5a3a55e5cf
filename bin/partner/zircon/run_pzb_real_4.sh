#!/bin/bash

# 블럭 사이즈 확인
# hdfs fsck /user/gfp-data/partner/zircon/b/2025/06/01/20250601.orc -files -blocks

# 실행 스크립트
# ./partner/zircon/b/run_pzb_real.sh /home1/irteam/projects/data-hub/sparkling-s com.navercorp.gfp.biz.partner.zircon.b.PartnerZirconBCompactor 20250601

sparkling_home=$1
aggregator=$2
date=$3
exec_time=$(date '+%Y-%m-%dT%H:%M:%S.%3N')

echo sparkling_home="${sparkling_home}"
echo aggregator="${aggregator}"
echo date="${date}"
echo exec_time="${exec_time}"

# 셔플 최적화
# --conf spark.sql.adaptive.enabled=true \
# --conf spark.sql.adaptive.coalescePartitions.enabled=true \
# --conf spark.sql.adaptive.shuffle.targetPostShuffleInputSize=134217728 \
#	--conf spark.sql.files.maxPartitionBytes=134217728 \
#	--conf spark.sql.files.openCostInBytes=4194304 \
#	--conf spark.driver.maxResultSize=2g \

#	--conf spark.sql.sources.parallelPartitionDiscovery.threshold=16\
# 병렬 스캔 시작 임계값. 디폴트 32개(32개 이하는 순차 스캔, 32개를 초과하면 병렬 스캔)
# 날짜 경로 밑에 시간 단위로 파티션 되어 있으므로 24개에 _COMPACTION_SUCCESS까지 25개이므로
# 병렬처리할 수 있도록 16개로 설정하여 병렬처리하게 함.

# --conf spark.sql.sources.parallelPartitionDiscovery.parallelism=4 \
# 병렬 스캔 시 스레드를 몇개로 할 것인지. 디폴트 없음(CPU 코어 수만큼 스레드 생성 (예: 16 core = 16 스레드))
# 24개의 경로를 4개 스레드로 처리하라고 했으므로
# 기본값을 쓰면 24개의 경로를 32개의 스레드로 처리하려고 하므로 32개 스레드 x 각각 메타데이터 = 높은 메모리 사용으로 이어짐
# 해서 이를 4개로 줄여서 아래와 같이 처리
# 	Thread 1: 경로 1-7    (7개)
# 	Thread 2: 경로 8-14   (7개)
# 	Thread 3: 경로 15-21  (7개)
# 	Thread 4: 경로 22-25  (4개)
# 	→ 메모리: 4개 스레드 × 각각 메타데이터 = 제어된 메모리 사용


# --conf spark.sql.parquet.columnarReaderBatchSize=2048
# 디폴트 4096 rows -> 2048 rows로 50% 감소로 메모리 사용량 절약


# Spark 리소스 설정
spark-submit --class ${aggregator} \
	--name "${aggregator}"-"${date}"-"${exec_time}" \
	--executor-memory 6g \
	--num-executors 3 \
	--executor-cores 2 \
	--driver-memory 3g \
	--conf spark.executor.extraJavaOptions="-XX:+UseG1GC -XX:G1HeapRegionSize=8m -XX:MaxGCPauseMillis=100" \
	--conf spark.executor.memoryOverhead=1g \
    --conf "spark.driver.extraJavaOptions=-Dhdp.version=*******-78 -XX:+UseG1GC -XX:G1HeapRegionSize=8m -XX:MaxGCPauseMillis=100 -Dfile.encoding=utf-8" \
	--conf spark.driver.maxResultSize=2g \
	--conf spark.sql.parquet.columnarReaderBatchSize=4096 \
	--conf spark.sql.sources.parallelPartitionDiscovery.threshold=16 \
    --conf spark.sql.sources.parallelPartitionDiscovery.parallelism=4 \
    --conf spark.sql.shuffle.partitions=24 \
    --deploy-mode cluster \
    --master yarn \
    --queue biz_gep \
    --conf spark.eventLog.dir=hdfs://pgcm/user/gfp-data/spark-history/ \
    --conf spark.eventLog.enabled=true \
    --conf spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922 \
    --conf spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro \
    --conf spark.hadoop.dfs.nameservices=pgcm,pg01,pg07 \
    --conf spark.kerberos.access.hadoopFileSystems=hdfs://pgcm,hdfs://pg01,hdfs://pg07 \
    --conf spark.kerberos.keytab=/home1/irteam/apps/c3/gfp-data.keytab \
    --conf spark.kerberos.principal=<EMAIL> \
    --conf spark.serializer=org.apache.spark.serializer.KryoSerializer \
    --conf spark.sql.caseSensitive=true \
    --conf spark.sql.parquet.mergeSchema=false \
    --conf spark.sql.warehouse.dir=hdfs://pg07/user/gfp-data/apps/spark/warehouse \
    --conf spark.yarn.am.extraJavaOptions=-Dhdp.version=*******-78 \
    --conf spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro \
    --conf spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922 \
    --conf spark.yarn.archive=hdfs://pg07/user/gfp-data/apps/spark/3.2.4/spark3.2.4_jars.tar.gz \
    --conf spark.yarn.submit.waitAppCompletion=true \
    --conf spark.yarn.historyServer.address=https://gfp-data--spark-history-server--shs--18080.proxy-pan.c3s.navercorp.com \
	"${sparkling_home}"/jar/sparkling-s.jar "${date}"
