"""
- 시작 ~ 종료 날짜를 주고 Zircon B GFP를 적재/컴팩션하는 스크립트
- 결과는 ./logs/zbgfp에 생성됨
"""
import os.path
import subprocess
from datetime import datetime

import pendulum

import manual_job_dao

_LOG_PREFIX = '[RUN-ZBGFP]'
_DEFAULT_TZ = pendulum.timezone('Asia/Seoul')
_KIND = 'manual'  # 정규 생성

BRANCH = 'ssp-branch'

# [ real ]
# 네이버서비스		5b8f669428b373001fe56ea8
# 퍼스널커뮤니티	5bfd00d166de9300257b0bcb
# 오픈커뮤니티		5d9ef168e933ef00170334a1
# SNOW 				5f5f0901a4ad0b0031bd4cbf
# 네이버TV			5f07c40db85575001dd194aa
# LINEWEBTOON		5d11dc1a34480e001d31fb26

pubId = '5b8f669428b373001fe56ea8'

# Zircon B GFP 생성을 원하는 기간 설정
start_date = pendulum.date(2024, 7, 31)
end_date = pendulum.date(2024, 7, 31)


def _run_spark_zircon_b_gfp_in_sequentially():
	"""
	날짜별/매체별 Zircon B 스파크 트리거
	:param context:
	:return:
	"""
	current_date = start_date
	while current_date <= end_date:
		ymd = current_date.format('YYYYMMDD')
		_run_sparkling_s(ymd)
		current_date = current_date.add(days=1)


def _run_sparkling_s(ymd: str):
	print(f'\n{_LOG_PREFIX} [pubId={pubId} {ymd}] 스파크 트리거 ...\n')

	# if not os.path.exists('./logs'):
	# 	os.makedirs('./logs/zbgfp')

	# f = open(f'./logs/zbgfp/{ymd}.log', 'w')
	try:
		# Zircon B GFP
		print(f'\n{_LOG_PREFIX} [{datetime.now().strftime("%Y.%m.%d_%H:%M:%S")}] ZBGFP ACCU pubId={pubId} {ymd} ...')
		out = subprocess.check_output([
			f'/home1/irteam/projects/{BRANCH}/sparkling-s/bin/zircon/b/run-zircon-b-gfp-{manual_job_dao.PROFILE}.sh',
			f'/home1/irteam/projects/{BRANCH}/sparkling-s',
			'com.navercorp.gfp.biz.zircon.b.ZirconBGfpAggregator',
			ymd,
			_KIND,
			pubId,
			'-',
		], encoding='utf-8')
		print(
			f'{_LOG_PREFIX} [{datetime.now().strftime("%Y.%m.%d_%H:%M:%S")}] ZBGFP ACCU pubId={pubId} {ymd} done\n\n')
		# f.write(out)


		# Zircon B GFP COMPACTION
		print(f'\n{_LOG_PREFIX} [{datetime.now().strftime("%Y.%m.%d_%H:%M:%S")}] ZBGFP COMP pubId={pubId} {ymd} ...')
		subprocess.check_output([
			f'/home1/irteam/projects/{BRANCH}/sparkling-s/bin/zircon/b/run-zircon-b-gfp-compaction-{manual_job_dao.PROFILE}.sh',
			f'/home1/irteam/projects/{BRANCH}/sparkling-s',
			'com.navercorp.gfp.biz.zircon.b.ZirconBGfpCompactor',
			ymd,
			_KIND,
			pubId,
			'-'
		], encoding='utf-8')
		print(
			f'{_LOG_PREFIX} [{datetime.now().strftime("%Y.%m.%d_%H:%M:%S")}] ZBGFP COMP pubId={pubId} {ymd} done\n\n')

		print(f'\n{_LOG_PREFIX} [pubId={pubId} {ymd}] 스파크 종료\n')
	except Exception as ex:
		# f.write(f"[{ymd}] 수행하다 에러 발생. {ex}\n")
		print(f"{_LOG_PREFIX} [{ymd}] 수행하다 에러 발생. {ex}\n")
	finally:
		# f.close()
		pass


_run_spark_zircon_b_gfp_in_sequentially()
