#!/bin/bash

# 리얼에서 특정 날짜 원본 데이터를 생성한 뒤, x개월치 복사본을 만들 때 사용함.

# 시작 날짜와 종료 날짜를 설정합니다.
start_date="2024-01-01"
end_date="2024-03-31"

# 시작 날짜부터 종료 날짜까지 하루씩 증가시키면서 반복합니다.
current_date="$start_date"
while [[ "$current_date" < "$end_date" ]] || [[ "$current_date" == "$end_date" ]]; do
	# 해당 날짜에 대한 디렉토리 경로 생성
	source_dir="/user/gfp-data/temp/bitnacho/zircon/b/warehouse_hh/$(echo $current_date | tr - /)"
	target_dir="/user/gfp-data/temp/bitnacho/zircon/b/warehouse_hh/$(echo $current_date | tr - /)"

	# 해당 날짜의 시간 디렉토리를 00부터 23까지 이동
	for i in {00..23}; do
		hadoop fs -mv "$source_dir/_apHour=$i" "$target_dir/$i"
		#echo "$source_dir/_apHour=$i $target_dir/$i"
	done

	# 다음 날짜로 이동
	current_date=$(date -I -d "$current_date + 1 day")
done
