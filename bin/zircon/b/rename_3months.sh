#!/bin/bash

# 리얼 7월~9월까지 3개월치 시간별 지르콘을 warehouse로 이동하는 스크립트

######### zb
# warehouse_bak 디렉토리 생성
echo 'warehouse_bak 디렉토리 생성 ..'
#hdfs dfs -mkdir -p  /user/gfp-data/zircon/b/warehouse_bak/2024


## warehouse를 warehouse_bak으로 이동
echo 'warehouse를 warehouse_bak으로 이동 ..'
#hdfs dfs -mv /user/gfp-data/zircon/b/warehouse/2024/07 /user/gfp-data/zircon/b/warehouse_bak/2024/.
#hdfs dfs -mv /user/gfp-data/zircon/b/warehouse/2024/08 /user/gfp-data/zircon/b/warehouse_bak/2024/.
#hdfs dfs -mv /user/gfp-data/zircon/b/warehouse/2024/09 /user/gfp-data/zircon/b/warehouse_bak/2024/.

## warehouse_hh를 warehouse로 이동
echo 'warehouse_hh를 warehouse로 이동 ..'
#hdfs dfs -mv /user/gfp-data/zircon/b/warehouse_hh/2024/07 /user/gfp-data/zircon/b/warehouse/2024/.
#hdfs dfs -mv /user/gfp-data/zircon/b/warehouse_hh/2024/08 /user/gfp-data/zircon/b/warehouse/2024/.
#hdfs dfs -mv /user/gfp-data/zircon/b/warehouse_hh/2024/09 /user/gfp-data/zircon/b/warehouse/2024/.


######### zbgfp
# warehouse_bak 디렉토리 생성
echo 'warehouse_bak 디렉토리 생성 ..'
#hdfs dfs -mkdir -p  /user/gfp-data/zircon/b/gfp/warehouse_bak/2024

echo 'warehouse를 warehouse_bak으로 이동 ..'
#hdfs dfs -mv /user/gfp-data/zircon/b/gfp/warehouse/2024/04 /user/gfp-data/zircon/b/gfp/warehouse_bak/2024/.
#hdfs dfs -mv /user/gfp-data/zircon/b/gfp/warehouse/2024/05 /user/gfp-data/zircon/b/gfp/warehouse_bak/2024/.
#hdfs dfs -mv /user/gfp-data/zircon/b/gfp/warehouse/2024/06 /user/gfp-data/zircon/b/gfp/warehouse_bak/2024/.
#hdfs dfs -mv /user/gfp-data/zircon/b/gfp/warehouse/2024/07 /user/gfp-data/zircon/b/gfp/warehouse_bak/2024/.
#hdfs dfs -mv /user/gfp-data/zircon/b/gfp/warehouse/2024/08 /user/gfp-data/zircon/b/gfp/warehouse_bak/2024/.
#hdfs dfs -mv /user/gfp-data/zircon/b/gfp/warehouse/2024/09 /user/gfp-data/zircon/b/gfp/warehouse_bak/2024/.
#hdfs dfs -mv /user/gfp-data/zircon/b/gfp/warehouse/2024/10 /user/gfp-data/zircon/b/gfp/warehouse_bak/2024/.

echo 'warehouse_hh를 warehouse로 이동 ..'
#hdfs dfs -mv /user/gfp-data/zircon/b/gfp/warehouse_hh/2024/10 /user/gfp-data/zircon/b/gfp/warehouse/2024/.