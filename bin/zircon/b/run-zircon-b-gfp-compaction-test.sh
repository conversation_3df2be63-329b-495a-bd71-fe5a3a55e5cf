#!/bin/bash

# [ test ]
# 네이버서비스		5b8f669428b373001fe56ea8

# [ real ]
# 네이버서비스		5b8f669428b373001fe56ea8
# 퍼스널커뮤니티	5bfd00d166de9300257b0bcb
# 오픈커뮤니티		5d9ef168e933ef00170334a1
# SNOW 				5f5f0901a4ad0b0031bd4cbf
# 네이버TV			5f07c40db85575001dd194aa

# 실행 스크립트
# ./zircon/b/run-zircon-b-gfp-compaction-test.sh /home1/irteam/projects/ssp-branch/sparkling-s com.navercorp.gfp.biz.zircon.b.ZirconBGfpCompactor 20240602 manual "*" "-"


source ~/.bashrc                                                                                                                                                              ˆ

sparkling_home=$1
aggregator=$2
date_time=$3
exec_time=`date '+%Y-%m-%dT%H:%M:%S.%3N'`

echo sparkling_home=${sparkling_home}
echo aggregator=${aggregator}
echo date_time=${date_time}
echo exec_time=${exec_time}
echo "${@:4}"

spark-submit --class ${aggregator} \
    --name ${aggregator}-${date_time}-${exec_time} \
    --executor-memory 1g \
    --num-executors 10 \
    --executor-cores 2 \
	--conf spark.scheduler.mode=FAIR \
	--conf spark.scheduler.allocation.file=hdfs://bizcloud/user/gfp-data/fairscheduler.xml \
	--conf spark.scheduler.pool=zircon_b_gfp \
    --deploy-mode cluster \
    --master yarn \
    --queue biz_gep \
    --conf spark.eventLog.dir=hdfs://bizcloud/user/gfp-data/spark-history/ \
    --conf spark.eventLog.enabled=true \
    --conf spark.executor.extraJavaOptions=-XX:+UseG1GC \
    --conf spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922 \
    --conf spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro \
    --conf "spark.driver.extraJavaOptions=-Dhdp.version=*******-78 -XX:+UseG1GC -Dfile.encoding=utf-8" \
    --conf spark.hadoop.dfs.nameservices=bizcloud \
    --conf spark.kerberos.access.hadoopFileSystems=hdfs://bizcloud \
    --conf spark.kerberos.keytab=/home1/irteam/apps/c3/gfp-data.keytab \
    --conf spark.kerberos.principal=<EMAIL> \
    --conf spark.serializer=org.apache.spark.serializer.KryoSerializer \
    --conf spark.sql.caseSensitive=true \
    --conf spark.sql.parquet.mergeSchema=true \
    --conf spark.sql.warehouse.dir=hdfs://bizcloud/user/gfp-data/apps/spark/warehouse \
    --conf spark.yarn.am.extraJavaOptions=-Dhdp.version=*******-78 \
    --conf spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro \
    --conf spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922 \
    --conf spark.yarn.archive=hdfs://bizcloud/user/gfp-data/apps/spark/3.2.4/spark3.2.4_jars.tar.gz \
    --conf spark.yarn.submit.waitAppCompletion=true \
    --conf spark.yarn.historyServer.address=http://spark3-his.nam-batch.svc.ad1.io.navercorp.com:18080 \
    ${sparkling_home}/jar/sparkling-s.jar ${date_time} "${@:4}"
