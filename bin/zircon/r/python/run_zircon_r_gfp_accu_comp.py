"""
- 시작 ~ 종료 날짜를 주고 Zircon R GFP를 적재/컴팩션하는 스크립트
- 결과는 ./logs/zrgfp에 생성됨
- python ./zircon/r/python/run_zircon_r_gfp_accu_comp.py
"""
import subprocess
from datetime import datetime

import pendulum

_LOG_PREFIX = '[RUN-ZRGFP]'
_DEFAULT_TZ = pendulum.timezone('Asia/Seoul')
_KIND = 'manual'  # 정규 생성

PROFILE = 'real'
BRANCH = 'wd-zr'

# [ real ]
# 네이버서비스		5b8f669428b373001fe56ea8
# 퍼스널커뮤니티	5bfd00d166de9300257b0bcb
# 오픈커뮤니티		5d9ef168e933ef00170334a1
# SNOW 				5f5f0901a4ad0b0031bd4cbf
# 네이버TV			5f07c40db85575001dd194aa
# LINEWEBTOON		5d11dc1a34480e001d31fb26

pubId = '*'

# Zircon R GFP 생성을 원하는 기간 설정
start_date = pendulum.date(2025, 5, 1)
end_date = pendulum.date(2025, 5, 1)


def _run_spark_zircon_r_gfp_in_sequentially():
	"""
	날짜별/매체별 Zircon R 스파크 트리거
	:param context:
	:return:
	"""
	current_date = start_date
	while current_date <= end_date:
		ymd = current_date.format('YYYYMMDD')
		_run_sparkling_s(ymd)
		current_date = current_date.add(days=1)


def _run_sparkling_s(ymd: str):
	print(f'\n{_LOG_PREFIX} [pubId={pubId} {ymd}] 스파크 트리거 ...\n')

	# if not os.path.exists('./logs'):
	# 	os.makedirs('./logs/zbgfp')

	# f = open(f'./logs/zbgfp/{ymd}.log', 'w')
	try:
		# Zircon R GFP
		# /home1/irteam/projects/wd-zr/sparkling-s com.navercorp.gfp.biz.zircon.r.ZirconRGfpAggregator manual 20250301 "*" "-"
		print(f'\n{_LOG_PREFIX} [{datetime.now().strftime("%Y.%m.%d_%H:%M:%S")}] ZRGFP ACCU pubId={pubId} {ymd} ...')
		cmd = [
			f'/home1/irteam/projects/{BRANCH}/sparkling-s/bin/zircon/r/{PROFILE[:1].lower()}1_run_zircon_r_gfp_{PROFILE}.sh',
			f'/home1/irteam/projects/{BRANCH}/sparkling-s',
			'com.navercorp.gfp.biz.zircon.r.ZirconRGfpAggregator',
			_KIND,
			ymd,
			pubId,
			'-',
		]
		print(f'cmd={cmd}')
		out = subprocess.check_output(cmd, encoding='utf-8')
		print(
			f'{_LOG_PREFIX} [{datetime.now().strftime("%Y.%m.%d_%H:%M:%S")}] ZRGFP ACCU pubId={pubId} {ymd} done\n\n')
		# f.write(out)

		# Zircon R GFP COMPACTION
		print(f'\n{_LOG_PREFIX} [{datetime.now().strftime("%Y.%m.%d_%H:%M:%S")}] ZRGFP COMP pubId={pubId} {ymd} ...')
		cmd = [
			f'/home1/irteam/projects/{BRANCH}/sparkling-s/bin/zircon/r/{PROFILE[:1].lower()}2_run_zircon_r_gfp_compaction_{PROFILE}.sh',
			f'/home1/irteam/projects/{BRANCH}/sparkling-s',
			'com.navercorp.gfp.biz.zircon.r.ZirconRGfpCompactor',
			_KIND,
			ymd,
			pubId,
			'-'
		]
		print(f'cmd={cmd}')
		subprocess.check_output(cmd, encoding='utf-8')
		print(
			f'{_LOG_PREFIX} [{datetime.now().strftime("%Y.%m.%d_%H:%M:%S")}] ZRGFP COMP pubId={pubId} {ymd} done\n\n')

		print(f'\n{_LOG_PREFIX} [pubId={pubId} {ymd}] 스파크 종료\n')
	except Exception as ex:
		# f.write(f"[{ymd}] 수행하다 에러 발생. {ex}\n")
		print(f"{_LOG_PREFIX} [{ymd}] 수행하다 에러 발생. {ex}\n")
	finally:
		# f.close()
		pass


_run_spark_zircon_r_gfp_in_sequentially()
