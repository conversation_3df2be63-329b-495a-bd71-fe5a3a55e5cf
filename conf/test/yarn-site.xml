<?xml version="1.0" encoding="UTF-8"?>

<!--Autogenerated by Cloudera Manager-->
<configuration>
  <property>
    <name>yarn.acl.enable</name>
    <value>true</value>
  </property>
  <property>
    <name>yarn.admin.acl</name>
    <value>zeppelin,SPARK,hive,yarn,irteam,hdfs,irteamsu,sdc,dr.who</value>
  </property>
  <property>
    <name>yarn.resourcemanager.ha.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>yarn.resourcemanager.ha.automatic-failover.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>yarn.resourcemanager.ha.automatic-failover.embedded</name>
    <value>true</value>
  </property>
  <property>
    <name>yarn.resourcemanager.recovery.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>yarn.resourcemanager.zk-address</name>
    <value>atcdh008-sa.nfra.io:2181,atcdh009-sa.nfra.io:2181,atcdh006-sa.nfra.io:2181,atcdh010-sa.nfra.io:2181,atcdh007-sa.nfra.io:2181</value>
  </property>
  <property>
    <name>yarn.resourcemanager.store.class</name>
    <value>org.apache.hadoop.yarn.server.resourcemanager.recovery.ZKRMStateStore</value>
  </property>
  <property>
    <name>yarn.client.failover-sleep-base-ms</name>
    <value>100</value>
  </property>
  <property>
    <name>yarn.client.failover-sleep-max-ms</name>
    <value>2000</value>
  </property>
  <property>
    <name>yarn.resourcemanager.cluster-id</name>
    <value>yarnRM</value>
  </property>
  <property>
    <name>yarn.resourcemanager.address.rm236</name>
    <value>atcdh002-sa.nfra.io:8032</value>
  </property>
  <property>
    <name>yarn.resourcemanager.scheduler.address.rm236</name>
    <value>atcdh002-sa.nfra.io:8030</value>
  </property>
  <property>
    <name>yarn.resourcemanager.resource-tracker.address.rm236</name>
    <value>atcdh002-sa.nfra.io:8031</value>
  </property>
  <property>
    <name>yarn.resourcemanager.admin.address.rm236</name>
    <value>atcdh002-sa.nfra.io:8033</value>
  </property>
  <property>
    <name>yarn.resourcemanager.webapp.address.rm236</name>
    <value>atcdh002-sa.nfra.io:8088</value>
  </property>
  <property>
    <name>yarn.resourcemanager.webapp.https.address.rm236</name>
    <value>atcdh002-sa.nfra.io:8090</value>
  </property>
  <property>
    <name>yarn.resourcemanager.address.rm357</name>
    <value>atcdh009-sa.nfra.io:8032</value>
  </property>
  <property>
    <name>yarn.resourcemanager.scheduler.address.rm357</name>
    <value>atcdh009-sa.nfra.io:8030</value>
  </property>
  <property>
    <name>yarn.resourcemanager.resource-tracker.address.rm357</name>
    <value>atcdh009-sa.nfra.io:8031</value>
  </property>
  <property>
    <name>yarn.resourcemanager.admin.address.rm357</name>
    <value>atcdh009-sa.nfra.io:8033</value>
  </property>
  <property>
    <name>yarn.resourcemanager.webapp.address.rm357</name>
    <value>atcdh009-sa.nfra.io:8088</value>
  </property>
  <property>
    <name>yarn.resourcemanager.webapp.https.address.rm357</name>
    <value>atcdh009-sa.nfra.io:8090</value>
  </property>
  <property>
    <name>yarn.resourcemanager.ha.rm-ids</name>
    <value>rm236,rm357</value>
  </property>
  <property>
    <name>yarn.resourcemanager.client.thread-count</name>
    <value>50</value>
  </property>
  <property>
    <name>yarn.resourcemanager.scheduler.client.thread-count</name>
    <value>50</value>
  </property>
  <property>
    <name>yarn.resourcemanager.admin.client.thread-count</name>
    <value>1</value>
  </property>
  <property>
    <name>yarn.scheduler.minimum-allocation-mb</name>
    <value>1024</value>
  </property>
  <property>
    <name>yarn.scheduler.increment-allocation-mb</name>
    <value>512</value>
  </property>
  <property>
    <name>yarn.scheduler.maximum-allocation-mb</name>
    <value>53643</value>
  </property>
  <property>
    <name>yarn.scheduler.minimum-allocation-vcores</name>
    <value>1</value>
  </property>
  <property>
    <name>yarn.scheduler.increment-allocation-vcores</name>
    <value>1</value>
  </property>
  <property>
    <name>yarn.scheduler.maximum-allocation-vcores</name>
    <value>40</value>
  </property>
  <property>
    <name>yarn.resourcemanager.amliveliness-monitor.interval-ms</name>
    <value>1000</value>
  </property>
  <property>
    <name>yarn.am.liveness-monitor.expiry-interval-ms</name>
    <value>600000</value>
  </property>
  <property>
    <name>yarn.resourcemanager.am.max-attempts</name>
    <value>2</value>
  </property>
  <property>
    <name>yarn.resourcemanager.container.liveness-monitor.interval-ms</name>
    <value>600000</value>
  </property>
  <property>
    <name>yarn.resourcemanager.nm.liveness-monitor.interval-ms</name>
    <value>1000</value>
  </property>
  <property>
    <name>yarn.nm.liveness-monitor.expiry-interval-ms</name>
    <value>600000</value>
  </property>
  <property>
    <name>yarn.resourcemanager.resource-tracker.client.thread-count</name>
    <value>50</value>
  </property>
  <property>
    <name>yarn.application.classpath</name>
    <value>$HADOOP_CLIENT_CONF_DIR,$HADOOP_CONF_DIR,$HADOOP_COMMON_HOME/*,$HADOOP_COMMON_HOME/lib/*,$HADOOP_HDFS_HOME/*,$HADOOP_HDFS_HOME/lib/*,$HADOOP_YARN_HOME/*,$HADOOP_YARN_HOME/lib/*</value>
  </property>
  <property>
    <name>yarn.resourcemanager.scheduler.class</name>
    <value>org.apache.hadoop.yarn.server.resourcemanager.scheduler.fair.FairScheduler</value>
  </property>
  <property>
    <name>yarn.resourcemanager.max-completed-applications</name>
    <value>10000</value>
  </property>
  <property>
    <name>yarn.nodemanager.remote-app-log-dir</name>
    <value>/tmp/logs</value>
  </property>
  <property>
    <name>yarn.nodemanager.remote-app-log-dir-suffix</name>
    <value>logs</value>
  </property>
</configuration>
