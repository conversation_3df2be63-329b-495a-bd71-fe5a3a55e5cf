"""
#### 개요
- airflow meta DB 의 유지보수를 위해 Dag<PERSON>un, TaskInstance, Log, XCom, Job DB and SlaMiss entries 관련 내용을 주기적으로 삭제
- worker pod 내 airflow-worker 컨테이너에서 bash 명령어을 실행하는 방식으로 동작
- [소스 참고1](https://cloud.google.com/composer/docs/cleanup-airflow-database)
- [소스 참고2](https://github.com/teamclairvoyant/airflow-maintenance-dags/tree/master/db-cleanup)

#### 주기
- 매일 14시 10분

#### config
- maxDBEntryAgeInDays: (optional) 삭제 하고자 하는 object 의 날짜 기준 (해당 일 수 보다 오래된 로그 삭제. default 62일)
"""
import logging
import os
from datetime import datetime, timedelta

import dateutil.parser
import pendulum
from airflow import settings
from airflow.configuration import conf
from airflow.jobs.base_job import BaseJob
from airflow.models import DAG, DagRun, Log, SlaMiss, \
    TaskInstance, Variable, XCom
from airflow.operators.python import PythonOperator
from sqlalchemy import and_, func
from sqlalchemy.exc import ProgrammingError
from sqlalchemy.orm import load_only

from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ

try:
    # airflow.utils.timezone is available from v1.10 onwards
    from airflow.utils import timezone

    now = timezone.utcnow
except ImportError:
    now = datetime.utcnow

_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")

_XCOM_MAX_DATE = 'max_date'

# airflow 버전 정보. 명세되어 있지 않다면 2.2.5 로 취급.
_AIRFLOW_VERSION = os.getenv("MAJOR_VERSION", "2.2.5").split(".")

# 삭제하고자 하는 로그 기간. default 62일 (Variable 에 설정 되어 있지 않음)
_DEFAULT_MAX_DB_ENTRY_AGE_IN_DAYS = int(Variable.get("airflow_db_cleanup__max_db_entry_age_in_days", 62))

# 삭제 진행중인 list 를 출력할 지 여부. True 일 경우 process 가 다소 늦어질 수 있음.
PRINT_DELETES = False

# 삭제할 모든 object 목록. 스킵하려면 주석처리.
# TODO: Enable DagModel when the appropriate patch is applied
#   See https://github.com/apache/airflow/issues/23206
DATABASE_OBJECTS = [{
    "airflow_db_model": BaseJob,
    "age_check_column": BaseJob.latest_heartbeat,
    "keep_last": False,
    "keep_last_filters": None,
    "keep_last_group_by": None
}, {
    "airflow_db_model": DagRun,
    "age_check_column": DagRun.execution_date,
    "keep_last": True,
    "keep_last_filters": [DagRun.external_trigger.is_(False)],
    "keep_last_group_by": DagRun.dag_id
}, {
    "airflow_db_model": TaskInstance,
    "age_check_column": TaskInstance.execution_date if _AIRFLOW_VERSION < ['2', '2', '0'] else TaskInstance.start_date,
    "keep_last": False,
    "keep_last_filters": None,
    "keep_last_group_by": None
}, {
    "airflow_db_model": Log,
    "age_check_column": Log.dttm,
    "keep_last": False,
    "keep_last_filters": None,
    "keep_last_group_by": None
}, {
    "airflow_db_model": XCom,
    "age_check_column": XCom.execution_date,
    "keep_last": False,
    "keep_last_filters": None,
    "keep_last_group_by": None
}, {
    "airflow_db_model": SlaMiss,
    "age_check_column": SlaMiss.execution_date,
    "keep_last": False,
    "keep_last_filters": None,
    "keep_last_group_by": None
    # }, {
    #     "airflow_db_model": DagModel,
    #     "age_check_column": DagModel.last_scheduler_run if AIRFLOW_VERSION < ['2', '0', '2'] else DagModel.last_parsed_time,
    #     "keep_last": False,
    #     "keep_last_filters": None,
    #     "keep_last_group_by": None
}]

# Check for TaskReschedule model
try:
    from airflow.models import TaskReschedule

    DATABASE_OBJECTS.append({
        "airflow_db_model": TaskReschedule,
        "age_check_column": TaskReschedule.execution_date if _AIRFLOW_VERSION < ['2', '2',
                                                                                 '0'] else TaskReschedule.start_date,
        "keep_last": False,
        "keep_last_filters": None,
        "keep_last_group_by": None
    })

except Exception as e:
    logging.error(e)

# Check for TaskFail model
try:
    from airflow.models import TaskFail

    DATABASE_OBJECTS.append({
        "airflow_db_model": TaskFail,
        "age_check_column": TaskFail.execution_date,
        "keep_last": False,
        "keep_last_filters": None,
        "keep_last_group_by": None
    })

except Exception as e:
    logging.error(e)

# Check for RenderedTaskInstanceFields model
try:
    from airflow.models import RenderedTaskInstanceFields

    DATABASE_OBJECTS.append({
        "airflow_db_model": RenderedTaskInstanceFields,
        "age_check_column": RenderedTaskInstanceFields.execution_date,
        "keep_last": False,
        "keep_last_filters": None,
        "keep_last_group_by": None
    })

except Exception as e:
    logging.error(e)

# Check for ImportError model
try:
    from airflow.models import ImportError

    DATABASE_OBJECTS.append({
        "airflow_db_model": ImportError,
        "age_check_column": ImportError.timestamp,
        "keep_last": False,
        "keep_last_filters": None,
        "keep_last_group_by": None
    })

except Exception as e:
    logging.error(e)

# Check for celery executor
airflow_executor = str(conf.get("core", "executor"))
if airflow_executor == "CeleryExecutor":
    try:
        from celery.backends.database.models import Task, TaskSet

        DATABASE_OBJECTS.extend(({
                                     "airflow_db_model": Task,
                                     "age_check_column": Task.date_done,
                                     "keep_last": False,
                                     "keep_last_filters": None,
                                     "keep_last_group_by": None
                                 }, {
                                     "airflow_db_model": TaskSet,
                                     "age_check_column": TaskSet.date_done,
                                     "keep_last": False,
                                     "keep_last_filters": None,
                                     "keep_last_group_by": None
                                 }))

    except Exception as e:
        logging.error(e)

session = settings.Session()


def _print_configuration_function(**context):
    """
    작업 수행에 있어서 설정된 configuration 들을 출력

    :param context:
    :return:
    """
    logging.info("Loading Configurations...")
    dag_run_conf = context.get("dag_run").conf
    logging.info(f"dag_run.conf: {str(dag_run_conf)}")
    max_db_entry_age_in_days = dag_run_conf.get("maxDBEntryAgeInDays")

    logging.info(f"maxDBEntryAgeInDays from dag_run.conf: {str(dag_run_conf)}")
    if max_db_entry_age_in_days is None or max_db_entry_age_in_days < 1:
        logging.info(
            f"maxDBEntryAgeInDays conf variable isn't included or Variable value is less than 1. Using Default")
        max_db_entry_age_in_days = _DEFAULT_MAX_DB_ENTRY_AGE_IN_DAYS
    max_date = now() + timedelta(-max_db_entry_age_in_days)
    logging.info(f"""
    Finished Loading Configurations
                     
    Configurations:
        max_db_entry_age_in_days: {str(max_db_entry_age_in_days)}
        max_date:                 {str(max_date)}
        session:                  {str(session)}\n
        Setting max_execution_date to XCom for Downstream Processes""")

    context["ti"].xcom_push(key=_XCOM_MAX_DATE, value=max_date.isoformat())


def _cleanup_function(**context):
    """
    DB sessiong 을 이용해 대상 object 들을 삭제

    :param context:
    :return:
    """
    if _AIRFLOW_VERSION > ['2', '2', '5']:
        logging.error("The script is not supported for this Airflow Version. Skipped deleting the data.")
        return
    logging.info("Retrieving max_execution_date from XCom")
    max_date = context["ti"].xcom_pull(task_ids=print_configuration.task_id, key=_XCOM_MAX_DATE)
    max_date = dateutil.parser.parse(max_date)  # stored as iso8601 str in xcom

    airflow_db_model = context["params"].get("airflow_db_model")
    state = context["params"].get("state")
    age_check_column = context["params"].get("age_check_column")
    keep_last = context["params"].get("keep_last")
    keep_last_filters = context["params"].get("keep_last_filters")
    keep_last_group_by = context["params"].get("keep_last_group_by")

    # config 로 입력하여 실행 할 수도 있다.
    logging.info(f"""
    Configurations:
        max_date:                 {str(max_date)}
        session:                  {str(session)}
        airflow_db_model:         {str(airflow_db_model)}
        state:                    {str(state)}
        age_check_column:         {str(age_check_column)}
        keep_last:                {str(keep_last)}
        keep_last_filters:        {str(keep_last_filters)}
        keep_last_group_by:       {str(keep_last_group_by)}

    Running Cleanup Process...""")

    try:
        query = session.query(airflow_db_model).options(
            load_only(age_check_column))

        logging.info(f"INITIAL QUERY : {str(query)}")

        if keep_last:

            subquery = session.query(func.max(DagRun.execution_date))
            # workaround for MySQL "table specified twice" issue
            # https://github.com/teamclairvoyant/airflow-maintenance-dags/issues/41
            if keep_last_filters is not None:
                for entry in keep_last_filters:
                    subquery = subquery.filter(entry)

                logging.info(f"SUB QUERY [keep_last_filters]: {str(subquery)}")

            if keep_last_group_by is not None:
                subquery = subquery.group_by(keep_last_group_by)
                logging.info(f"SUB QUERY [keep_last_group_by]: {str(subquery)}")

            subquery = subquery.from_self()

            query = query.filter(
                and_(age_check_column.notin_(subquery)),
                and_(age_check_column <= max_date))

        else:
            query = query.filter(age_check_column <= max_date, )

        if PRINT_DELETES:
            entries_to_delete = query.all()

            logging.info(f"Query: {str(query)}")
            logging.info(f"Process will be Deleting the following {str(airflow_db_model.__name__)}(s):")
            for entry in entries_to_delete:
                date = str(entry.__dict__[str(age_check_column).split(".")[1]])
                logging.info("\tEntry: " + str(entry) + ", Date: " + date)

            logging.info(f"Process will be Deleting {str(len(entries_to_delete))} {str(airflow_db_model.__name__)}(s)")
        else:
            logging.warning(
                "You've opted to skip printing the db entries to be deleted. Set PRINT_DELETES to True to show entries")

        logging.info("Performing Delete...")
        # using bulk delete
        query.delete(synchronize_session=False)
        session.commit()
        logging.info("Finished Performing Delete")

        logging.info("Finished Running Cleanup Process")

    except ProgrammingError as e:
        logging.error(e)
        logging.error(f'{str(airflow_db_model)} + " is not present in the metadata. Skipping..."')


with DAG(
        _DAG_ID,
        description='airflow meta DB 내 로그를 주기적으로 삭제하는 dag',
        default_args={
            "owner": 'ins.cho',
            "email": ALERT_EMAIL_ADDRESSES,
            "email_on_failure": True,
            "email_on_retry": False,
            "retries": 1,
            "retry_delay": timedelta(minutes=1),
            "depends_on_past": False,
        },
        schedule_interval='10 14 * * *',
        start_date=pendulum.yesterday(tz=DEFAULT_TZ),
        tags=['airflow', 'maintenance', 'cleanup', 'meta-db', 'hourly'],
        catchup=False,
        params={
            'maxDBEntryAgeInDays': ''
        }
) as dag:
    dag.doc_md = __doc__

    print_configuration = PythonOperator(
        task_id="print_configuration",
        python_callable=_print_configuration_function,
    )

    for db_object in DATABASE_OBJECTS:
        cleanup_op = PythonOperator(
            task_id=f"cleanup_{str(db_object['airflow_db_model'].__name__)}",
            python_callable=_cleanup_function,
            params=db_object,
            provide_context=True
        )

        print_configuration >> cleanup_op
