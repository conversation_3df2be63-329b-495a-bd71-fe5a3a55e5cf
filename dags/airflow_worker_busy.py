"""
#### 개요
- pod 의 graceful shutdown 을 막을 용도로, 단순히 worker 에게 작업을 부여하기 위해 주기적으로 실행되는 Dag
- [참고](https://wiki.navercorp.com/pages/viewpage.action?pageId=866567138#:~:text=airflow%2Dworker%20container%20restart%2C%20it%20isn%27t%20graceful%20shutdown)

#### 주기
- 매시 15, 45분

#### config
- 없음
"""
import logging
import os
import random
import time
from datetime import timedelta
from pathlib import Path

import pendulum
from airflow.models.dag import DAG
from airflow.operators.dummy import DummyOperator
from airflow.operators.python import PythonOperator

from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, NUM_WORKERS
from core.utils import clean_dag_logs

_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")

_LOCK_FILE = f'/tmp/{_DAG_ID}.lck'


def make_busy():
    """
    단순히 루프를 도는 함수.
    단 스스로의 dag log 를 삭제한다.

    :return:
    """
    lock_file = Path(_LOCK_FILE)
    try:
        lock_file.touch(mode=0o640, exist_ok=False)
    except FileExistsError:
        logging.info(f'{lock_file} exists, somebody else is working on')
    else:
        clean_dag_logs(_DAG_ID, max_age_hours=24 * 3, dry_run=False)
    finally:
        lock_file.unlink(missing_ok=True)
        logging.info(f'{lock_file} unlinked (maybe missing)')

    logging.info('******************')
    logging.info('BUSY WORKS')
    logging.info('******************')

    n = 1000
    dur = 10 / n
    for i in range(n):
        for j in range(10):
            random.sample(range(100000), 2000)
        time.sleep(dur)

    logging.info('BUSY WORKS: DONE')


with DAG(
        _DAG_ID,
        default_args={
            'owner': 'ins.cho',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
        },
        schedule_interval='15,45 * * * *',
        start_date=pendulum.datetime(2022, 3, 1, tz=DEFAULT_TZ),
        tags=['airflow', 'maintenance', 'hourly', 'twice'],
        catchup=False,
) as dag:
    dag.doc_md = __doc__

    setup = DummyOperator(
        task_id='setup',
    )

    for worker_id in range(NUM_WORKERS):
        busy_op = PythonOperator(
            task_id=f'make_worker_{worker_id}_busy',
            python_callable=make_busy,
            execution_timeout=timedelta(minutes=30),
        )

        setup >> busy_op
