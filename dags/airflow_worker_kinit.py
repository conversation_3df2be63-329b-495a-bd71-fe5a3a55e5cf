"""
### worker pod 커버로스 인증 갱신 ( 순차 처리 )

#### 주기
- 매시
- 실패 시 5분 간격으로 5번 재시도 (총 6회 실행)

#### config
- 없음
"""

import os
import logging
import pendulum
import subprocess

from string import digits
from datetime import timedelta

from airflow.models.dag import DAG
from airflow.exceptions import AirflowException
from airflow.operators.python import PythonOperator

from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, NUM_WORKERS, \
    C3_USER, C3_REALM

_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")

_WORKER_POD_NAME = os.environ.get('HOSTNAME').rstrip(digits)
_KINIT_CMD = f"kinit -kt /opt/airflow/c3s/{C3_USER}.keytab {C3_USER}@{C3_REALM}"


def _setup():
    logging.info(f'config: airflow #workers: {NUM_WORKERS}')


def _kinit(target_pod_name: str):
    """
    각 worker pod 에 커버로스 인증 갱신

    :return:
    """
    exec_pod = os.environ.get("HOSTNAME")
    logging.info(f'{exec_pod} pod 에서 {target_pod_name} pod 에 kinit 실행')
    base_cmd = ('' if exec_pod == target_pod_name else f'kubectl exec {target_pod_name} -- ') + "bash -c '{cmd}'"
    # 본 task instance 가 실행된 pod 과 처리할 pod 이 같다면 kubectl exec 를 통해 target pod 내부로 접근할 필요 없음

    try:
        kinit_result = subprocess.run(
            [base_cmd.format(cmd=_KINIT_CMD)],
            shell=True,
            capture_output=True,
            text=True,
            timeout=900,
        )
        logging.info(f'kinit c3 result={kinit_result}')

        # klist 로 확인
        klist_result = subprocess.run(
            [base_cmd.format(cmd="klist -5")],
            shell=True,
            capture_output=True,
            text=True,
            timeout=900,
        )
        logging.info('klist -5 result ==>')
        logging.info(f'STDOUT:\n{klist_result.stdout}')
        logging.info(f'STDERR:\n{klist_result.stderr}')

        klist_result.check_returncode()

    except subprocess.CalledProcessError:
        raise AirflowException(f'{base_cmd.format(cmd=_KINIT_CMD)} 실패')
    else:
        logging.info(f'{base_cmd.format(cmd=_KINIT_CMD)} 완료')


with DAG(
        _DAG_ID,
        default_args={
            'owner': 'bitna.cho',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
        },
        schedule_interval='0 * * * *',
        start_date=pendulum.datetime(2023, 9, 1, tz=DEFAULT_TZ),
        tags=['airflow', 'operation', 'worker', 'kinit'],
        catchup=False,
) as dag:
    dag.doc_md = __doc__

    setup = PythonOperator(
        task_id='setup',
        python_callable=_setup
    )

    upstream = setup
    for worker_idx in range(NUM_WORKERS):
        kinit_on_worker = PythonOperator(
            task_id=f'kinit_on_worker_{worker_idx}',
            python_callable=_kinit,
            execution_timeout=timedelta(hours=1),
            retries=5,
            retry_delay=timedelta(minutes=5),
            op_args=[_WORKER_POD_NAME + str(worker_idx)]
        )

        # worker 하나씩 순차 처리
        upstream >> kinit_on_worker
        upstream = kinit_on_worker
