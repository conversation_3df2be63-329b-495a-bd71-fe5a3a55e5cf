import logging

from airflow.exceptions import AirflowException
from airflow.providers.mongo.hooks.mongo import MongoHook

from core.utils import id_arr_to_csv, csv_to_id_arr

from core.base import CONN_MONGO_FOR_DATA
from core.dao.environment_dao import get_environment_value_by_name


def get_adpost_abuse_publisher_ids():
	"""
	Adpost Abuse 리포트 대상 매체 id 목록 정보 추출
		- adpost-abuse-report-config = [{ publiser_id, keyGroup_id }]
	:return publisher_ids: String
	"""

	publisher_ids = [doc['publisher_id'] for doc in get_environment_value_by_name('adpost-abuse-report-config')]

	return id_arr_to_csv(publisher_ids)


def is_ready_abuse_report_log(ym, publisher_ids):
	"""
	매체와 연동된 RK AP 들의 어뷰즈 리포트 연동이 완료 되었는지 체크
	:param ym: YYYYMM
	:param publisher_ids:
	:return is_ready: Boolean
	"""
	report_api_types = get_report_api_types(csv_to_id_arr(publisher_ids))

	logging.info(f'report_api_types: {report_api_types}')

	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()['AdProviderAbuseReportApiSchedules']

		res = list(coll.find({
			'targetDate': ym,
			'reportApiType': {'$in': report_api_types},
		}))

		if len(res) == 0:
			raise AirflowException(f'targetDate({ym}) 에 해당하는 AP 어뷰즈 리포트 연동 스케쥴 정보가 존재하지 않습니다. (reportApiType = {report_api_types})')

		not_complete = list(filter(lambda doc: doc['state'] != 'COMPLETE', res))

		if len(not_complete) == 0:
			return True
		else:
			return False


def get_report_api_types(publisher_ids):
	"""
	매체와 연동된 RK AP 의 reportApiType 목록 조회
	:param publisher_ids:
	:return report_api_types:
	"""

	logging.info(f'publisher_ids: {publisher_ids}')

	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()['SyncAdProviderInfos']

		res = coll.aggregate([
			{'$match': {'publisher_id': {'$in': publisher_ids}}},
			{'$lookup': {
				'from': 'SyncAdProviders',
				'foreignField': '_id',
				'localField': 'adProvider_id',
				'as': 'ap'
			}},
			{'$unwind': {'path': '$ap'}},
			{'$match': {'ap.reportApi.rkUse': 1}},
			{'$group': {'_id': '', 'reportApiTypes': {'$addToSet': '$ap.reportApi.type'}}},
		])

		results = list(res)

		if len(results) != 1:
			raise AirflowException(f'publisher_ids: {publisher_ids} 와 연동 중인 RK AP 정보가 존재하지 않습니다.')

		logging.info(f'results: {results[0]}')

		return results[0]['reportApiTypes']
