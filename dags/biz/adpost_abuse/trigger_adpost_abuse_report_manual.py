"""
### ADPOST 어뷰즈 리포트 수동 트리거 DAG

#### 0. 위키
- [32. DAG - Adpost 어뷰즈 리포트](https://wiki.navercorp.com/pages/viewpage.action?pageId=1620915937)
- [12. 비지니스 - Adpost 어뷰즈 리포트](https://wiki.navercorp.com/pages/viewpage.action?pageId=1624088836)

#### 1. 주기
- 없음

#### 2. 스케쥴 콜렉션
- Data DB : Jobs
- manual=1 인 경우만 처리 대상임

#### 3. 트리거 대상 DAG
- spark_adpost_abuse_report

#### 4. config
- target_ym_list : 처리할 스케쥴 날짜 리스트 (YYYYMM,YYYYMM,YYYYMM)
"""

import logging
import os
from datetime import timedelta
from time import sleep

import pendulum
from airflow.exceptions import AirflowException
from airflow.models import DAG, DagRun
from airflow.operators.python import PythonOperator
from airflow.utils.email import send_email
from airflow.utils.state import DagRunState
from airflow.utils.types import DagRunType

from biz.adpost_abuse.spark_adpost_abuse_report import DAG_ID as SPARK_DAG_ID
from core.airflow_api import trigger_dagrun
from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, XCOM_TARGET_YM_LIST
from core.dao import job_dao

# DAG 기본정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRIGGER-ADPOST-ABUSE-REPORT-MANUAL]'


def _setup(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화

	:param context:
	:return:
	"""
	target_ym_list_str = context['params'].get(XCOM_TARGET_YM_LIST)

	target_ym_list = []

	if target_ym_list_str:
		target_ym_list_str = target_ym_list_str.replace(' ', '').strip(',')

		# target_ym_list 에 추가
		target_ym_list.extend(target_ym_list_str.split(','))

		for i, ym in enumerate(target_ym_list):
			pendulum.from_format(ym, 'YYYYMM')

		logging.info(f'{_LOG_PREFIX} target_ym_list_str = {target_ym_list_str}')
	else:
		raise AirflowException(f'"target_ym_list"가 기술 되지 않음')

	context['ti'].xcom_push(XCOM_TARGET_YM_LIST, target_ym_list)


def _trigger_adpost_abuse_report(**context):
	"""
	target_ym_list 에 있는 날짜에 대해 스파크 집계 처리함

	:param context:
	:return:
	"""

	# 스킵, 실패한 스케쥴 리스트
	skipped_list = []
	failed_list = []

	target_ym_list = context['ti'].xcom_pull(key=XCOM_TARGET_YM_LIST)

	logging.info(f'{_LOG_PREFIX} target_ym_list={target_ym_list}')

	count = len(target_ym_list)
	for idx, ym in enumerate(target_ym_list):
		logging.info(f'{_LOG_PREFIX} [{idx + 1}/{count}] ym={ym}')

		# 이미 job이 있는지
		job = job_dao.get_job({'type': SPARK_DAG_ID, 'datetime': ym})
		if job:
			if job['manual'] == 0:
				# 자동인 경우, 처리 대상 아님
				skipped_list.append((ym, '스케쥴 DAG 에 의해 처리 예정'))
				logging.warning(f'{_LOG_PREFIX} [{idx + 1}/{count}] ym={ym}. 스케쥴 DAG 에 의해 처리 예정')

				continue
			else:
				# 수동인 경우, 이미 실행 중인지 확인
				if job['running'] == 1:
					skipped_list.append((ym, '이미 처리 중'))
					logging.warning(f'{_LOG_PREFIX} [{idx + 1}/{count}] ym={ym}, running=1. 이미 처리 중이므로 스킵')

					continue

		logging.info(f'{_LOG_PREFIX} [{idx + 1}/{count}] ym={ym}. 진행중')

		# 해당 날짜의 adpost abuse report 생성 요청
		dag_run_id = trigger_dagrun(SPARK_DAG_ID, pendulum.now(DEFAULT_TZ), {'target_ym': ym, 'trigger_by': DagRunType.MANUAL}, DagRunType.MANUAL)
		logging.info(f'{_LOG_PREFIX} 스파크 집계 트리거 ( ym={ym}, spark_dag_id={SPARK_DAG_ID}, dag_run_id={dag_run_id} )')

		# dag run 이 끝났는지 60초 간격으로 확인
		while True:
			sleep(60)
			dag_runs = DagRun.find(dag_id=SPARK_DAG_ID, run_id=dag_run_id)
			if dag_runs:
				dag_run: DagRun = dag_runs.pop()

				logging.info(f'{_LOG_PREFIX} {SPARK_DAG_ID}({dag_run_id}) state = {dag_run.get_state()}')
				if dag_run.get_state() == DagRunState.SUCCESS:
					break
				elif dag_run.get_state() == DagRunState.FAILED:
					failed_list.append((ym, f'dag_run_id={dag_run_id}'))
					break
			else:
				failed_list.append((ym, f'dag_run_id={dag_run_id} 트리거 시켰으나, 존재하지 않음'))
				logging.info(f'{_LOG_PREFIX} {SPARK_DAG_ID}({dag_run_id}) 트리거 시켰으나, 존재하지 않음')
				break

	logging.info(f'{_LOG_PREFIX} 완료')

	context['ti'].xcom_push('skipped_list', skipped_list)
	context['ti'].xcom_push('failed_list', failed_list)


def _alert(**context):
	"""
	스킵 또는 실패된 리스트 알림
	:param context:
	:return:
	"""
	skipped_list = context['ti'].xcom_pull(key='skipped_list')
	skipped_msg = f'skipped {len(skipped_list)} 건 <br/>'

	for item in skipped_list:
		skipped_msg += f'- ym={item[0]}, reason={item[1]}<br/>'

	failed_list = context['ti'].xcom_pull(key='failed_list')
	failed_msg = f'failed {len(failed_list)} 건 <br/>'
	for item in failed_list:
		failed_msg += f'- ym={item[0]}, reason={item[1]}<br/>'

	if skipped_list or failed_list:
		title = f'[adpost abuse report 수동 처리] skipped {len(skipped_list)} 건, failed {len(failed_list)} 건'
		body = f'{skipped_msg}<br/>{failed_msg}<br/>'
		send_email(ALERT_EMAIL_ADDRESSES, title, body)


with DAG(
		_DAG_ID,
		description='Trigger manual adpost abuse report DAG',
		tags=['trigger', 'manual', 'abuse', 'adpost', 'adpost_abuse', 'monthly', 'hdfs', 'bitna.cho'],
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		params={
			XCOM_TARGET_YM_LIST: '',
		},
		start_date=pendulum.datetime(2023, 7, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup
	)

	trigger_adpost_abuse_report = PythonOperator(
		task_id=f'trigger_adpost_abuse_report',
		python_callable=_trigger_adpost_abuse_report,
		retries=3,
		retry_delay=timedelta(seconds=60)
	)

	alert = PythonOperator(
		task_id='alert',
		python_callable=_alert,
	)

	setup >> trigger_adpost_abuse_report >> alert
