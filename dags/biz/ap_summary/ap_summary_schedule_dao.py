import logging

from typing import Callable, List, Union

import pendulum
import requests
import tenacity

from bson.objectid import ObjectId
from pendulum.tz.timezone import Timezone

from airflow.models.dagrun import DagRun
from airflow.utils.types import DagRunType
from airflow.exceptions import AirflowException
from airflow.providers.http.hooks.http import HttpHook
from airflow.providers.mongo.hooks.mongo import MongoHook

from core.utils import id_arr_to_csv
from core.base import CONN_AIRFLOW_WEB, CONN_MONGO_FOR_CMS
from core.dao.environment_dao import is_ready_silver_log, is_ready_gold_index

from biz.ap_summary.ap_summary_base import XCOM_SCHEDULE_ID, \
	SMTYP_AP, SMTYP_REVENUE, SMTYP_GFP, SMTYP_GFP_PK, SMTYP_AP_AMBER, SMTYP_GFP_AMBER

# https://wiki.navercorp.com/display/GFP/DataAdProviderSummarySchedules
COLLECTION = 'DataAdProviderSummarySchedules'

ST_READY = 'READY'
ST_WAIT = 'WAIT'
ST_LAUNCH = 'LAUNCH'  # New status for airflow control
ST_IN_PROGRESS = 'IN_PROGRESS'
ST_COMPLETE = 'COMPLETE'
ST_FAILURE = 'FAILURE'


def fetch_schedules_count(ymd: str):
	"""
	DataAdProviderSummarySchedules 에서 ymd 에 해당하는 스케쥴 관련 건수 정보 조회
		- total_count, complete_count, waiting_count

	:param ymd: 처리할 스케쥴 날짜 (YYYYMMDD)
	:return: total_count, complete_count, waiting_count
	"""

	schedules = fetch_schedules(ymd)

	total_count = len(schedules)
	complete_count = 0
	waiting_count = 0

	# for sch in schedules:
	#     st = sch['state']
	#     if st == ST_COMPLETE:
	#         complete_count += 1
	#     elif st in [ST_WAIT, ST_READY]:
	#         waiting_count += 1

	# Deprecated - CMS 집계 걷어낼 때, 같이 제거할 것. 그때는 state 만 존재함
	for sch in schedules:
		st = sch['state']
		cms_st = str(sch['cmsState']) if sch['cmsState'] else None
		if st == ST_COMPLETE and cms_st in [ST_COMPLETE, None]:
			complete_count += 1
		elif st in [ST_WAIT, ST_READY] and cms_st in [ST_WAIT, ST_READY, None]:
			waiting_count += 1

	return total_count, complete_count, waiting_count


def fetch_schedules(
		ymd: str = '',
		ymd_list: List[str] = None,
		*,
		state: str = '',
		report_api_type: str = '',
		publisher_ids: List[ObjectId] = None,
		adprovider_ids: List[ObjectId] = None,
):
	"""
	DataAdProviderSummarySchedules 에서 조건에 해당하는 스케쥴 목록 조회

	:param ymd: 처리할 스케쥴 날짜 (YYYYMMDD)
	:param ymd_list: 처리할 스케쥴 날짜 리스트
	:param state: 스케쥴 상태 ( default = READY )
	:param report_api_type:
	:param publisher_ids:
	:param adprovider_ids:
	:return: schedules
	"""

	with MongoHook(CONN_MONGO_FOR_CMS) as hook:
		cond = {}

		if ymd:
			cond['ymd'] = ymd
		elif ymd_list:
			cond['ymd'] = {'$in': ymd_list}
		else:
			raise AirflowException('날짜 정보(ymd, ymd_list) 가 존재하지 않습니다.')

		# if state:
		#     cond['state'] = state

		# Deprecated - CMS 집계 걷어낼 때, 같이 제거할 것. 그때는 state 만 존재함
		if state:
			cond['$or'] = [{'state': state}, {'cmsState': state}]

		if report_api_type:
			cond['reportApiType'] = report_api_type

		if publisher_ids:
			cond['publisher_ids'] = {'$in': publisher_ids}

		if adprovider_ids:
			cond['adProvider_ids'] = {'$in': adprovider_ids}

		coll = hook.get_conn().get_database()[COLLECTION]

		schedules = list(coll.find(cond))

		return schedules


def is_ready_to_trigger(tz: Union[str, Timezone], sch: dict):
	"""
	해당 스케쥴이 스파크 집계 가능한 상태인지 확인
		- summaryType=AP/REVENUE/AP_AMBER
			- state=WAIT
		- summaryType=GFP/GFP_PK
			- state=WAIT && 실버 로그 적재 완료
		- summaryType=GFP_AMBER
			- state=WAIT && 골드 로그 적재 완료

	:param tz:
	:param sch:
	:return:
	"""

	if sch.get('state') != ST_WAIT:
		return False

	if sch.get('summaryType') not in [SMTYP_GFP, SMTYP_GFP_PK, SMTYP_GFP_AMBER]:
		return True

	# ap timezone 으로 적용된 endDate 를 한국시간대로 변환
	end_ymdh = sch['endDate'] + '23'
	target_ymdh = pendulum.from_format(end_ymdh, 'YYYYMMDDHH', sch['timezone']).in_tz(tz).format('YYYYMMDDHH')

	if sch.get('summaryType') in [SMTYP_GFP, SMTYP_GFP_PK]:
		return is_ready_silver_log(target_ymdh)
	else:
		return is_ready_gold_index(target_ymdh)


def is_failed(sch: dict):
	"""
	스케쥴이 실패 상태인지 체크

	:param sch:
	:return:
	"""

	# return sch['state'] == ST_FAILURE

	# Deprecated - CMS 집계 걷어낼 때, 같이 제거할 것. 그때는 state 만 존재함
	return sch['state'] == ST_FAILURE or sch['cmsState'] == ST_FAILURE


def trigger_dag_with_api(
		sch: dict,
		*,
		dag_map_fn: Callable,
		trigger_by=''
) -> (str, str):
	"""
	rest api 를 사용하여, spark dag을 trigger 한다.

	:param sch: 스케쥴 정보
	:param dag_map_fn: map_summary_type_to_dag
	:param trigger_by: regular or reprocess
	:return:
	"""

	ymd, schedule_id, summary_type = sch['ymd'], sch['_id'], sch['summaryType']
	dag_id = dag_map_fn(summary_type)
	if dag_id is None:
		raise ValueError('Unknown summaryType: ' + summary_type + ', aborting trigger DAG')

	logging.info(f'trigger DAG target schedule_id: {schedule_id}')

	logical_date = pendulum.now('UTC')
	dag_run_id = DagRun.generate_run_id(DagRunType.MANUAL, logical_date)

	conf = {
		'schedule_id': str(schedule_id),
		'schedule_args': {
			'ymd': ymd,
			'start_date': sch['startDate'],
			'end_date': sch['endDate'],
			'rk_use': sch['rkUse'],
			'adprovider_ids': id_arr_to_csv(sch['adProvider_ids']),
			'publisher_ids': id_arr_to_csv(sch['publisher_ids']),
		},
		'trigger_by': trigger_by
	}

	logging.info(f'trigger DAG ready: dag_id={dag_id}, dag_run_id={dag_run_id}')

	endpoint = f'api/v1/dags/{dag_id}/dagRuns'
	payload = dict(
		dag_run_id=dag_run_id,
		logical_date=str(logical_date),
		conf=conf,
	)

	api_conn = HttpHook('POST', CONN_AIRFLOW_WEB)
	retry_args = dict(
		wait=tenacity.wait.wait_fixed(30),
		stop=tenacity.stop.stop_after_attempt(4),
		retry=tenacity.retry_if_exception_type(requests.ConnectionError)
	)
	res: requests.Response = api_conn.run_with_advanced_retry(
		retry_args,
		endpoint,
		extra_options=dict(check_response=True),
		json=payload,
	)
	logging.info(f'triggered DAG: {endpoint}\n{res.text}')

	return dag_id, dag_run_id


def fetch_schedule_args(schedule_id: str):
	"""
	schedule_id 에 해당하는 ymd, start_date, end_date, rk_use, adprovider_ids, publisher_ids 조회

	:param schedule_id:
	:return:
	"""

	with MongoHook(CONN_MONGO_FOR_CMS) as hook:
		coll = hook.get_conn().get_database()[COLLECTION]

		schedule = coll.find_one(ObjectId(schedule_id),
								 ['ymd', 'startDate', 'endDate', 'rkUse', 'adProvider_ids', 'publisher_ids'])

		return dict(
			ymd=schedule['ymd'],
			start_date=schedule['startDate'],
			end_date=schedule['endDate'],
			rk_use=schedule['rkUse'],
			adprovider_ids=id_arr_to_csv(schedule['adProvider_ids']),
			publisher_ids=id_arr_to_csv(schedule['publisher_ids'])
		)


def _update_schedule(_id: ObjectId, set_values: dict):
	"""
	_id 에 해당하는 스케쥴 상태 정보 업데이트

	:param _id: 스케쥴 id
	:param set_values: state / jobStatus / cmsState / cmsJobStatus
	:return:
	"""

	with MongoHook(CONN_MONGO_FOR_CMS) as hook:
		coll = hook.get_conn().get_database()[COLLECTION]

		field = 'UNKNOWN FIELD'
		state = 'UNKNOWN STATE'

		# Deprecated - CMS 집계 걷어낼 때, 같이 제거할 것. 그때는 state 만 존재함
		if 'state' in set_values:
			field = 'state'
			state = set_values[field]
		elif 'cmsState' in set_values:
			field = 'cmsState'
			state = set_values[field]

		res = coll.update_one(
			{'_id': _id},
			{'$set': set_values}
		)

		if res.modified_count == 1:
			logging.info(f'{COLLECTION}: _id={_id}: Updated: {field}={state}')
		else:
			logging.info(f'{COLLECTION}: _id={_id}: Not updated: {field}={state}')


def update_state_to_wait(
		ymd: str,
		report_api_type: str,
		adprovider_id: str,
		publisher_id: str
):
	"""
	AP Summary 스케쥴 스파크 집계를 위해 state 를 READY -> WAIT 으로 변경
		- Silvergrey NonRk GFP 집계 완료 후 호출됨
			- AP, REVENUE, AP_AMBER
			- GFP, GFP_PK, GFP_AMBER
	:param ymd:
	:param report_api_type:
	:param adprovider_id:
	:param publisher_id:
	:return:
	"""

	with MongoHook(CONN_MONGO_FOR_CMS) as hook:
		coll = hook.get_conn().get_database()[COLLECTION]

		query = {
			'ymd': ymd,
			'reportApiType': report_api_type,
			'state': ST_READY,
			'$or': [
				{
					'summaryType': {'$in': [SMTYP_AP, SMTYP_REVENUE, SMTYP_AP_AMBER]},
					'adProvider_ids': [ObjectId(adprovider_id)],
					'publisher_ids': [ObjectId(publisher_id)],
				},
				{
					'summaryType': {'$in': [SMTYP_GFP, SMTYP_GFP_PK, SMTYP_GFP_AMBER]}
				},
			],
		}

		updates = {
			'$set': {'state': ST_WAIT}
		}

		return coll.update_many(query, updates).modified_count


def update_state_to_launch(dag_id: str, context: dict):
	"""
	state = LAUNCH 로 설정
	jobStatus.dagId, jobStatus.dagRunId, jobStatus.launchedAt 설정

	:param dag_id:
	:param context:
	:return:
	"""

	ti = context['ti']
	schedule_id = ti.xcom_pull(key=XCOM_SCHEDULE_ID)

	# Deprecated - CMS 집계 걷어낼 때, 같이 제거할 것. 그때는 state 만 존재하기 때문에 complete 체크 안해도 됨
	# if schedule_id
	if schedule_id and not is_state_complete('state', schedule_id):
		updates = {
			'state': ST_LAUNCH,
			'jobStatus.dagId': dag_id,
			'jobStatus.dagRunId': context['run_id'],
			'jobStatus.launchedAt': context['logical_date'],
		}
		_update_schedule(ObjectId(schedule_id), updates)
		logging.info(f'schedule({schedule_id}) state set to {ST_LAUNCH}')


def update_state_to_in_progress(context: dict):
	"""
	state = IN_PROGRESS 로 설정
	jobStatus.yarnAppId, jobStatus.yarnTrackingUrl, jobStatus.submittedAt 설정
		- create_task_group 에서 호출됨
			- spark_submit_cb >> conclude_job.on_success_callback
	:param context:
	:return:
	"""

	ti = context['ti']
	schedule_id = ti.xcom_pull(key=XCOM_SCHEDULE_ID)

	if schedule_id:
		now = pendulum.now()

		tracking_url = ti.xcom_pull(task_ids=ti.task_id, key='tracking_url')
		app_id = ti.xcom_pull(task_ids=ti.task_id, key='app_id')

		updates = {
			'state': ST_IN_PROGRESS,
			'begunAt': now,
			'jobStatus.yarnAppId': app_id,
			'jobStatus.yarnTrackingUrl': tracking_url,
			'jobStatus.submittedAt': now,
		}
		_update_schedule(ObjectId(schedule_id), updates)


def update_state_to_complete(success: bool, context: dict):
	"""
	스케줄의 state 를 COMPLETE / FAILURE 로 설정
		- create_task_group 에서 호출됨
			- wait_showup_failure_cb >> wait_for_spark_app_showup.on_failure_callback
			- wait_complete_failure_cb >> wait_for_spark_app_complete.on_failure_callback
			- conclude_app_success_cb >> conclude_yarn_app.on_success_callback
			- conclude_app_failure_cb >> invoke_spark_submit_job.on_failure_callback
										 wait_for_job_done.on_failure_callback
										 conclude_yarn_app.on_failure_callback
	:param success:
	:param context:
	:return:
	"""

	schedule_id = context["ti"].xcom_pull(key=XCOM_SCHEDULE_ID)

	logging.info(f'schedule ID: {schedule_id}')

	if schedule_id:
		now = pendulum.now()

		state = ST_COMPLETE if success else ST_FAILURE

		updates = {
			'state': state,
			'endedAt': now,
			'jobStatus.completedAt': now,
		}

		_update_schedule(ObjectId(schedule_id), updates)


def _fetch_state_cms_state_by_id(schedule_id: str):
	"""
	[Deprecated] state, cmsState 조회

	:param schedule_id:
	:return:
	"""

	with MongoHook(CONN_MONGO_FOR_CMS) as hook:
		coll = hook.get_conn().get_database()[COLLECTION]

		schedule = coll.find_one(ObjectId(schedule_id), ['state', 'cmsState'])

		return schedule


def is_state_complete(state_type, schedule_id: str):
	"""
	[Deprecated]

	state 또는 cmsState 가 COMPLETE 인지 아닌지
	:param state_type: state 또는 cmsState
	:param schedule_id:
	:return:
	"""

	if schedule_id:
		schedule = _fetch_state_cms_state_by_id(schedule_id)

		if schedule[state_type] == ST_COMPLETE:
			return True
		else:
			return False


##############################################################################


# CMS 집계 시 사용. CMS 집계 걷어낼 때 같이 제거할 것

def update_cms_state_to_wait(
		ymd: str,
		report_api_type: str
):
	"""
	[Deprecated] GFP, GFP_PK 대상

	AP Summary 스케쥴 스파크 집계를 위해 cmsState 를 READY -> WAIT 으로 변경
		- Silvergrey NonRk GFP 집계 완료 후 호출됨
			- GFP, GFP_PK

	:param ymd:
	:param report_api_type:
	:param adprovider_id:
	:param publisher_id:
	:return:
	"""

	with MongoHook(CONN_MONGO_FOR_CMS) as hook:
		coll = hook.get_conn().get_database()[COLLECTION]

		query = {
			'ymd': ymd,
			'reportApiType': report_api_type,
			'cmsState': ST_READY,
			'summaryType': {'$in': [SMTYP_GFP, SMTYP_GFP_PK]}
		}

		updates = {
			'$set': {'cmsState': ST_WAIT}
		}

		return coll.update_many(query, updates).modified_count


def update_cms_state_to_launch(dag_id: str, context: dict):
	"""
	[Deprecated] GFP, GFP_PK 대상

	state, cmsState = LAUNCH 로 설정
	cmsJobStatus.dagId, cmsJobStatus.dagRunId, cmsJobStatus.launchedAt 설정
	:param dag_id:
	:param context:
	:return:
	"""

	ti = context['ti']
	schedule_id = ti.xcom_pull(key=XCOM_SCHEDULE_ID)

	if schedule_id:
		updates = {
			'state': ST_LAUNCH,
			'cmsState': ST_LAUNCH,
			'cmsJobStatus.dagId': dag_id,
			'cmsJobStatus.dagRunId': context['run_id'],
			'cmsJobStatus.launchedAt': context['logical_date'],
		}
		_update_schedule(ObjectId(schedule_id), updates)
		logging.info(f'schedule({schedule_id}) cmsState set to {ST_LAUNCH}')


def update_cms_state_to_in_progress(context: dict):
	"""
	[Deprecated] GFP, GFP_PK 대상

	state, cmsState = IN_PROGRESS 로 설정
	begunAt, cmsJobStatus.yarnAppId, cmsJobStatus.yarnTrackingUrl, cmsJobStatus.submittedAt 설정
		- create_task_group 에서 호출됨
			- spark_submit_cb >> conclude_job.on_success_callback
	:param context:
	:return:
	"""

	ti = context['ti']
	schedule_id = ti.xcom_pull(key=XCOM_SCHEDULE_ID)

	if schedule_id:
		now = pendulum.now()

		tracking_url = ti.xcom_pull(task_ids=ti.task_id, key='tracking_url')
		app_id = ti.xcom_pull(task_ids=ti.task_id, key='app_id')

		updates = {
			'state': ST_IN_PROGRESS,
			'cmsState': ST_IN_PROGRESS,
			'begunAt': now,
			'cmsJobStatus.yarnAppId': app_id,
			'cmsJobStatus.yarnTrackingUrl': tracking_url,
			'cmsJobStatus.submittedAt': now,
		}
		_update_schedule(ObjectId(schedule_id), updates)


def update_cms_state_to_complete(success: bool, context: dict):
	"""
	[Deprecated] GFP, GFP_PK 대상

	state, cmsState = COMPLETE / FAILURE 로 설정
	endedAt, cmsJobStatus.completedAt 설정
		- create_task_group 에서 호출됨
			- wait_showup_failure_cb
			- wait_complete_failure_cb
			- conclude_app_success_cb
			- conclude_app_failure_cb
	:param success:
	:param context:
	:return:
	"""

	schedule_id = context["ti"].xcom_pull(key=XCOM_SCHEDULE_ID)

	if schedule_id:
		now = pendulum.now()

		state = ST_COMPLETE if success else ST_FAILURE

		updates = {
			'state': state,
			'cmsState': state,
			'endedAt': now,
			'cmsJobStatus.completedAt': now,
		}

		_update_schedule(ObjectId(schedule_id), updates)
