"""
### ap summary REVENUE 스파크 집계

#### 1. 위키
- [01. AP 집계](https://wiki.navercorp.com/pages/viewpage.action?pageId=**********)

#### 2. 저장소
- Data DB : AdProviderRevenueDaily

#### 3. config
- schedule_id
- start_date : YYYYMMDD
- end_date : YYYYMMDD
- rk_use : 1(rk) or 0(nonrk)
- adprovider_ids : 광고공급자 id 리스트 (oid,oid,oid,..)
- publisher_ids : 매체 id 리스트 (oid,oid,oid,..)
"""

import os
import pendulum

from functools import partial

from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator

from core import utils
from core.base import DEFAULT_TZ, PROFILE, ALERT_EMAIL_ADDRESSES, \
	PROFILE_LOCAL, PROFILE_DEV, PROFILE_TEST, PROFILE_STAGE, PROFILE_REAL, SPARK_SUBMIT_OPTIONS, \
	POOL_SPARK_AP, SPARKLING_APP_JAR_PATH, SPARKLING_IMAGE, \
	SPARKLING_APP_HARD_LIMIT_4 as SPARKLING_APP_HARD_LIMIT

from core.spark_pool import POOL_AP_SLOT_TRIVIAL_5
from core.spark_task_group import create_task_group
from core.spark_submit_op import invoke_job_with_args

from biz.ap_summary.ap_summary_schedule_dao import fetch_schedule_args, \
	update_state_to_launch, update_state_to_in_progress, update_state_to_complete

from biz.ap_summary.ap_summary_base import init_settings, print_settings, \
	XCOM_START_DATE, XCOM_END_DATE, XCOM_RK_USE, XCOM_ADPROVIDER_IDS, XCOM_PUBLISHER_IDS

_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [SPARK-AP-SUMMARY-REVENUE]'

# Spark
_SPARK_APP_CLASS = 'com.navercorp.gfp.biz.adprovider.AdProviderRevenueDailyAggregator'

# Profile
_PROFILE_SETTINGS = {
	PROFILE_TEST: {
		SPARK_SUBMIT_OPTIONS: """
			--num-executors 1
			--executor-cores 4
			--executor-memory 500m
			--conf spark.sql.shuffle.partitions=5
			""".split()
	},
	PROFILE_REAL: {
		SPARK_SUBMIT_OPTIONS: """
			--num-executors 1
			--executor-cores 4
			--executor-memory 500m
			--conf spark.sql.shuffle.partitions=5
			""".split()
	}
}
_PROFILE_SETTINGS[PROFILE_LOCAL] = _PROFILE_SETTINGS[PROFILE_TEST]
_PROFILE_SETTINGS[PROFILE_DEV] = _PROFILE_SETTINGS[PROFILE_TEST]
_PROFILE_SETTINGS[PROFILE_STAGE] = _PROFILE_SETTINGS[PROFILE_REAL]


def _setup(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화 및 로깅

	:param context:
	:return:
	"""

	init_settings(fetch_schedule_args, **context)
	print_settings(
		pool_spark=POOL_SPARK_AP,
		pool_slot=POOL_AP_SLOT_TRIVIAL_5,
		profile=PROFILE,
		spark_submit_options=_PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS],
		**context)

	# state 가 COMPLETE 이 아닌 경우, LAUNCH 로 변경
	update_state_to_launch(_DAG_ID, context)


def _get_app_args(context: dict):
	"""
	spark-submit 시, 넘겨줄 파라미터 정보
	:param context:
	:return:
	"""
	return [
		context["ti"].xcom_pull(key=XCOM_START_DATE),
		context["ti"].xcom_pull(key=XCOM_END_DATE),
		str(context["ti"].xcom_pull(key=XCOM_RK_USE)),
		context["ti"].xcom_pull(key=XCOM_ADPROVIDER_IDS),
		context["ti"].xcom_pull(key=XCOM_PUBLISHER_IDS)
	]


with DAG(
		_DAG_ID,
		description='Invoke spark app for ap summary REVENUE',
		tags=['spark', 'ap_summary.spark', 'ap_summary', 'ap_summary.ap', 'daily', 'data_db', 'bitna.cho'],
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		params={
			'schedule_id': '',
			'schedule_args': {
				'ymd': '',
				'start_date': '',
				'end_date': '',
				'rk_use': None,
				'adprovider_ids': '',
				'publisher_ids': '',
			}
		},
		start_date=pendulum.datetime(2023, 1, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	task_group_id = 'spark_run_ap_summary_revenue'
	spark_run_ap_summary_revenue = create_task_group(
		task_group_id,
		tooltip='AP Summary: ' + _SPARK_APP_CLASS,
		spark_pool=POOL_SPARK_AP,
		pool_slot=POOL_AP_SLOT_TRIVIAL_5,
		tz=DEFAULT_TZ,
		summary_history_kwargs={
			'aggregator_name': _SPARK_APP_CLASS,
			'dt': f'{{{{ ti.xcom_pull(key="{XCOM_END_DATE}") }}}}',
			'dag_id': _DAG_ID,
			'dag_run_id': '{{ run_id }}',
			'dag_run_url': f'{utils.get_dag_run_url(_DAG_ID, "{{ run_id }}")}',
			'detail': {
				'startDate': f'{{{{ ti.xcom_pull(key="{XCOM_START_DATE}") }}}}',
				'endDate': f'{{{{ ti.xcom_pull(key="{XCOM_END_DATE}") }}}}',
				'rkUse': f'{{{{ ti.xcom_pull(key="{XCOM_RK_USE}") }}}}',
				'adProviderIds': utils.csv_to_str_arr(f'{{{{ ti.xcom_pull(key="{XCOM_ADPROVIDER_IDS}") }}}}'),
				'publisherIds': utils.csv_to_str_arr(f'{{{{ ti.xcom_pull(key="{XCOM_PUBLISHER_IDS}") }}}}'),
			}
		},
		invoke_job_callable=invoke_job_with_args,
		invoke_job_kwargs={
			'task_group_id': task_group_id,
			'image': SPARKLING_IMAGE,
			'app_jar': SPARKLING_APP_JAR_PATH,
			'app_class': _SPARK_APP_CLASS,
			'app_args_fn': _get_app_args,
			'spark_submit_options': _PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS],
			'tz': DEFAULT_TZ,
			'alt_datetime': f'{{{{ ti.xcom_pull(key="{XCOM_END_DATE}") }}}}',
		},
		spark_app_execution_limit=SPARKLING_APP_HARD_LIMIT,
		spark_submit_cb=partial(update_state_to_in_progress),
		wait_showup_failure_cb=partial(update_state_to_complete, False),
		wait_complete_failure_cb=partial(update_state_to_complete, False),
		conclude_app_success_cb=partial(update_state_to_complete, True),
		conclude_app_failure_cb=partial(update_state_to_complete, False),
	)

	setup >> spark_run_ap_summary_revenue
