"""
### nubes 백업/복구 관련 배치서버 호출 재처리 트리거 DAG

#### 위키
- [91-2. HDFS 데이터 Nubes 백업/복구/삭제 재처리](https://wiki.navercorp.com/pages/viewpage.action?pageId=2053802051)

#### 주기 및 스케줄
- 10분 간격
- Data DB : Jobs
  - Job 별로 가장 과거 날짜의 처리가 끝나기 전까지 다음 날짜를 처리하지 못한다.
  - 일반 정규 처리 Job 보다 우선순위를 가진다. (여러 시간대/파일타입에 대해 동시 실행에 제한이 있음)
- max_active_runs=1

#### 스케쥴 콜렉션
- Data DB : Jobs

#### 트리거 대상 DAG
- request_backup_restore_nubes
- Data DB : Environments > backup-nubes-config (참고)

#### config
- 없음
"""

import logging
import os
from datetime import timedelta
from time import sleep

import pendulum
from airflow import AirflowException
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator, BranchPythonOperator
from airflow.utils.email import send_email
from airflow.utils.state import DagRunState
from airflow.utils.types import DagRunType

from biz.backup_restore.request_backup_restore_nubes import DAG_ID as _TARGET_DAG_ID, JOB_TYPE, XCOM_TARGET_METHOD
from biz.backup_restore.trigger_backup_restore_nubes_regular import JOB_TRIGGER_TIMEOUT_HOURS
from core.airflow_api import trigger_dagrun
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_DATA, XCOM_TARGET_DT
from core.dao import job_dao
from core.dao.environment_dao import get_nubes_backup_data_types

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [TRG-BACKUP-RESTORE-NUBES-REPROCESS]'

_XCOM_TARGET_JOBS = 'target_jobs'


def _setup(**context):
	"""
	method, data_type 별 가장 오래된 실패 이력 1건만 가져와서 재실행 여부 결정 설정
	:param context:
	:return:
	"""
	method_cnt = _set_target_jobs(**context)
	next_task = []

	for method, cnt in method_cnt.items():
		if cnt > 0:
			next_task.append(f'trigger_request_nubes_{method}')

	if len(next_task) == 0:
		_conclude(**context)

	return next_task


def _set_target_jobs(**context):
	skipped_list = []
	target_jobs = []
	method_cnt = {method: 0 for method in JOB_TYPE.keys()}

	data_types = get_nubes_backup_data_types()
	for method_type in JOB_TYPE.keys():
		for data_type in data_types:
			job_type = JOB_TYPE[method_type].format(data_type=data_type)
			# 첫 정규 실행 중에 재처리 트리거가 실행되면 정규로 실행되고 있는 running job(실버) 이 끝나길 기다리다가 끝나는 순간,
			# 첫 정규 트리거로 실행되어야 할 [retryCnt=-1, running=0] 인 job(지르콘) 를 중복으로 실행하게 된다. 이를 방지하기 위해
			# 1) retryCnt >= 0 인 경우에만 재처리 대상으로 삼고, (-1 이 최초 실행)
			# 2) regular: _wait_until_can_run() / reprocess: _wait_prev_job_done() 를 사용한다.
			job = job_dao.get_job({'type': job_type, 'retryCnt': {'$gte': 0}, 'manual': 0})
			if job:
				if job['retryCnt'] >= job_dao.MAX_RETRY_CNT:
					# monitor_jobs 에서 알림 메일 발송하기 때문에, 별도 처리 하지 않음
					logging.warning(
						f'{_LOG_PREFIX} {job["datetime"]}의 retryCnt={job["retryCnt"]}. 최대처리횟수({job_dao.MAX_RETRY_CNT})에 도달해서 스킵.')
					skipped_list.append(
						(job_type, job['datetime'], f'최대처리횟수({job_dao.MAX_RETRY_CNT})에 도달해서 스킵'))

				elif job['running'] == 1:
					logging.warning(f'{_LOG_PREFIX} {job}:{job["datetime"]} 이미 처리 중이므로 스킵.')
					skipped_list.append((job_type, job['datetime'], '이미 처리 중'))

				else:
					target_jobs.append({'type': job['type'], 'datetime': job['datetime']})
					method_cnt[method_type] += 1
			else:
				logging.info(f'{_LOG_PREFIX} {job_type} 실패된 job 없으므로 재처리 안함')

	context['ti'].xcom_push('skipped_list', skipped_list)
	context['ti'].xcom_push(_XCOM_TARGET_JOBS, target_jobs)

	return method_cnt


def _trigger_request_nubes_job(method, **context):
	"""
	재처리 대상인 reprocess jobs 에 대해 순차적으로 request_backup_restore_nubes DAG 트리거시킴
	_setup 단계에서 대상이 되는 Job 이 수동 실행인지, 실행중인지 확인했기에 실행만 시키면 됨
	method 에 따라 각 branch 들이 실행 시키는 job_type 이 다름

	실패된 Job 은 DagRun 의 마지막 Task 에서 메일을 보내 알림
	:param method:
	:param context:
	:return:
	"""
	failed_list = []
	target_jobs = context['ti'].xcom_pull(key=_XCOM_TARGET_JOBS)

	# 대상 시간대 목록을 돌면서
	jobs_len = len(target_jobs)
	for idx, target_job in enumerate(target_jobs):
		job_type = target_job['type']

		if method not in job_type:
			continue

		data_type = job_type.split('_')[-1]  # job['type'] 의 suffix 가 곧 data_type
		target_dt = target_job['datetime']

		logging.info(f'{_LOG_PREFIX} {job_type}:{target_dt} 시작 ({idx + 1}/{jobs_len})')
		dag_run_id = None

		try:
			_wait_prev_job_done(job_type)

			# 실행 전 Jobs collection 에 있는지 확인, 없으면 정상 처리되어 지워진 것
			job = job_dao.get_job({'type': job_type, 'datetime': target_dt, 'manual': 0, 'running': 0,
								   'retryCnt': {'$lt': job_dao.MAX_RETRY_CNT}})
			if job is None:
				continue

			# 특정 데이터 타입, 시간대의 파일 백업 요청
			dag_run_id = trigger_dagrun(
				dag_id=_TARGET_DAG_ID,
				logical_date=pendulum.now(DEFAULT_TZ),
				conf={
					XCOM_TARGET_METHOD: job_type.split('_')[1],  # job_type='request_{method_type}_nubes_{data_type}'
					XCOM_TARGET_DATA: data_type,
					XCOM_TARGET_DT: target_dt,
					'force': False,
				},
				dag_run_type=DagRunType.SCHEDULED
			)
			logging.info(f'{_LOG_PREFIX} {job_type}:{target_dt}({dag_run_id}) 트리거시킴')

			# dag이 끝났는지 주기적으로 확인
			while True:
				sleep(180)

				dag_runs = DagRun.find(dag_id=_TARGET_DAG_ID, run_id=dag_run_id)

				if dag_runs:
					dag_run: DagRun = dag_runs.pop()
					if dag_run.get_state() == DagRunState.SUCCESS:
						logging.info(f'{_LOG_PREFIX} {job_type}:{target_dt}({dag_run_id}) 정상 종료')
						break
					elif dag_run.get_state() == DagRunState.FAILED:
						# 실패한 job 의 retryCnt 는 request_backup_resotre_nubes DAG 에서 처리
						logging.info(f'{_LOG_PREFIX} {job_type}:{target_dt}({dag_run_id}) 실패')
						failed_list.append((job_type, target_dt, f'dag_run_id={dag_run_id}'))
						break
				else:
					failed_list.append((job_type, target_dt, f'dag_run_id={dag_run_id} 트리거 시켰으나, 존재하지 않음'))
					raise AirflowException(f'{job_type}:{target_dt}({dag_run_id}) 트리거 시켰으나, 존재하지 않음')

		except Exception as e:
			job_dao.clean_up(job_type, target_dt, False)
			logging.error(f'{_LOG_PREFIX} {job_type}:{target_dt}({dag_run_id}) 트리거 중에 에러 발생하여 실패처리(retryCnt 증가)\n{e}')

	logging.info(f'{_LOG_PREFIX} 완료')

	context['ti'].xcom_push(f'{method}_failed_list', failed_list)


def _wait_prev_job_done(job_type):
	"""
	재처리 트리거도 이미 실행 중인 정규 Job 이 있다면 데이터 타입에 상관없이 대기한다.
	백업과 복구/삭제를 담당하는 배치 서버가 다르므로 같은 작업들 끼리의 동시 실행만 피함.

	재처리 할 Job 이 없어 정규 Job 이 시작되고, 그 도중에 재처리 할 Job 이 생겨 대기하는 경우..
	ex) 정규 처리 시 실버가 실패, 지르콘 진행 중일 때는 정규 지르콘이 끝나고 실버를 재처리

	:param job_type:
	:return:
	"""
	while True:
		idx_before_date_type = job_type.rindex('_')
		running_job = job_dao.get_job(
			{'type': {'$regex': f'^{job_type[:idx_before_date_type]}'}, 'manual': 0, 'running': 1})
		is_exist_running_job = running_job is not None

		if is_exist_running_job:
			logging.info(
				f'{_LOG_PREFIX} 현재 실행 중인 다른 백업 작업이 존재하므로 대기. {running_job["type"]}:{running_job["datetime"]} running= {running_job["running"]} retryCnt= {running_job["retryCnt"]}')

			sleep(180)
		else:
			# trigger_backup_restore_nubes_regular.py 의 _wait_until_can_run() 에 의해
			# 정규/재처리가 모두 대기 중이라면 실행중이던 Job 이 끝났을 때 우선 순위를 갖는 것은 job['datetime'] 이 빠른 재처리 job 이다.
			return


def _conclude(**context):
	"""
	스킵 또는 실패된 리스트 알림
	:param context:
	:return:
	"""
	cnt_skip = 0
	cnt_fail = 0

	skipped_msg = '[skipped_list]<br/>'
	failed_msg = '[failed_list]<br/>'

	skipped_list = context['ti'].xcom_pull(key='skipped_list')
	if skipped_list:
		for item in skipped_list:
			skipped_msg += f'&nbsp;&nbsp;&nbsp;&nbsp; job_type:{item[0]} datetime:{item[1]} reason:{item[2]}<br/>'
			cnt_skip += 1

	for method in JOB_TYPE.keys():
		failed_list = context['ti'].xcom_pull(key=f'{method}_failed_list')
		if failed_list:
			for item in failed_list:
				failed_msg += f'&nbsp;&nbsp;&nbsp;&nbsp; job_type:{item[0]} datetime:{item[1]} reason:{item[2]}<br/>'
				cnt_fail += 1

	if cnt_skip or cnt_fail:
		title = f'[Nubes 백업/삭제 재처리 실행에 스킵 또는 실패 건이 있음] 스킵 건수={cnt_skip} 실패 건수={cnt_fail}'
		body = f'{skipped_msg}<br/>{failed_msg}<br/>'
		send_email(ALERT_EMAIL_ADDRESSES, title, body)

		if cnt_fail:
			raise AirflowException(f'{_LOG_PREFIX} Nubes 백업/삭제 재처리 실행 중에 실패가 발생했습니다.')


with DAG(
		_DAG_ID,
		description='Trigger reqeust DAG to execute nubes job by reprocess',
		tags=['trigger', 'reprocess', 'backup', 'restore', 'delete', 'nubes', 'silver', 'ins.cho'],
		default_args={
			'owner': 'ins.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2024, 1, 1, tz=DEFAULT_TZ),
		schedule_interval='*/10 * * * *',  # 10분 간격
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	# 처리해야 할 job이 있는 method 의 task 만 브랜치로 진행
	# Jobs 에서 method, data_type 별 가장 오래된 실패이력의 시간대만 추출
	setup = BranchPythonOperator(
		task_id='branch_nubes_job',
		python_callable=_setup,
	)

	# 타입별 가장 과거의 datetime 을 가진 job 별로 백업 재처리
	trigger_request_nubes_backup = PythonOperator(
		task_id=f'trigger_request_nubes_backup',
		python_callable=_trigger_request_nubes_job,
		op_args=[[*JOB_TYPE][0]],  # backup
		execution_timeout=timedelta(hours=JOB_TRIGGER_TIMEOUT_HOURS)
	)

	# 타입별 가장 과거의 datetime 을 가진 job 별로 복구 재처리
	trigger_request_nubes_restore = PythonOperator(
		task_id=f'trigger_request_nubes_restore',
		python_callable=_trigger_request_nubes_job,
		op_args=[[*JOB_TYPE][1]],  # restore
		execution_timeout=timedelta(hours=JOB_TRIGGER_TIMEOUT_HOURS)
	)

	# 타입별 가장 과거의 datetime 을 가진 job 별로 삭제 재처리
	trigger_request_nubes_delete = PythonOperator(
		task_id=f'trigger_request_nubes_delete',
		python_callable=_trigger_request_nubes_job,
		op_args=[[*JOB_TYPE][2]],  # delete
		execution_timeout=timedelta(hours=JOB_TRIGGER_TIMEOUT_HOURS)
	)

	# 스킵 또는 실패된 리스트 알림
	conclude = PythonOperator(
		task_id='conclude',
		python_callable=_conclude,
	)

	# 파이프라인
	setup >> [trigger_request_nubes_backup, trigger_request_nubes_restore, trigger_request_nubes_delete] >> conclude
