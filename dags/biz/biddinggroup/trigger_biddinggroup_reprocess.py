"""
### GFP/NAM Bidding Group 재처리 트리거

#### 위키
- [12-2. GFP/NAM 비딩그룹 재처리](https://wiki.navercorp.com/spaces/GFP/pages/3608235267)

#### 개요
- Jobs에 처리되지 않고 남아 있는 job 에 대해 자동으로 재처리.
- spark_nam_biddinggroup, spark_gfp_biddinggroup DAG 을 실행
  - 한 번 실행 시 각 spark DAG 별로 하나만 처리
  - nam 은 하나의 yyyyMMdd, gfp 는 하나의 yyyyMMddHH
- 본 DAG 은 동시에 1개의 dag run 만 존재할 수 있음. 중복 실행 불가

#### 주기
- 10분
- 실버로그의 재처리 따른 NAM bidding Group 재처리가 필요한 경우가 있는데 이 경우,
  make_silver_trace -> trace_silver_for_nam_bg 에 의해 SPARK_DAG_ID_NAM_BG job 이 생기며 본 DAG 에서 해당 job 을 실행함

#### config
- 없음
"""
import json
import logging
import os

import pendulum
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import BranchPythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.utils.types import DagRunType

from biz.biddinggroup.spark_gfp_biddinggroup import DAG_ID as SPARK_DAG_ID_GFP_BG
from biz.biddinggroup.spark_nam_biddinggroup import DAG_ID as SPARK_DAG_ID_NAM_BG
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YMDH, XCOM_TARGET_YMD, XCOM_KIND, \
	XCOM_SILVER_TRACE_ID
from core.dao import job_dao

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = ".......... [TRG-GFP-NAM-BG-REPROCESS]"

_KIND = 'reprocess'


def _get_jobs(**context):
	"""
	실행 대상 job 조회.

	:param context:
	:return:
	"""
	next_task = []
	jobs = job_dao.get_jobs({'type': {'$in': [SPARK_DAG_ID_GFP_BG, SPARK_DAG_ID_NAM_BG]},
	                         'manual': 0,
	                         'running': 0,
	                         'retryCnt': {'$gt': -1, '$lt': job_dao.MAX_RETRY_CNT}
	                         })
	if jobs:
		exist_jobs = {}
		for job in jobs:
			# nam 비딩그룹은 datetime 외의 detail 정보가 필요하므로 추출
			# {SPARK_DAG_ID_GFP_BG: [{job1}, {job2}, ...], SPARK_DAG_ID_NAM_BG: [{job3}, {job4}, ...]}
			job_info = {k: v for k, v in job.items() if k in ['datetime', 'detail']}
			if s_trace_id := job_info['detail'].get('silverTrace_id'):
				job_info['detail']['silverTrace_id'] = str(s_trace_id)
			exist_jobs.setdefault(job['type'], []).append(job_info)

		if SPARK_DAG_ID_GFP_BG in exist_jobs:
			next_task.append(trigger_gfp_bg.task_id)
			# gfp 비딩그룹은 같은 날짜에 대해 앞선 시간대를 모두 포함하여 집계되므로, (yyyyMMdd: 오름차순 -> hh:내림차순) 정렬
			# 따라서 같은 날짜에 대해 늦은 시간대부터 집계하게 되고, spark_gfp_biddinggroup DAG 에서 앞선 시간대의 job 은 삭제하여 중복 회피
			exist_jobs[SPARK_DAG_ID_GFP_BG].sort(key=lambda j: (j['datetime'][:8], -int(j['datetime'][8:])))
			target_job = exist_jobs[SPARK_DAG_ID_GFP_BG][0]
			context['ti'].xcom_push(key=SPARK_DAG_ID_GFP_BG, value=target_job['datetime'])

		if SPARK_DAG_ID_NAM_BG in exist_jobs:
			next_task.append(trigger_nam_bg.task_id)
			# nam 비딩그룹은 특정 ymd 에 대해 한번만 실행해도 됨
			target_job = exist_jobs[SPARK_DAG_ID_NAM_BG][0]
			# 조건에 따른 XCOM_SILVER_TRACE_ID key 에 대한 값을 아예 포함시키지 않고 싶은데,
			# ti.xcom_pull 과 dict unpacking 표현을 TriggerDagRunOperator.conf 에서 직접 사용할 수 없어 conf 용 xcom 변수 미리 설정
			xcom_conf_for_nam_job = {
				XCOM_TARGET_YMD: target_job['datetime'],
				XCOM_KIND: target_job['detail']['kind'],
				**({XCOM_SILVER_TRACE_ID: target_job['detail']['silverTrace_id']}
				   if target_job['detail'].get('silverTrace_id') else {})
			}
			context['ti'].xcom_push(key=SPARK_DAG_ID_NAM_BG, value=json.dumps(xcom_conf_for_nam_job))

	else:
		logging.info(f'{_LOG_PREFIX} 처리할 job 없음')

	return next_task


with DAG(
		_DAG_ID,
		description='GFP/NAM 비딩그룹 지표를 위한 자동 재처리 DAG',
		default_args={
			'owner': 'ins.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2025, 5, 1, tz=DEFAULT_TZ),
		schedule_interval="*/10 * * * *",
		tags=['trigger', 'reprocess', 'gfp', 'nam', 'biddinggroup', 'bg', 'hourly', 'daily', 'ins.cho'],
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	# 처리해야 할 job이 있을 때만 다운스트림 태스크 진행
	# Jobs에서 시간대순으로 하나의 시간대만 추출
	get_jobs = BranchPythonOperator(
		task_id='setup',
		python_callable=_get_jobs,
	)

	# spark_gfp_biddinggroup 트리거
	trigger_gfp_bg = TriggerDagRunOperator(
		task_id=f'trigger_{SPARK_DAG_ID_GFP_BG}',
		trigger_dag_id=SPARK_DAG_ID_GFP_BG,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=False,
		poke_interval=60,
		reset_dag_run=True,
		conf={
			XCOM_TARGET_YMDH: f'{{{{ ti.xcom_pull(key="{SPARK_DAG_ID_GFP_BG}")["datetime"] }}}}'
		}
	)

	# spark_gfp_biddinggroup 트리거
	trigger_nam_bg = TriggerDagRunOperator(
		task_id=f'trigger_{SPARK_DAG_ID_NAM_BG}',
		trigger_dag_id=SPARK_DAG_ID_NAM_BG,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=False,
		poke_interval=60,
		reset_dag_run=True,
		# conf 는 dict 또는 json-string 이 가능한데, dict 를 xcom_push 하면 단순히 xcom_pull 한 변수를 사용할 수 없으나
		# 위에서 json.dumps(xcom_conf_for_nam_job) 값으로 xcom_push 했으므로 사용 가능
		conf=f'{{{{ ti.xcom_pull(key="{SPARK_DAG_ID_NAM_BG}") }}}}'
	)

	get_jobs >> [trigger_gfp_bg, trigger_nam_bg]
