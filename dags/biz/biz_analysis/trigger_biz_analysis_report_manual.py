"""
### 비즈 성과 분석 리포트 수동 트리거 DAG

#### 위키
- [16-3. 비즈 성과 분석 리포트 생성 수동 처리](https://wiki.navercorp.com/pages/viewpage.action?pageId=2196977315)

#### 주기
- 없음

#### 스케쥴 콜렉션
- Data DB : Jobs
- manual=1 인 경우만 처리 대상임

#### 트리거 대상 DAG
- spark_biz_analysis_report

#### config
- target_ymd_list : 처리할 스케줄 날짜 리스트 ("YYYYMMDD,YYYYMMDD,YYYYMMDD")
"""

import logging
import os
from datetime import timedelta
from time import sleep

import pendulum
from airflow.exceptions import AirflowException
from airflow.models import DAG, DagRun
from airflow.operators.python import PythonOperator
from airflow.utils.email import send_email
from airflow.utils.state import DagRunState
from airflow.utils.types import DagRunType

from biz.biz_analysis.spark_biz_analysis_report import _DAG_ID as SPARK_DAG_ID
from core.airflow_api import trigger_dagrun
from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, XCOM_TARGET_YMD_LIST
from core.dao import job_dao

# DAG 기본정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRG-BIZ-ANLYS-REPORT-MANUAL]'


def _setup(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화

	:param context:
	:return:
	"""
	target_ymd_list_csv = context['params'].get(XCOM_TARGET_YMD_LIST)

	if target_ymd_list_csv:
		logging.info(f'{_LOG_PREFIX} target_ymd_list_str={target_ymd_list_csv}')

		target_ymd_list_csv = target_ymd_list_csv.replace(' ', '').strip(',')
		target_ymd_list = target_ymd_list_csv.split(',')

		for ymd in target_ymd_list:
			pendulum.from_format(ymd, 'YYYYMMDD')

	else:
		raise AirflowException(f'target_ymd_list is empty')

	context['ti'].xcom_push(XCOM_TARGET_YMD_LIST, target_ymd_list)


def _trigger_biz_analysis_report(**context):
	"""
	target_ym_list 에 있는 날짜에 대해 스파크 집계 처리함

	:param context:
	:return:
	"""

	# 스킵, 실패한 스케쥴 리스트
	skipped_list = []
	failed_list = []

	target_ymd_list = context['ti'].xcom_pull(key=XCOM_TARGET_YMD_LIST)

	logging.info(f'{_LOG_PREFIX} target_ymd_list={target_ymd_list}')

	count = len(target_ymd_list)
	for idx, ymd in enumerate(target_ymd_list):
		logging.info(f'{_LOG_PREFIX} [{idx + 1}/{count}] ymd={ymd}')

		# 이미 job이 있는지
		job = job_dao.get_job({'type': SPARK_DAG_ID, 'datetime': ymd})
		if job:
			if job['manual'] == 0:
				# 자동인 경우, 처리 대상 아님
				skipped_list.append((ymd, '스케쥴 DAG 에 의해 처리 예정'))
				logging.warning(f'{_LOG_PREFIX} [{idx + 1}/{count}] ymd={ymd}. 스케쥴 DAG 에 의해 처리 예정')

				continue
			else:
				# 수동인 경우, 이미 실행 중인지 확인
				if job['running'] == 1:
					skipped_list.append((ymd, '이미 처리 중'))
					logging.warning(f'{_LOG_PREFIX} [{idx + 1}/{count}] ymd={ymd}, running=1. 이미 처리 중이므로 스킵')

					continue

		logging.info(f'{_LOG_PREFIX} [{idx + 1}/{count}] ymd={ymd}. 진행중')

		# 해당 날짜의 report 생성 요청
		dag_run_id = trigger_dagrun(SPARK_DAG_ID, pendulum.now(DEFAULT_TZ),
									{'target_ymd': ymd}, DagRunType.MANUAL)
		logging.info(f'{_LOG_PREFIX} 스파크 집계 트리거 ( ymd={ymd}, target_dag_id={SPARK_DAG_ID}, dag_run_id={dag_run_id} )')

		# dag run 이 끝났는지 60초 간격으로 확인
		while True:
			sleep(60)
			dag_runs = DagRun.find(dag_id=SPARK_DAG_ID, run_id=dag_run_id)
			if dag_runs:
				dag_run: DagRun = dag_runs.pop()

				logging.info(f'{_LOG_PREFIX} {SPARK_DAG_ID}({dag_run_id}) state = {dag_run.get_state()}')
				if dag_run.get_state() == DagRunState.SUCCESS:
					break
				elif dag_run.get_state() == DagRunState.FAILED:
					failed_list.append((ymd, f'dag_run_id={dag_run_id}'))
					break
			else:
				failed_list.append((ymd, f'dag_run_id={dag_run_id} 트리거 시켰으나, 존재하지 않음'))
				logging.info(f'{_LOG_PREFIX} {SPARK_DAG_ID}({dag_run_id}) 트리거 시켰으나, 존재하지 않음')
				break

	logging.info(f'{_LOG_PREFIX} 완료')

	context['ti'].xcom_push('skipped_list', skipped_list)
	context['ti'].xcom_push('failed_list', failed_list)


def _alert(**context):
	"""
	스킵 또는 실패된 리스트 알림
	:param context:
	:return:
	"""
	skipped_list = context['ti'].xcom_pull(key='skipped_list')
	skipped_msg = f'skipped {len(skipped_list)} 건 <br/>'

	for item in skipped_list:
		skipped_msg += f'- ymd={item[0]}, reason={item[1]}<br/>'

	failed_list = context['ti'].xcom_pull(key='failed_list')
	failed_msg = f'failed {len(failed_list)} 건 <br/>'
	for item in failed_list:
		failed_msg += f'- ymd={item[0]}, reason={item[1]}<br/>'

	if skipped_list or failed_list:
		title = f'[비즈 성과 분석 리포트 수동 처리] skipped {len(skipped_list)} 건, failed {len(failed_list)} 건'
		body = f'{skipped_msg}<br/>{failed_msg}<br/>'
		send_email(ALERT_EMAIL_ADDRESSES, title, body)


with DAG(
		_DAG_ID,
		description='Trigger manual biz analysis report DAG',
		tags=['trigger', 'manual', 'biz', 'analysis', 'daily', 'hdfs', 'aida', 'cuve', 'ins.cho'],
		default_args={
			'owner': 'ins.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		params={
			XCOM_TARGET_YMD_LIST: '',
		},
		start_date=pendulum.datetime(2023, 9, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup
	)

	trigger_biz_analysis_report = PythonOperator(
		task_id=f'trigger_{SPARK_DAG_ID}',
		python_callable=_trigger_biz_analysis_report,
		retries=3,
		retry_delay=timedelta(seconds=60)
	)

	alert = PythonOperator(
		task_id='alert',
		python_callable=_alert,
	)

	setup >> trigger_biz_analysis_report >> alert
