import pytz
import logging
from datetime import datetime

from bson.objectid import ObjectId
from airflow.exceptions import AirflowException
from airflow.providers.mongo.hooks.mongo import MongoHook

from core.base import CONN_MONGO_FOR_DATA, CONN_MONGO_FOR_CMS


def get_last_datetime(ymd, publisher_id):
    """
    Apollo 매체와 연동된 RK AP 중에서 타임존 offset 이 가장 작은 타임존의 한국시간대 구하기
    :param ymd:
    :param publisher_id:
    :return datetime: YYYYMMDDHH
    """
    year = int(ymd[:4])
    month = int(ymd[4:6])
    day = int(ymd[6:])
    dt = datetime(year, month, day, 23)

    logging.info(f'ymd: {ymd}, publisher_id: {publisher_id}')

    with <PERSON><PERSON><PERSON><PERSON>(CONN_MONGO_FOR_DATA) as hook:
        coll = hook.get_conn().get_database()['SyncAdProviderInfos']

        res = coll.aggregate([
            {'$match': {'publisher_id': ObjectId(publisher_id)}},
            {'$lookup': {
                'from': 'SyncAdProviders',
                'foreignField': '_id',
                'localField': 'adProvider_id',
                'as': 'ap'
            }},
            {'$unwind': {'path': '$ap'}},
            {'$match': {'ap.reportApi.rkUse': 1}},
            {'$group': {'_id': '', 'timezone': {'$addToSet': '$ap.timezone'}}},
        ])

        results = list(res)

        if len(results) != 1:
            raise AirflowException('timezone 정보가 존재하지 않습니다.')

        logging.info(f'results: {results[0]}')

        # tz_map = { -14400000 -> America/New_York, 32400000 -> Asia/Seoul, 0 -> Etc/GMT, 28800000 -> Asia/Shanghai, -25200000 -> America/Los_Angeles }
        tz_map = {}

        for tz in results[0]['timezone']:
            offset = int(pytz.timezone(tz).utcoffset(dt).total_seconds())
            tz_map[offset] = tz

        # tz_map 을 Key 를 기준으로 정렬하여, offset 이 가장 작은 타임존을 찾는다.
        # { -25200000 -> America/Los_Angeles, -14400000 -> America/New_York, 0 -> Etc/GMT, 28800000 -> Asia/Shanghai, 32400000 -> Asia/Seoul }
        index = sorted(tz_map)[0]

        ap_tz = pytz.timezone(tz_map[index])

        # ap_dt = ap 타임존을 적용한 yyyymmdd23 dt
        # local_dt ap_dt 를 GMT+9 로 변환한 dt
        ap_dt = ap_tz.localize(dt)
        local_dt = ap_dt.astimezone(pytz.timezone('Asia/Seoul'))

        logging.info(f'ap_tz: {ap_tz}, ap_dt: {ap_dt}, local_dt: {local_dt}')

        return local_dt.strftime('%Y%m%d%H')


def is_ready_silvergrey_log(ymd, publisher_id):
    """
    매체와 연동된 RK AP 들의 리포트 연동이 완료 되었는지 체크
    :param ymd: YYYYMMDD
    :param publisher_id:
    :return is_ready: Boolean
    """

    report_api_types = get_report_api_types(publisher_id)

    with MongoHook(CONN_MONGO_FOR_CMS) as hook:
        coll = hook.get_conn().get_database()['BatchReportJobSchedules']

        res = list(coll.find({
            'period.endDate': ymd,
            'reportApiType': {'$in': report_api_types},
        }))

        if len(res) == 0:
            raise AirflowException(f'period.endDate({ymd}) 에 해당하는 AP 리포트 연동 스케쥴 정보가 존재하지 않습니다. (reportApiType = {report_api_types})')

        not_complete = list(filter(lambda doc: doc['state'] != 'COMPLETE', res))

        if len(not_complete) == 0:
            return True
        else:
            return False


def get_report_api_types(publisher_id):
    """
    매체와 연동된 RK AP 의 reportApiType 목록 조회
    :param publisher_id:
    :return report_api_types:
    """

    logging.info(f'publisher_id: {publisher_id}')

    with MongoHook(CONN_MONGO_FOR_DATA) as hook:
        coll = hook.get_conn().get_database()['SyncAdProviderInfos']

        res = coll.aggregate([
            {'$match': {'publisher_id': ObjectId(publisher_id)}},
            {'$lookup': {
                'from': 'SyncAdProviders',
                'foreignField': '_id',
                'localField': 'adProvider_id',
                'as': 'ap'
            }},
            {'$unwind': {'path': '$ap'}},
            {'$match': {'ap.reportApi.rkUse': 1}},
            {'$group': {'_id': '', 'reportApiTypes': {'$addToSet': '$ap.reportApi.type'}}},
        ])

        results = list(res)

        if len(results) != 1:
            raise AirflowException(f'publisher_id: {publisher_id} 와 연동 중인 RK AP 정보가 존재하지 않습니다.')

        logging.info(f'results: {results[0]}')

        return results[0]['reportApiTypes']


def check_in_progress(date: str):
    """
    CreatorAdvisorReport 가 처리 중인지 체크 (START, IN_PROGRESS)
    :param date:
    :return:
    """
    car = _get_creator_advisor_report(date)

    if car:
        if car['state'] not in ['COMPLETE', 'FAILURE']:
            return True
        else:
            return False


def check_success(date: str):
    """
    CreatorAdvisorReport 가 성공 처리인지 체크
    :param date:
    :return:
    """
    car = _get_creator_advisor_report(date)

    if car:
        if car['state'] == 'COMPLETE':
            return True
        else:
            return False


def _get_creator_advisor_report(date: str):
    """
    date 에 해당하는 CreatorAdvisorReport 조회
    :param date:
    :return:
    """

    with MongoHook(CONN_MONGO_FOR_DATA) as hook:
        coll = hook.get_conn().get_database()['CreatorAdvisorReports']

        return coll.find_one({'date': date})
