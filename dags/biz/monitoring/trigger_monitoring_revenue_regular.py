"""
### 수익률 모니터링 정규 트리거 DAG

#### 0. 위키
- [25-1. 수익률 모니터링 정규 처리](https://wiki.navercorp.com/spaces/GFP/pages/3263714690)

#### 1. 주기
- 리얼 환경 : 매일 16시
- 테스트 환경 OFF 처리
- timeout = 4시간

#### 2. 트리거 대상 DAG
- spark_monitoring_sales_rate
- spark_monitoring_profit_rate
- request_monitoring_revenue

#### 3. config
- 없음

#### 4. max_active_runs=1
"""

import logging
import os

import pendulum
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.utils.types import DagRunType

from biz.monitoring.request_monitoring_revenue import DAG_ID as REQUEST_DAG_ID
from biz.monitoring.spark_monitoring_profit_rate import DAG_ID as SPARK_DAG_ID_PROFIT
from biz.monitoring.spark_monitoring_sales_rate import DAG_ID as SPARK_DAG_ID_SALES
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_START_DATE, XCOM_END_DATE, XCOM_TARGET_YMD

_DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [TRIGGER_MONITORING_REVENUE_REGULAR]'


def _setup(**context):
	"""
	regular dag 이므로 스케줄에 의한 파라미터로 결정

	:param context:
	:return:
	"""
	# 실행 날짜 ( 주기가 하루인 경우 logical_date 는 D-1 이므로 +1 처리 )
	target_ymd = pendulum.instance(context['logical_date']).in_tz(DEFAULT_TZ).add(days=1).format('YYYYMMDD')
	# 오늘기준 D-3 부터 D-1 까지의 데이터를 집계
	start_date = pendulum.from_format(target_ymd, 'YYYYMMDD').subtract(days=3).format('YYYYMMDD')
	end_date = pendulum.from_format(target_ymd, 'YYYYMMDD').subtract(days=1).format('YYYYMMDD')

	logging.info(
		f'{_LOG_PREFIX} Using target_ymd from tz-shifted "logical_date". logical_date={context["logical_date"].in_tz(DEFAULT_TZ)} target_ymd= {target_ymd} start_date={start_date} end_date={end_date}')

	context['ti'].xcom_push(XCOM_TARGET_YMD, target_ymd)
	context['ti'].xcom_push(XCOM_START_DATE, start_date)
	context['ti'].xcom_push(XCOM_END_DATE, end_date)


with DAG(
		_DAG_ID,
		description='Trigger regular monitoring revenue DAG',
		tags=['trigger', 'regular', 'monitoring', 'sales', 'profit', 'monitoring_revenue', 'daily', 'mongo', 'ins.cho'],
		default_args={
			'owner': 'ins.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2025, 3, 1, tz=DEFAULT_TZ),
		schedule_interval='0 16 * * *',  # 매일 16시
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	trigger_monitoring_sales_rate = TriggerDagRunOperator(
		trigger_dag_id=SPARK_DAG_ID_SALES,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id=f'trigger_{SPARK_DAG_ID_SALES}',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=True,
		poke_interval=60,
		reset_dag_run=True,
		conf={
			XCOM_TARGET_YMD: f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
			XCOM_START_DATE: f'{{{{ ti.xcom_pull(key="{XCOM_START_DATE}") }}}}',
			XCOM_END_DATE: f'{{{{ ti.xcom_pull(key="{XCOM_END_DATE}") }}}}',
		}
	)

	trigger_monitoring_profit_rate = TriggerDagRunOperator(
		trigger_dag_id=SPARK_DAG_ID_PROFIT,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id=f'trigger_{SPARK_DAG_ID_PROFIT}',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=True,
		poke_interval=60,
		reset_dag_run=True,
		conf={
			XCOM_TARGET_YMD: f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
			XCOM_START_DATE: f'{{{{ ti.xcom_pull(key="{XCOM_START_DATE}") }}}}',
			XCOM_END_DATE: f'{{{{ ti.xcom_pull(key="{XCOM_END_DATE}") }}}}',
		}
	)

	trigger_request_monitoring = TriggerDagRunOperator(
		trigger_dag_id=REQUEST_DAG_ID,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id=f'trigger_{REQUEST_DAG_ID}',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=True,
		poke_interval=60,
		reset_dag_run=True,
		conf={
			XCOM_TARGET_YMD: f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
			XCOM_START_DATE: f'{{{{ ti.xcom_pull(key="{XCOM_START_DATE}") }}}}',
			XCOM_END_DATE: f'{{{{ ti.xcom_pull(key="{XCOM_END_DATE}") }}}}',
		}
	)

	# sales, profit 둘 중 하나라도 실패하면, request 는 실행되지 않는 문제가 있으나
	# 이는 trigger_monitoring_revenue_reprocess DAG 의 _setup 에서 처리함
	# trigger_monitoring_revenue_reprocess :: _replace_last_failed_jobs_with_longest_period_job 참고
	setup >> [trigger_monitoring_sales_rate, trigger_monitoring_profit_rate] >> trigger_request_monitoring
