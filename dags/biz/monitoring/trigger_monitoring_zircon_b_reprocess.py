"""
### 모니터링 지르콘B 재처리 트리거 DAG

#### 0. 위키
- [19. 모니터링 지르콘B](https://wiki.navercorp.com/pages/viewpage.action?pageId=2380323492)

#### 1. 주기
- 리얼 환경 : 매 30분 간격
    - 테스트 환경 OFF 처리
- zircon-b-check-timeout = 10800 (3시간)

#### 2. 스케쥴 콜렉션 : Jobs
- 정규 DAG 에서 실패한 경우
- trace_zircon_b DAG 에서 추가한 경우 ( ZirconTrace 에 지르콘B 재처리 이력이 남은 경우 )

#### 3. 트리거 대상 DAG
- spark_monitoring_zircon_b

#### 4. max_active_runs=1
"""

import os
import logging
import pendulum

from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.utils.types import DagRunType
from airflow.operators.python import ShortCircuitOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator

from core.dao import job_dao
from core.base import DEFAULT_TZ, XCOM_TARGET_YMD, ALERT_EMAIL_ADDRESSES

from biz.monitoring.spark_monitoring_zircon_b import DAG_ID as SPARK_DAG_ID

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [TRIGGER-MONITORING-ZIRCON-B-REPROCESS]'


def _setup(**context):
	"""
	가장 오래된 실패 이력 1건만 가져와서 target_ymd 설정
	:param context:
	:return:
	"""
	doc = job_dao.get_job({'type': SPARK_DAG_ID, 'manual': 0})
	if doc:
		if doc['retryCnt'] >= job_dao.MAX_RETRY_CNT:
			# monitor_jobs 에서 알림 메일 발송하기 때문에, 별도 처리 하지 않음
			logging.warning(
				f'{_LOG_PREFIX} {doc["datetime"]}의 retryCnt={doc["retryCnt"]}. 최대 처리 횟수({job_dao.MAX_RETRY_CNT})에 도달 해서 스킵.')
		elif doc['running'] == 1:
			logging.warning(
				f'{_LOG_PREFIX} {doc["datetime"]} 이미 처리 중이므로 스킵. doc={doc}')
		else:
			context['ti'].xcom_push(XCOM_TARGET_YMD, doc['datetime'])
			return True
	else:
		logging.info(f'{_LOG_PREFIX} 처리할 job 없음')

	return False


with DAG(
		_DAG_ID,
		description='Trigger reprocess monitoring zircon b DAG',
		tags=['trigger', 'reprocess', 'monitoring', 'zircon_b', 'monitoring_zircon_b', 'daily', 'hdfs', 'bitna.cho'],
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2024, 5, 1, tz=DEFAULT_TZ),
		schedule_interval='*/30 * * * *',  # 매 30분 간격
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	# 처리해야 할 job이 있을 때만 다운스트림 태스크 진행
	# Jobs 에서 시간대순으로 하나의 시간대만 추출
	setup = ShortCircuitOperator(
		task_id='setup',
		python_callable=_setup,
	)

	trigger_monitoring_zircon_b = TriggerDagRunOperator(
		trigger_dag_id=SPARK_DAG_ID,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id=f'trigger_{SPARK_DAG_ID}',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=False,
		poke_interval=60,
		reset_dag_run=True,
		conf={
			XCOM_TARGET_YMD: f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
			'trigger_by': DagRunType.SCHEDULED,
		}
	)

	setup >> trigger_monitoring_zircon_b
