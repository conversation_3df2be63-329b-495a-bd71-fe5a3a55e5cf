"""
### Simple 매체 성과 리포트 정규 트리거 DAG

#### 0. 위키
- [17. 매체 성과 리포트](https://wiki.navercorp.com/pages/viewpage.action?pageId=2210851496)

#### 1. 주기
- trigger_zircon_b_regular_1d_ago, trigger_zircon_b_regular_2d_ago 에서 트리거 됨

#### 2. 트리거 대상 DAG
- spark_simple_performance
- D-1(target_ymd - 1), D-0(target_ymd) 을 처리 한다. ( 심플성과는 타임존별 집계를 하므로 )

#### 3. config
- target_ymd : 처리할 날짜 YYYYMMDD ( default: 어제 )

#### 4. max_active_runs=1
"""

import os
import logging
import pendulum

from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.utils.types import DagRunType
from airflow.exceptions import AirflowFailException
from airflow.operators.python import PythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator

from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YMD

from biz.performance.simple.simple_performance_base import XCOM_ONE_DAY_AGO

from biz.performance.simple.spark_simple_performance import DAG_ID as SPARK_DAG_ID

DAG_ID = _DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRIGGER-SIMPLE-PERFORMANCE-REGULAR]'


def _setup(**context):
	"""
	DAG 실행에 필요한 환경 설정 및 확인

	:param context:
	:return:
	"""
	_init_settings(**context)


def _init_settings(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화
	:param context:
	:return:
	"""
	target_ymd = context['params'][XCOM_TARGET_YMD]

	if target_ymd:
		target_ymd = str(target_ymd).strip()
		pendulum.from_format(target_ymd, 'YYYYMMDD')
		one_day_ago = pendulum.from_format(target_ymd, 'YYYYMMDD', DEFAULT_TZ).add(days=-1).format('YYYYMMDD')

		context['ti'].xcom_push(XCOM_TARGET_YMD, target_ymd)
		context['ti'].xcom_push(XCOM_ONE_DAY_AGO, one_day_ago)
	else:
		# target_ymd 가 없으면 에러
		logging.error(f'There is not "params.target_ymd"')
		raise AirflowFailException('There is not "params.target_ymd"')


with DAG(
		_DAG_ID,
		description='Simple 매체 성과 리포트 정규 DAG',
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		params={
			XCOM_TARGET_YMD: '',
		},
		tags=['trigger', 'regular', 'pr', 'simple_performance', 'simple', 'daily', 'data_db', 'bitna.cho'],
		start_date=pendulum.datetime(2024, 4, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	# D-1 트리거
	trigger_simple_performance_1d_ago = TriggerDagRunOperator(
		trigger_dag_id=SPARK_DAG_ID,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id=f'trigger_{SPARK_DAG_ID}_1d_ago',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=False,
		reset_dag_run=True,
		conf={XCOM_TARGET_YMD: f'{{{{ ti.xcom_pull(key="{XCOM_ONE_DAY_AGO}") }}}}', 'trigger_by': 'regular'}
	)

	# D-0 트리거
	trigger_simple_performance = TriggerDagRunOperator(
		trigger_dag_id=SPARK_DAG_ID,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id=f'trigger_{SPARK_DAG_ID}',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=False,
		reset_dag_run=True,
		conf={XCOM_TARGET_YMD: f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}', 'trigger_by': 'regular'}
	)

	setup >> [trigger_simple_performance_1d_ago, trigger_simple_performance]
