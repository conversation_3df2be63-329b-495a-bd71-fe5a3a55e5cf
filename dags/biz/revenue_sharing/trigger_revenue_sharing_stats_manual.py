"""
### 수익쉐어 리포트 용 GFP 통계 생성 - 수동

#### 위키
- [https://wiki.navercorp.com/pages/viewpage.action?pageId=1461924754](https://wiki.navercorp.com/pages/viewpage.action?pageId=1461924754)

#### 개요
- 수동 실행 (스케줄 되지 않음)
- 일 또는 월 통계 집계

#### config
- from_target_ymd, to_target_ymd : 두 일자 사이의 모든 일자를 대상으로 하는 스케줄 실행
    - format : "YYYYMMDD"
- target_dt_list : 입력한 일자 또는 월에 대한 집계
    - comma로 구분된 일자 또는 월을 공백없이 나열 (정렬 필요 없음)
    - 일자 또는 월을 섞어 기술할 수 있으나 반드시 <mark>일자를 먼저 기술</mark>해야 함
    - format : "YYYYMMDD,YYYYMMDD, .. " 또는 "YYYYMM,YYYYMM, .. " 또는 "YYYYMMDD,YYYYMMDD,YYYYMM,YYYYMM .. "
- 세 값 모두 입력한 경우 : from/to 우선
- 모두 입력하지 않은 경우 : 에러
"""
import logging
import os

import pendulum
import pymongo
from airflow import AirflowException
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.email import send_email
from airflow.utils.state import DagRunState
from time import sleep

from biz.revenue_sharing import spark_revenue_sharing_stats
from core.airflow_api import trigger_dagrun
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_DT
from core.dao import summary_history_dao
from core.dao.summary_history_dao import SPARK_APP_STATE_LAUNCH, SPARK_APP_STATE_IN_PROGRESS
from core.utils import get_between_ymd_list

# DAG 기본정보
DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRG-RS-STATS-REGULAR]'
_TARGET_DAG_ID = spark_revenue_sharing_stats.DAG_ID
_XCOM_TARGET_DT_LIST = 'target_dt_list'


def _setup(**context):
    """
    DAG 실행에 필요한 환경 설정 및 확인
    :param context:
    :return:
    """
    _set_dt_list(**context)


def _set_dt_list(**context):
    """
    config를 이용해 재실행할 대상일 또는 월 설정
    :param context:
    :return:
    """
    from_ymd = context['params'].get('from_target_ymd')
    to_ymd = context['params'].get('to_target_ymd')
    target_dt_list: str = context['params'].get('target_dt_list')

    dt_list = []

    if from_ymd or to_ymd:
        from_ymd = from_ymd.strip()
        pendulum.from_format(from_ymd, 'YYYYMMDD')
        if to_ymd:
            to_ymd = to_ymd.strip()
            pendulum.from_format(to_ymd, 'YYYYMMDD')
            if from_ymd > to_ymd:
                raise AirflowException(f'from_target_ymd은 to_target_ymd 보다 작거나 같아야 합니다.'
                                       f' from_ymd={from_ymd} to_ymd={to_ymd}')
            else:
                dt_list = get_between_ymd_list(from_ymd, to_ymd)
                logging.info(f'{_LOG_PREFIX} from_target_ymd / to_target_ymd를 이용한 실행.'
                             f' from_ymd={from_ymd} to_ymd={to_ymd} ymd_list={dt_list}')
        else:
            raise AirflowException(f'to_target_ymd가 기술되지 않음. from_ymd={from_ymd}')
    elif target_dt_list:
        target_dt_list = target_dt_list.strip(', ')
        dt_list = target_dt_list.split(',')
        logging.info(f'{_LOG_PREFIX} target_dt_list를 이용한 실행. {dt_list}')
    else:
        raise AirflowException(f'"config"가 기술되지 않음')

    # xcom에 push
    context['ti'].xcom_push(_XCOM_TARGET_DT_LIST, dt_list)


def _trigger_revenue_sharing_stats(**context):
    """
    DB에서 읽어온 스케줄의 날짜에 대해 시간 순으로 spark_revenue_sharing_stats 트리거시킴
    스킵되거나, 실패된 리스트는 따로 정리
    :param context:
    :return:
    """
    skipped_list = []
    failed_list = []

    # 대상 스케줄 목록을 돌면서
    dt_list = context['ti'].xcom_pull(key=_XCOM_TARGET_DT_LIST)
    dt_list_len = len(dt_list)
    for idx, dt in enumerate(dt_list):
        # 이미 실행중인지 확인 - SummaryHistory
        # spark_revenue_sharing_stats는 reprocess dag이 없기때문에 Jobs에서 관리하지 않으므로 실행중 여부를 SummaryHistory에서 확인함.
        filter = {
            'dag.dag_id': _TARGET_DAG_ID,
            'datetime': dt,
            'spark.sparkAppState': {'$in': [SPARK_APP_STATE_LAUNCH, SPARK_APP_STATE_IN_PROGRESS]}
        }
        sort = [('_id', pymongo.DESCENDING)]  # 최근 이력
        doc = summary_history_dao.get_history(filter, sort)
        if doc:
            skipped_list.append((dt, '이미 처리 중'))
            logging.warning(
                f'{_LOG_PREFIX} 이미 처리 중이므로 스킵({idx + 1}/{dt_list_len}). {dt} dag_run_id={doc["dag"]["dag_run_id"]}')
            continue
        else:
            logging.warning(f'{_LOG_PREFIX} 진행중({idx + 1}/{dt_list_len}) {dt}')

            # 해당 스케줄(일 또는 월)의 통계 생성 요청
            dag_run_id = trigger_dagrun(_TARGET_DAG_ID, pendulum.now(DEFAULT_TZ), {XCOM_TARGET_DT: dt})
            logging.info(f'{_LOG_PREFIX} {_TARGET_DAG_ID} 트리거시킴'
                         f' dt={dt} target_dag_id={_TARGET_DAG_ID} dag_run_id={dag_run_id}')

            # dag이 끝났는지 주기적으로 확인
            while True:
                sleep(60)
                dag_runs = DagRun.find(dag_id=_TARGET_DAG_ID, run_id=dag_run_id)
                if dag_runs:
                    dag_run: DagRun = dag_runs.pop()
                    if dag_run.get_state() == DagRunState.SUCCESS:
                        break
                    elif dag_run.get_state() == DagRunState.FAILED:
                        failed_list.append((dt, dag_run_id))
                        break
                else:
                    failed_list.append((dt, f'{dag_run_id} 트리거 시켰으나, 존재하지 않음)'))
                    logging.info(f'{_LOG_PREFIX} {_TARGET_DAG_ID}({dag_run_id}) 트리거 시켰으나, 존재하지 않음')
                    break

    context['ti'].xcom_push('skipped_list', skipped_list)
    context['ti'].xcom_push('failed_list', failed_list)


def _conclude(**context):
    """
    스킵 또는 실패된 리스트 알림
    실패 건들에 대한 자동 재처리 DAG은 없다. target dag이 하루에 5번 돌기 때문에 그것으로 대체.
    :param context:
    :return:
    """
    skipped_msg = '[skipped_list]<br/>'
    skipped_list = context['ti'].xcom_pull(key='skipped_list')
    for item in skipped_list:
        skipped_msg += f'&nbsp;&nbsp;&nbsp;&nbsp; ymdh={item[0]} reason={item[1]}<br/>'

    failed_msg = '[failed_list]<br/>'
    failed_list = context['ti'].xcom_pull(key='failed_list')
    for item in failed_list:
        failed_msg += f'&nbsp;&nbsp;&nbsp;&nbsp; ymdh={item[0]} dag_run_id={item[1]}<br/>'

    if skipped_list or failed_list:
        title = f'[NAM 성과 리포트 생성 수동 실행에 스킵 또는 실패 건이 있음] 스킵 건수={len(skipped_list)} 실패 건수={len(failed_list)}'
        body = f'{skipped_msg}<br/>{failed_msg}<br/>'
        send_email(ALERT_EMAIL_ADDRESSES, title, body)


with DAG(
        DAG_ID,
        description='수익쉐어 리포트 GFP 용 통계 생성을 위한 정규 DAG. spark_revenue_sharing_stats 트리거시킴',
        default_args={
            'owner': 'juyoun.kim',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
        },
        tags=['trigger', 'revenue_sharing', 'revenuesharing', 'revenue', 'manual', 'juyoun.kim'],
        start_date=pendulum.datetime(2023, 5, 1, tz=DEFAULT_TZ),
        schedule_interval=None,
        catchup=False,
        params={
            'USAGE': 'Refer to the DAG code for detailed parameter usage.',
            'from_target_ymd': '',
            'to_target_ymd': '',
            'target_dt_list': '',
        },
        render_template_as_native_obj=True
) as dag:
    dag.doc_md = __doc__

    # 환경 설정
    setup = PythonOperator(
        task_id='setup',
        python_callable=_setup,
    )

    # spark 실행. n개 날짜(일 또는 월)에 대해 순차 처리
    trigger_revenue_sharing_stats = PythonOperator(
        task_id='trigger_revenue_sharing_stats',
        python_callable=_trigger_revenue_sharing_stats,
    )

    # 스킵 또는 실패된 리스트 알림
    conclude = PythonOperator(
        task_id='conclude',
        python_callable=_conclude,
    )

    # 파이프라인
    setup >> trigger_revenue_sharing_stats >> conclude
