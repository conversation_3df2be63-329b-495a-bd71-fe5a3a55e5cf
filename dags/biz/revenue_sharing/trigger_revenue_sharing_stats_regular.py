"""
### 수익쉐어 리포트 용 GFP 통계 생성 - 정규

#### 위키
- [https://wiki.navercorp.com/pages/viewpage.action?pageId=1461924703](https://wiki.navercorp.com/pages/viewpage.action?pageId=1461924703)

#### 개요
- CMS DB > SummaryRevenueSharingGfpStatsSchedules 컬렉션에서 sparkAppState = 'STANDBY'인 스케줄을 추출하여 시간 순으로 spark_revenue_sharing_stats를 트리거시킨다.

#### 주기
- 10분
- 동시에 한 번의 DAG RUN만 허용

#### config
- 없음
- 수동 실행을 위해서는 trigger_revenue_sharing_stats_manual을 사용헤 주세요.
"""
import logging
import os

import pendulum
import pymongo
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.email import send_email
from airflow.utils.state import DagRunState
from time import sleep

from biz.revenue_sharing import spark_revenue_sharing_stats, revenue_sharing_dao
from core.airflow_api import trigger_dagrun
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_DT
# DAG 기본정보
from core.dao import summary_history_dao
from core.dao.summary_history_dao import SPARK_APP_STATE_LAUNCH, SPARK_APP_STATE_IN_PROGRESS
from core.utils import prettyStr

DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRG-RS-STATS-REGULAR]'
_TARGET_DAG_ID = spark_revenue_sharing_stats.DAG_ID
_XCOM_DT_LIST = 'dt_list'


def _setup(**context):
    """
    DAG 실행에 필요한 환경 설정 및 확인
    :param context:
    :return:
    """
    # STANDBY 스케줄을 일별, 월별 순서로 가져와서
    filter = {
        # 'date': {'$in': [
        #     # '20230401', '20230402', '20230403'
        #     '20230501'
        # ]},
        'sparkAppState': 'STANDBY'
    }
    dt_list = revenue_sharing_dao.get_target_dates(filter)
    logging.info(f'{_LOG_PREFIX} dts={prettyStr(dt_list)}')

    # xcom에 push
    context['ti'].xcom_push(_XCOM_DT_LIST, dt_list)


def _trigger_revenue_sharing_stats(step: int, **context):
    """
    xcom에 있는 'dt_list'에 대해 spark_revenue_sharing_stats 트리거시킴
    스킵되거나, 실패된 리스트는 따로 정리
    :param context:
    :return:
    """
    success_list = []
    skipped_list = []
    failed_list = []

    # 대상 스케줄 목록을 돌면서
    dt_list = context['ti'].xcom_pull(key=_XCOM_DT_LIST)
    logging.info(f"{_LOG_PREFIX} ................................................step={step} dt_list={dt_list}")

    dt_list_len = len(dt_list)
    for idx, dt in enumerate(dt_list):
        logging.info(f"{_LOG_PREFIX} dt={dt}")  # 일(ymd) 또는 월(ym) 단위

        # ........................ test code
        # success_list.append((dt, '1'))
        # skipped_list.append((dt, '1'))
        # failed_list.append((dt, '1'))

        # 이미 실행중인지 확인 - SummaryHistory
        # spark_revenue_sharing_stats는 reprocess dag이 없기때문에 Jobs에서 관리하지 않으므로 실행중 여부를 SummaryHistory에서 확인함.
        filter = {
            'dag.dag_id': _TARGET_DAG_ID,
            'datetime': dt,
            'spark.sparkAppState': {'$in': [SPARK_APP_STATE_LAUNCH, SPARK_APP_STATE_IN_PROGRESS]}
        }
        sort = [('_id', pymongo.DESCENDING)]  # 최근 이력
        doc = summary_history_dao.get_history(filter, sort)
        if doc:
            skipped_list.append((dt, '이미 처리 중'))
            logging.warning(
                f'{_LOG_PREFIX} 이미 처리 중이므로 스킵({idx + 1}/{dt_list_len}). {dt} dag_run_id={doc["dag"]["dag_run_id"]}')
            continue
        else:
            logging.warning(f'{_LOG_PREFIX} 진행중({idx + 1}/{dt_list_len}) {dt}')

            # 해당 스케줄(일 또는 월)의 통계 생성 요청
            dag_run_id = trigger_dagrun(_TARGET_DAG_ID, pendulum.now(DEFAULT_TZ), {XCOM_TARGET_DT: dt})
            logging.info(f'{_LOG_PREFIX} {_TARGET_DAG_ID} 트리거시킴'
                         f' dt={dt} target_dag_id={_TARGET_DAG_ID} dag_run_id={dag_run_id}')

            # dag이 끝났는지 주기적으로 확인
            while True:
                sleep(60)
                dag_runs = DagRun.find(dag_id=_TARGET_DAG_ID, run_id=dag_run_id)
                if dag_runs:
                    dag_run: DagRun = dag_runs.pop()
                    if dag_run.get_state() == DagRunState.SUCCESS:
                        success_list.append((dt, dag_run_id))
                        break
                    elif dag_run.get_state() == DagRunState.FAILED:
                        failed_list.append((dt, dag_run_id))
                        break
                else:
                    failed_list.append((dt, f'{dag_run_id} 트리거 시켰으나, 존재하지 않음)'))
                    logging.info(f'{_LOG_PREFIX} {_TARGET_DAG_ID}({dag_run_id}) 트리거 시켰으나, 존재하지 않음')
                    break

    context['ti'].xcom_push(f'step{step}_success_list', success_list)
    context['ti'].xcom_push(f'step{step}_skipped_list', skipped_list)
    context['ti'].xcom_push(f'step{step}_failed_list', failed_list)
    context['ti'].xcom_push('step', step)


def _conclude(step: int, **context):
    """
    스킵 또는 실패된 리스트 알림
    실패 건들에 대한 자동 재처리 DAG은 없다. target dag이 하루에 5번 돌기 때문에 그것으로 대체.
    :param context:
    :return:
    """
    skipped_msg = f'[step{step}_skipped_list]<br/>'
    skipped_list = context['ti'].xcom_pull(key=f'step{step}_skipped_list')
    for item in skipped_list:
        skipped_msg += f'&nbsp;&nbsp;&nbsp;&nbsp; ymdh={item[0]} reason={item[1]}<br/>'

    failed_msg = f'[step{step}_failed_list]<br/>'
    failed_list = context['ti'].xcom_pull(key=f'step{step}_failed_list')
    for item in failed_list:
        failed_msg += f'&nbsp;&nbsp;&nbsp;&nbsp; ymdh={item[0]} dag_run_id={item[1]}<br/>'

    if skipped_list or failed_list:
        title = f'[수익쉐어 리포트 용 GFP 통계 생성 정규 실행에 스킵 또는 실패 건이 있음] 스킵 건수={len(skipped_list)} 실패 건수={len(failed_list)}'
        body = f'{skipped_msg}<br/>{failed_msg}<br/>'
        send_email(ALERT_EMAIL_ADDRESSES, title, body)


with DAG(
        DAG_ID,
        description='수익쉐어 리포트 GFP 용 통계 생성을 위한 정규 DAG. spark_revenue_sharing_stats 트리거시킴',
        default_args={
            'owner': 'juyoun.kim',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
        },
        tags=['trigger', 'revenue_sharing', 'revenuesharing', 'revenue', 'regular', 'juyoun.kim'],
        start_date=pendulum.datetime(2023, 5, 1, tz=DEFAULT_TZ),
        schedule_interval="*/10 * * * *",  # 10분마다
        catchup=False,
        max_active_runs=1,
) as dag:
    dag.doc_md = __doc__

    # 환경 설정
    setup = PythonOperator(
        task_id='setup',
        python_callable=_setup,
    )

    # 일별 또는 월별 통계 생성 spark
    trigger_revenue_sharing_stats = PythonOperator(
        task_id='trigger_revenue_sharing_stats',
        python_callable=_trigger_revenue_sharing_stats,
        op_kwargs={'step': 1}
    )

    # 일별 또는 월별 통계 생성 spark의 스킵 또는 실패된 리스트 알림
    conclude = PythonOperator(
        task_id='conclude',
        python_callable=_conclude,
        op_kwargs={'step': 1},
    )

    # 파이프라인
    setup >> trigger_revenue_sharing_stats >> conclude
