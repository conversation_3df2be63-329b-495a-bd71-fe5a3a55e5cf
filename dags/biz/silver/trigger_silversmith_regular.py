"""
### 실버 로그 적재 정규 처리

#### 위키
- [로그적재/DAG 정의/연동/재처리 디자인 패턴](https://wiki.navercorp.com/pages/viewpage.action?pageId=1242737611)
- [https://wiki.navercorp.com/pages/viewpage.action?pageId=1392868680](https://wiki.navercorp.com/pages/viewpage.action?pageId=1392868680)

#### 개요
- Jobs 컬렉션에서 시간대 순으로 하나의 시간대를 추출하여 spark_silversmith를 트리거시킨다.

#### 주기
- 10분
    - make_silver_job이 매 시 2, 12, 22, 32, 42, 52분에 동작하기 때문에
    이게 끝나고 난 뒤 길어야 8분 안에는 실버 적재가 이루어지는 형태.
- 동시에 한 번의 DAG RUN만 허용

#### config
- 없음
- 수동 실행을 위해서는 trigger_silversmith_manual을 사용헤 주세요.
"""

import logging
import os

import pendulum
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import ShortCircuitOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.utils.types import DagRunType

from biz.silver import spark_silversmith
from core.base import DEFAULT_TZ, XCOM_TARGET_YMDH, ALERT_EMAIL_ADDRESSES, XCOM_LOG_TYPE
from core.dao import job_dao

# DAG 기본정보
from core.dao.environment_dao import get_environment_value_by_name

DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRG-SILVERSMITH-REGULAR]'

# 브론즈 지연이 해소되었을 때 최대한 빨리 재처리하기 위해 스케줄을 기본 '*/10 * * * *'에서 '*/1 * * * *' 등으로 바꿀 수 있음.
# 코드 수정 없이 적용하기 위해 환경값으로 관리
_SCHEDULE = str(get_environment_value_by_name('silver-log-regular-schedule'))


def _get_job(**context):
	"""
	처리해야 할 하나의 시간대 얻어 xcom에 설정.
	시간 순.
	:param context:
	:return:
	"""
	doc = job_dao.get_job({
		'type': spark_silversmith.DAG_ID,
		'manual': 0,
		'retryCnt': -1,
		'running': 0,
	})
	if doc:
		# ObjectId이 포함된 mongodb doc object -> str :: json_util.dumps(doc)
		# context['ti'].xcom_push(_XCOM_TARGET_JOB, json_util.dumps(doc))
		context['ti'].xcom_push(XCOM_TARGET_YMDH, doc['datetime'])
		return True
	else:
		logging.info(f'{_LOG_PREFIX} 처리할 job 없음')

	return False


with DAG(
		DAG_ID,
		description='실버 적재를 위한 정규 DAG. spark_silversmith 트리거시킴',
		default_args={
			'owner': 'juyoun.kim',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		tags=['trigger', 'silver', 'silversmith', 'hourly', 'regular', 'juyoun.kim'],
		start_date=pendulum.datetime(2023, 1, 1, tz=DEFAULT_TZ),
		schedule_interval=_SCHEDULE,
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	# 처리해야 할 job이 있을 때만 다운스트림 태스크 진행
	# Jobs에서 시간대순으로 하나의 시간대만 추출
	get_job = ShortCircuitOperator(
		task_id='get_job',
		python_callable=_get_job,
	)

	# spark_silversmith 트리거
	trigger_silversmith = TriggerDagRunOperator(
		trigger_dag_id=spark_silversmith.DAG_ID,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id='trigger_spark_silversmith',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=True,
		poke_interval=60,
		reset_dag_run=True,
		conf={
			XCOM_TARGET_YMDH: f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMDH}") }}}}',
			XCOM_LOG_TYPE: '*',
		}
	)

	# 파이프라인
	get_job >> trigger_silversmith
