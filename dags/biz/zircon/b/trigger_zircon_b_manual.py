"""
### Zircon B 생성 수동 처리

#### 위키
- [05-3. Zircon B 생성 수동 처리](https://wiki.navercorp.com/pages/viewpage.action?pageId=1402969276)

#### 개요
- 수동 실행 (스케줄 되지 않음)
- config로부터 받은 일자에 대해 순차적으로 실행
- Jobs 컬렉션에 매체별 spark_zircon_b job 추가
- 해당 날짜 안에서 spark_zircon_b는 병렬처리
- 수동 실행에서 실패한 job에 대해서는 자동 재처리하지 않음.
- 동시에 여러 DAG RUN 허용
- 같은 날짜에 대한 중복 실행 불가

#### config
- 날짜
	- from_target_ymd, to_target_ymd : 두 값 사이(edge 포함)의 모든 datetime 을 대상으로 하는 스케줄 실행
		- format : "YYYYMM"
	- target_ymd_list : 입력한 값들에 속하는 datetime 을 대상으로 하는 스케줄 실행
		- comma로 구분된 ymd 를 공백없이 나열 (정렬 필요 없음)
		- format : "YYYYMM,YYYYMM,YYYYMM, .. "
	- 세 값 모두 입력한 경우 : from/to 우선
	- 모두 입력하지 않은 경우 : 에러
- kind:
	- 'manual' (고정)
- pub_id
	- 매체 ID
- ap_id
	- 광고공급자 ID 또는 *
- pub_id:ap_id 관계
	- 1:* 가능
	- *:1 가능
	- 1:1 가능
	- *:* 가능(manual에서만 가능)
- is_full_field
	- 0: 계산에 필요한 중간 집계 필드 저장하지 않음
	- 1: 계산에 필요한 중간 집계 필드 저장
"""
import logging
import os
from time import sleep

import pendulum
from airflow import AirflowException
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.email import send_email
from airflow.utils.state import DagRunState
from airflow.utils.types import DagRunType
from bson import ObjectId

from biz.zircon.b import spark_zircon_b
from biz.zircon.b.spark_zircon_b import XCOM_PUB_ID, XCOM_AP_ID, XCOM_KIND, validate_param
from core import utils, c3
from core.airflow_api import trigger_dagrun
from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, XCOM_TARGET_YMD, XCOM_SILVER_TRACE_ID, \
	XCOM_ZIRCON_TRACE_ID, XCOM_TARGET_YMD_LIST, XCOM_IS_FULL_FIELD, BATCH_SERVER_ADDRESS, ZIRCON_B_GFP_HOME, \
	ZIRCON_B_STATE_TOUCHED
from core.dao import job_dao, environment_dao, zircon_trace_dao
from core.dao.environment_dao import get_environment_value_by_name
from core.utils import get_between_ymd_list

# DAG 기본정보
DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRG-ZB-MANUAL]'

_ZIRCON_B_REGULAR_EXE_TIMEOUT = int(get_environment_value_by_name('zircon-b-regular-execution-timeout'))
_KIND = 'manual'

# Batch 호출 주소
_REQ_URL = BATCH_SERVER_ADDRESS + '/batch/zircon/b/recentymd/refresh'


def _setup(**context):
	# 날짜 설정
	_set_ymd_list(**context)

	# 사용자가 지정한 파라미터 가져오기
	pub_id = context['params'].get(XCOM_PUB_ID)
	ap_id = context['params'].get(XCOM_AP_ID)
	is_full_field = context['params'].get(XCOM_IS_FULL_FIELD)

	# 파라미터 유효성 검사
	# ※ manual에서는 pub_id, ap_id가 모두 '*'일 수 있음.
	validate_param(_KIND, pub_id, ap_id, '-', '-', is_full_field)

	# 파라미터를 이용해 XCOM에 PUSH
	context['ti'].xcom_push(XCOM_PUB_ID, pub_id)
	context['ti'].xcom_push(XCOM_AP_ID, ap_id)
	context['ti'].xcom_push(XCOM_IS_FULL_FIELD, is_full_field)


def _set_ymd_list(**context):
	"""
	config를 이용해 재실행할 대상월(ym) 설정
	:param context:
	:return:
	"""
	from_ymd = context['params'].get('from_target_ymd')
	to_ymd = context['params'].get('to_target_ymd')
	target_ymd_list: str = context['params'].get('target_ymd_list')

	ymd_list = []

	if from_ymd or to_ymd:
		from_ymd = from_ymd.strip()
		pendulum.from_format(from_ymd, 'YYYYMMDD')
		if to_ymd:
			to_ymd = to_ymd.strip()
			pendulum.from_format(to_ymd, 'YYYYMMDD')
			if from_ymd > to_ymd:
				raise AirflowException(f'from_target_ymd은 to_target_ymd보다 작거나 같아야 합니다.'
									   f' from_ymd={from_ymd} to_ymd={to_ymd}')
			else:
				ymd_list = get_between_ymd_list(from_ymd, to_ymd)
				logging.info(f'{_LOG_PREFIX} from_target_ymd / to_target_ymd를 이용한 실행'
							 f' from_ymd={from_ymd} to_ymd={to_ymd} ymd_list={ymd_list}')
		else:
			raise AirflowException(f'to_target_ymd이 기술되지 않음. from_ymd={from_ymd}')
	elif target_ymd_list:
		target_ymd_list = target_ymd_list.strip(', ')

		ymd_list = target_ymd_list.split(',')
		for ymd in ymd_list:
			pendulum.from_format(ymd, 'YYYYMMDD')

		logging.info(f'{_LOG_PREFIX} target_ymd_list를 이용한 실행. {ymd_list}')
	else:
		raise AirflowException(f'"config"가 기술되지 않음')

	# xcom에 push
	context['ti'].xcom_push(XCOM_TARGET_YMD_LIST, ymd_list)


def _check_dup_run(**context):
	"""
	사용자가 요청한 날짜가 이미 다른 DAG에서 돌고 있는지
	:param context:
	:return:
	"""
	ymd_list = context['ti'].xcom_pull(key=XCOM_TARGET_YMD_LIST)
	my_pub_id = context['ti'].xcom_pull(key=XCOM_PUB_ID)
	my_ap_id = context['ti'].xcom_pull(key=XCOM_AP_ID)

	# My DAG
	my_ymd_set = set(ymd_list)
	my_dag_run_id = context['dag_run'].run_id

	# Others DAG
	dag_runs: list[DagRun] = DagRun.find(dag_id=DAG_ID, state=DagRunState.RUNNING)
	for dag_run in dag_runs:
		your_dag_run_id = dag_run.run_id
		if my_dag_run_id != your_dag_run_id:
			ti = dag_run.get_task_instance('setup')
			running_target_ymd_list = ti.xcom_pull(key=XCOM_TARGET_YMD_LIST)
			intersection = list(my_ymd_set & set(running_target_ymd_list))

			# 겹치는 날짜가 있고
			if intersection:
				your_pub_id = ti.xcom_pull(key=XCOM_PUB_ID)
				your_ap_id = ti.xcom_pull(key=XCOM_AP_ID)

				if (
						# 나의 pub/ap와 너의 pub/ap가 같거나
						(my_pub_id == your_pub_id and my_ap_id == your_ap_id) or

						# 나와 너의 pub/ap가 교차되어 '*'를 포함하고 있거나
						((my_pub_id == '*' and your_ap_id == '*') or (my_ap_id == '*' and your_pub_id == '*')) or

						# pub이 같은데 ap가 *를 포함하는 경우
						(my_pub_id == your_pub_id and (my_ap_id == '*' or your_ap_id == '*')) or

						# ap가 같은데 pub이 *를 포함하는 경우
						(my_ap_id == your_ap_id and (my_pub_id == '*' or your_pub_id == '*'))
				):
					raise AirflowException(
						f'\t동시 실행할 수 없는 조건의 DAG이 이미 실행중입니다.\n'
						f'\t            ymd={", ".join(intersection)}\n'
						f'\t  my_dag_run_id={my_dag_run_id}   my_pub_id={my_pub_id}   my_ap_id={my_ap_id}\n'
						f'\tyour_dag_run_id={your_dag_run_id} your_pub_id={your_pub_id} your_ap_id={your_ap_id}')


def _process(**context):
	"""
	날짜별로 Zircon B를 생성한다.
		1. 이 날짜에 대한 매체별 spark_zircon_b jobㅇㄹ Jobs 컬렉션에 추가
		2. 추가한 job을 병렬처리. 한 날짜에 대해 모두 처리할 때까지 루프

	같은 날짜라도 pub_id, ap_id의 조합이 다르면 처리할 수 있다.
	내가 처리해야 할 job이 어떤 것인지 구분하기 위해 job을 추가할 때 dag_run_id를 기술하고
	병렬처리를 할 때 나의 dag_run_id가 기술된 job만 처리한다.

	:param context:
	:return:
	"""
	ymd_list = context['ti'].xcom_pull(key=XCOM_TARGET_YMD_LIST)
	pub_id = context['ti'].xcom_pull(key=XCOM_PUB_ID)
	ap_id = context['ti'].xcom_pull(key=XCOM_AP_ID)
	is_full_field = context['ti'].xcom_pull(key=XCOM_IS_FULL_FIELD)

	logging.info(f'{_LOG_PREFIX} 사용자 입력 파라미터 ymd_list={ymd_list} '
				 f'pub_id={pub_id} ap_id={ap_id} is_full_field={is_full_field}')

	for ymd in ymd_list:
		_insert_zircon_b_jobs(ymd, pub_id, ap_id, is_full_field, **context)
		_run_parallel_spark_zircon_b(ymd, **context)


def _get_publisher_ids(ymd: str) -> []:
	"""
	Zircon B GFP warehouse 특정 일자 경로 아래에 있는 유니크 매체ID 구하기
	:param ymd:
	:return:
	"""
	unique_pub_ids = []
	zbgfp_root = f'{ZIRCON_B_GFP_HOME}/warehouse/{ymd[:4]}/{ymd[4:6]}/{ymd[6:8]}'
	hours = [str(i).zfill(2) for i in range(24)]
	for hour in hours:
		path = f'{zbgfp_root}/{hour}'
		pub_paths = c3.read_dir(path)
		for pub_path in pub_paths:
			pub = pub_path['pathSuffix']
			if pub.startswith('_publisherId='):
				pub_id = pub[pub.index('=') + 1:]
				unique_pub_ids.append(pub_id)

	return list(set(unique_pub_ids))


def _insert_zircon_b_jobs(ymd: str, pub_id: str, ap_id: str, is_full_field: str, **context):
	"""
	매체 개수만큼 job 추가
	:param context:
	:return:
	"""
	# 이번 DAG RUN ID를 통해 처리해야 할 job을 구분하기 위함
	dag_run_id = context['dag_run'].run_id

	if pub_id == '*':
		# 모든 매체의 ObjectId 리스트 생성
		pub_obj_ids = [ObjectId(pub_id_str) for pub_id_str in _get_publisher_ids(ymd)]
	else:
		# 특정 매체의 ObjectId 생성
		pub_obj_ids = [ObjectId(pub_id)]

	ap_obj_id = ObjectId(ap_id) if ap_id != '*' else None

	ymd_list = context['ti'].xcom_pull(key=XCOM_TARGET_YMD_LIST)
	logging.info(
		f'{_LOG_PREFIX} 처리해야 할 날짜={ymd} / [{", ".join(ymd_list)}] 매체개수={len(pub_obj_ids)}\npub_obj_ids={pub_obj_ids}')

	# 매체별로 spark_zircon_b job 생성
	for pub_obj_id in pub_obj_ids:
		filter = {
			'type': spark_zircon_b.DAG_ID,
			'datetime': ymd,
			'detail.kind': _KIND,
			'detail.publisher_id': pub_obj_id,
			'detail.adProvider_id': ap_obj_id,
			'detail.silverTrace_id': None,
			'detail.zirconTrace_id': None,
			'manual': 1,
		}
		job = job_dao.get_job(filter)
		if job:
			# job이 이미 있으면 retryCnt=-1
			job_dao.update_job(filter, {
				'$set': {
					'retryCnt': -1,
					'detail.dag_run_id': dag_run_id,
					'modifiedAt': pendulum.now(DEFAULT_TZ)
				}
			})
			logging.warning(f'이미 있으므로 추가하지 않고 retryCnt=-1로 업데이트. job={job}')
		else:
			# job이 없다면 추가
			job = {
				'type': spark_zircon_b.DAG_ID,
				'datetime': ymd,
				'detail': {
					'kind': _KIND,
					'publisher_id': pub_obj_id,
					'adProvider_id': ap_obj_id,
					'silverTrace_id': None,
					'zirconTrace_id': None,
					'isFullField': is_full_field,
					'dag_run_id': dag_run_id,
				},
				'retryCnt': -1,
				'manual': 1,
				'running': 0,
			}
			job_dao.insert_job(job)

		_set_zircon_trace_to_touched(ymd, pub_id, ap_id)

	logging.info(f'{_LOG_PREFIX} Jobs에 추가 완료. 추가된 매체 개수={len(pub_obj_ids)}')


def _set_zircon_trace_to_touched(ymd: str, pub_id: str, ap_id: str):
	"""
	ZirconTrace.zirconBTouchedAt 업데이트
	Job 처리를 위해 감지했음을 표시

	ZirconTrace.zirconBTouchedAt를 만지는 곳은 지르콘을 수행하기 위해 Job을 생성하는 곳으로 아래와 같다.
		- trigger_zircon_b_regualr_xx
		- trigger_zircon_b_manual
		- trace_silvregrey

	trigger_zircon_b_reprocess는 이미 만들어진 job을 재처리하는 곳이므로 해당사항 없다.

	:param ymd:
	:param pub_id:
	:param ap_id:
	:return:
	"""
	match = {'date': ymd}
	if pub_id != '*':
		match['publisher_id'] = ObjectId(pub_id)
	if ap_id != '*':
		match['adProvider_id'] = ObjectId(ap_id)

	now = pendulum.now(DEFAULT_TZ)
	update = [{
		'$set': {
			'zirconBTouchedAt': now,
			'zirconBState': ZIRCON_B_STATE_TOUCHED,
			'modifiedAt': now
		}
	}]

	result = zircon_trace_dao.update_trace_many(match, update)
	logging.info(
		f'{_LOG_PREFIX} _set_zircon_trace_to_touched() match={match} result.modified_count={result.modified_count}')


def _run_parallel_spark_zircon_b(ymd: str, **context):
	"""
	날짜별/매체별 Zircon B 스파크 트리거
	:param context:
	:return:
	"""
	# 병렬도 설정
	value = environment_dao.get_environment_value_by_name('zircon-b-parallel-level')
	_MAX_PARALLEL_LEVEL = int(value) if value else 3  # 디폴트 3
	logging.info(f'{_LOG_PREFIX} _MAX_PARALLEL_LEVEL={_MAX_PARALLEL_LEVEL}')

	dag_run_id = context['dag_run'].run_id

	start_time = pendulum.now(DEFAULT_TZ)
	triggered_cnt = 0
	while True:
		remained_list = _get_remained_list(ymd, dag_run_id)

		if len(remained_list) == 0:
			end_time = pendulum.now(DEFAULT_TZ)
			elapsed_time = end_time.diff(start_time)
			minutes = elapsed_time.in_minutes()
			seconds = elapsed_time.in_seconds() % 60
			logging.info(f'{_LOG_PREFIX}\t{ymd} 수동처리 완료. 소요시간 {minutes}분 {seconds}초\n')

			break
		else:
			# 진행중인 작업 목록 조회
			ing_list = _get_ing_list(ymd, dag_run_id)
			ing_cnt = len(ing_list) if ing_list else 0

			# 동시 처리를 위한 여분 설정
			spare_cnt = _MAX_PARALLEL_LEVEL - ing_cnt
			if spare_cnt == 0:  # 여유분 없이 꽉 찼음
				pass
			elif spare_cnt > 0:  # 여유분이 있을 경우
				logging.info(f'{_LOG_PREFIX} 여유분={spare_cnt}')
				# 여유분 만큼 job 목록 가겨오기
				standby_list = _get_standby_list(ymd, dag_run_id, spare_cnt)

				# 여유분만큼 스파크 트리거 병렬처리
				if standby_list:
					triggered_job_ids = []
					for job in standby_list:
						job_id = job.get('_id')

						pub_id = str(job.get('detail').get('publisher_id')) \
							if job.get('detail').get('publisher_id') else '*'
						ap_id = str(job.get('detail').get('adProvider_id')) \
							if job.get('detail').get('adProvider_id') else '*'
						is_full_field = job.get('detail').get('isFullField')

						conf = {
							XCOM_TARGET_YMD: ymd,  # YYYYMMDD
							XCOM_KIND: _KIND,
							XCOM_PUB_ID: pub_id,
							XCOM_AP_ID: ap_id,
							XCOM_SILVER_TRACE_ID: '-',
							XCOM_ZIRCON_TRACE_ID: '-',
							XCOM_IS_FULL_FIELD: is_full_field
						}

						# 스파크 트리거
						spark_dag_run_id = trigger_dagrun(spark_zircon_b.DAG_ID,
														  pendulum.now(DEFAULT_TZ),
														  conf,
														  dag_run_type=DagRunType.MANUAL)

						triggered_job_ids.append(job_id)
						triggered_cnt = triggered_cnt + 1

						logging.info(
							f'{_LOG_PREFIX} {spark_zircon_b.DAG_ID} 트리거시킴. ({triggered_cnt}) '
							f'{ymd} pub_id={pub_id} ap_id={ap_id} is_full_field={is_full_field} '
							f'job_id={job_id} conf={conf} spark_dag_run_id={spark_dag_run_id}')

					# job이 running=1로 바뀔 때까지 대기
					# 그래야 다음 루프에서 _get_remained_list()를 했을 때 다시 추출되지 않음
					# spark_zircon_b._upsert_job()에서 running=1로 바꿈
					_wait_for_job_running(triggered_job_ids)

			else:  # 여유분이 음수란 얘기는 진행 중인 작업의 개수가 _MAX_PARALLEL_LEVEL보다 많다는 뜻
				msg = f'-----------  _MAX_PARALLEL_LEVEL({_MAX_PARALLEL_LEVEL}) OVER  ing_cnt={ing_cnt} ------------\n'
				for ing in ing_list:
					msg = msg + f'\t{ing}\n'
				logging.warning(f'{_LOG_PREFIX}{msg}')

				raise AirflowException(
					f'진행중인 작업이 _MAX_PARALLEL_LEVEL({_MAX_PARALLEL_LEVEL})보다 많습니다. ing_cnt={ing_cnt}')

			sleep(15)


def _get_remained_list(ymd: str, dag_run_id: str) -> list:
	"""
	남은 작업 수 조회
	:return:
	"""
	filter = {
		'type': spark_zircon_b.DAG_ID,
		'datetime': ymd,
		'detail.kind': _KIND,
		'detail.dag_run_id': dag_run_id,
		'manual': 1,
		'retryCnt': -1
	}
	remained_list = job_dao.get_jobs(filter)
	return remained_list


def _get_ing_list(ymd: str, dag_run_id: str) -> list:
	"""
	진행중인 작업 수 조회
	:return:
	"""
	filter = {
		'type': spark_zircon_b.DAG_ID,
		'datetime': ymd,
		'detail.kind': _KIND,
		'detail.dag_run_id': dag_run_id,
		'manual': 1,
		'retryCnt': -1,
		'running': 1,
	}
	ing_list = job_dao.get_jobs(filter)
	return ing_list


def _get_standby_list(ymd: str, dag_run_id: str, spare_cnt: int) -> list:
	"""
	대기 작업 조회. spare_cnt 만큼만
	:return:
	"""
	filter = {
		'type': spark_zircon_b.DAG_ID,
		'datetime': ymd,
		'detail.kind': _KIND,
		'detail.dag_run_id': dag_run_id,
		'manual': 1,
		'retryCnt': -1,
		'running': 0,
	}
	sort = [('_id', 1)]
	standby_list = job_dao.get_jobs(filter, sort, spare_cnt)
	return standby_list


def _get_failed_list(**context) -> list:
	"""
	실패한 작업 조회
	:return:
	"""
	ymd_list = context['ti'].xcom_pull(key=XCOM_TARGET_YMD_LIST)
	filter = {
		'type': spark_zircon_b.DAG_ID,
		'datetime': {'$in': ymd_list},
		'detail.dag_run_id': context['dag_run'].run_id,
		'manual': 1,
		'retryCnt': {'$gte': 0},

	}
	failed_list = job_dao.get_jobs(filter)
	return failed_list


def _wait_for_job_running(triggered_job_ids: list):
	"""
	트리거 시킨 spark_dag_run과 관련된 Job.running = 0인 것이 하나라도 있다면 대기
	retryCnt:-1인 것만 조회. 실패한 것은 제외.
	혹은 너무 빨리 끝나서 job이 모두 없어졌다면 종료
	:param job_id:
	:return:
	"""
	sleep(10)
	while True:
		jobs = job_dao.get_jobs({'_id': {'$in': triggered_job_ids}, 'retryCnt': -1})
		if jobs:
			unrunning_cnt = 0
			for job in jobs:
				if job.get('running') == 0:
					unrunning_cnt = unrunning_cnt + 1

			logging.info(
				f'{_LOG_PREFIX} len(triggered_job_ids)={len(triggered_job_ids)} unrunning_cnt={unrunning_cnt}')
			if unrunning_cnt < 1:
				break
			else:
				sleep(10)
		else:
			break


def _refresh_recent_zircon_b_ymd():
	"""
	최근 Zircon B 적재 일자 갱신
	:return:
	"""
	logging.info(f'{_LOG_PREFIX} url={_REQ_URL}')
	res = utils.request(_REQ_URL)
	return res


def _conclude(**context):
	"""
	실패 건이 있을 경우 이메일 전송
	:return:
	"""
	has_failed = False

	my_dag_run_id = context['dag_run'].run_id
	failed_list = _get_failed_list(**context)
	if len(failed_list):
		has_failed = True

		failed_msg = f'dagRunId={my_dag_run_id}<br><br>'
		for job in failed_list:
			failed_msg += (f'date=<span style="color:crimson">{job.get("datetime")}</span> '
						   f'pub_id={job.get("detail").get("publisher_id")} '
						   f'ap_id={job.get("detail").get("adProvider_id")} '
						   f'silver_trace_id={job.get("detail").get("silverTrace_id")} '
						   f'zircon_trace_id={job.get("detail").get("zirconTrace_id")} '
						   f'job_id={job.get("_id")} '
						   f'<br>')

		title = f'[Zircon B 수동 생성에 실패 건이 있음] 건수={len(failed_list)}'
		body = f'{failed_msg}<br/>'
		send_email(ALERT_EMAIL_ADDRESSES, title, body)

		logging.info(f'{_LOG_PREFIX}Zircon B 생성 수동 생성에 실패한 작업이 있음. {len(failed_list)} 건.\n{body}\n')

	# trigger_zircon_b_all_manual에서 결과 조회를 위해 사용함
	context['ti'].xcom_push('has_failed', has_failed)


def timeout(**kwargs):
	sleep(180)  # 180초 후 종료


with DAG(
		DAG_ID,
		description='Zircon B 생성 수동 처리',
		default_args={
			'owner': 'juyoun.kim',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2024, 4, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		tags=['trigger', 'zircon', 'zirconb', 'zb', 'manual', 'juyoun.kim'],
		catchup=False,
		params={
			'from_target_ymd': '',  # YYYYMMDD
			'to_target_ymd': '',  # YYYYMMDD
			'target_ymd_list': '',  # YYYYMMDD를 콤마를 구분자로 열거
			'pub_id': '',  # 특정 PUB ID or "*"(모든 PUB. 수동처리 시에만 허용)
			'ap_id': '*',  # 특정 AP ID or "*" (모든 AP)
			'is_full_field': '0',  # "0" or "1"
		},
) as dag:
	dag.doc_md = __doc__

	# 필요한 정보 세팅
	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup
	)

	# 중복 실행중인 날짜가 있는지 체크
	check_dup_run = PythonOperator(
		task_id='check_dup_run',
		python_callable=_check_dup_run,
	)

	# timeout_task = PythonOperator(
	# 	task_id='timeout_task',
	# 	python_callable=timeout,
	# 	dag=dag,
	# )

	# 날짜별 처리
	process = PythonOperator(
		task_id='process',
		python_callable=_process,
	)

	# 최근 Zircon B 적재 일자 갱신
	refresh_recent_zircon_b_ymd = PythonOperator(
		task_id='refresh_recent_zircon_b_ymd',
		python_callable=_refresh_recent_zircon_b_ymd,
	)

	conclude = PythonOperator(
		task_id='conclude',
		python_callable=_conclude,
	)

	# 파이프라인
	setup >> check_dup_run >> process >> refresh_recent_zircon_b_ymd >> conclude
