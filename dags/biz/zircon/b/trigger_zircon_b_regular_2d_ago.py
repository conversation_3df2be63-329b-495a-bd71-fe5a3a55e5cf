"""
### <PERSON>ircon B 생성 정규 처리
- D-2 생성

#### 위키
- [18-11. [Trigger] Zircon B 생성 정규 - 1일/2일 전](https://wiki.navercorp.com/pages/viewpage.action?pageId=2388085571)

#### 주기
- 30 4,10,16,22 * * *
- 동시에 하나의 DAG RUN만 허용

#### config
- 없음
- 수동 실행을 위해서는 trigger_zircon_b_manual을 사용헤 주세요.
"""

import logging
import os
from datetime import timedelta
from time import sleep

import pendulum
from airflow import AirflowException
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.dummy import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.utils.types import DagRunType
from bson import ObjectId

from biz.performance.simple import trigger_simple_performance_regular
from biz.zircon.b import spark_zircon_b
from biz.zircon.b.spark_zircon_b import XCOM_KIND, XCOM_AP_ID, XCOM_PUB_ID
from core import c3
from core.airflow_api import trigger_dagrun
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YMD, XCOM_SILVER_TRACE_ID, \
	XCOM_ZIRCON_TRACE_ID, ZIRCON_B_GFP_HOME, ZIRCON_B_STATE_TOUCHED
from core.dao import job_dao, environment_dao, zircon_trace_dao
from core.dao.environment_dao import get_environment_value_by_name
from core.utils import prettyStr

# DAG 기본정보
DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRG-ZB-REGULAR-2D-AGO]'

# 디폴트 3시간
_ZIRCON_B_REGULAR_EXE_TIMEOUT = int(get_environment_value_by_name('zircon-b-regular-execution-timeout'))
_KIND = 'regular'


def _setup(**context):
	"""
	DAG 실행에 필요한 환경 설정 및 확인

	:param context:
	:return:
	"""
	_set_target_ymd(**context)  # 생성해야 할 일자 설정


def _set_target_ymd(**context):
	"""
	스케줄에 의한 파라미터로 결정된다.
	:param context:
	:return:
	"""
	'''
	- 스케줄이 "30 4,10,16 * * *"일 때
		* 4시에 도는 dag의 logical_date = 전날 22시를 가리키고
		* 그 외에 도는 dag의 logical_date = 당일 이전 주기 시간을 가리킨다.
	- 따라서 target_ymd를 올바로 설정하기 위해서는
		* logical_date의 시간이 22시라면 logical_date에서 1일을 빼서 쓰고
		  `context["logical_date"].in_tz(_DEFAULT_TZ).subtract(days=1).format("YYYYMMDD")`
		* 그 외라면 2일을 빼서 쓴다.
		  `context["logical_date"].in_tz(_DEFAULT_TZ).subtract(days=2).format("YYYYMMDD")`    
	'''
	logical_date = context["logical_date"].in_tz(DEFAULT_TZ)
	if logical_date.hour == 16:
		ymd = logical_date.subtract(days=1).format("YYYYMMDD")
	else:
		ymd = logical_date.subtract(days=2).format("YYYYMMDD")  # 이틀 전
	logging.info(f'{_LOG_PREFIX} 처리해야 할 날짜={ymd}')

	context['ti'].xcom_push(XCOM_TARGET_YMD, ymd)


def _get_publisher_ids(ymd: str) -> []:
	"""
	Zircon B GFP warehouse 특정 일자 경로 아래에 있는 유니크 매체ID 구하기
	:param ymd:
	:return:
	"""
	pub_ids = []
	zbgfp_root = f'{ZIRCON_B_GFP_HOME}/warehouse/{ymd[:4]}/{ymd[4:6]}/{ymd[6:8]}'
	hours = [str(i).zfill(2) for i in range(24)]
	for hour in hours:
		path = f'{zbgfp_root}/{hour}'
		pub_paths = c3.read_dir(path)
		for pub_path in pub_paths:
			pub = pub_path['pathSuffix']
			if pub.startswith('_publisherId='):
				pub_id = pub[pub.index('=') + 1:]
				pub_ids.append(pub_id)

	unique_pub_ids = list(set(pub_ids))
	logging.info(f'{_LOG_PREFIX} 처리할 매체')
	logging.info(prettyStr(unique_pub_ids))

	return unique_pub_ids


def _insert_zircon_b_jobs(**context):
	"""
	매체 개수만큼 job 추가
	:param context:
	:return:
	"""
	# D-1에 대한 정규처리
	ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	logging.info(f'{_LOG_PREFIX} 처리해야 할 날짜={ymd}')

	# ZBGFP에 생성된 publisherIds
	pub_ids = _get_publisher_ids(ymd)
	logging.info(f'{_LOG_PREFIX} 처리해야 할 매체 개수={len(pub_ids)}\n{pub_ids}')

	# 매체별로 spark_zircon_b job 생성
	for pub_id in pub_ids:
		filter = {
			'type': spark_zircon_b.DAG_ID,
			'datetime': ymd,
			'detail.kind': _KIND,
			'detail.publisher_id': ObjectId(pub_id),
			'manual': 0,
		}
		job = job_dao.get_job(filter)
		if job:
			# job이 이미 있으면 retryCnt=-1
			job_dao.update_job(filter, {
				'$set': {
					'retryCnt': -1,
					'modifiedAt': pendulum.now(DEFAULT_TZ)
				}
			})
			logging.warning(f'이미 있으므로 추가하지 않고 retryCnt=-1로 업데이트. job={job}')
		else:
			# job이 없다면 추가
			job = {
				'type': spark_zircon_b.DAG_ID,
				'datetime': ymd,
				'detail': {
					'kind': _KIND,
					'publisher_id': ObjectId(pub_id),
					'adProvider_id': None,
					'silverTrace_id': None,
					'zirconTrace_id': None,
				},
				'retryCnt': -1,
				'manual': 0,
				'running': 0,
			}
			job_dao.insert_job(job)

		_set_zircon_trace_to_touched(ymd, pub_id, '*')

	logging.info(f'{_LOG_PREFIX} Jobs 추가 완료. {len(pub_ids)} 건')


def _set_zircon_trace_to_touched(ymd: str, pub_id: str, ap_id: str):
	"""
	ZirconTrace.zirconBTouchedAt 업데이트
	Job 처리를 위해 감지했음을 표시

	ZirconTrace.zirconBTouchedAt를 만지는 곳은 지르콘을 수행하기 위해 Job을 생성하는 곳으로 아래와 같다.
		- trigger_zircon_b_regualr_xx
		- trigger_zircon_b_manual
		- trace_silvregrey

	trigger_zircon_b_reprocess는 이미 만들어진 job을 재처리하는 곳이므로 해당사항 없다.

	:param ymd:
	:param pub_id:
	:param ap_id:
	:return:
	"""
	match = {'date': ymd}
	if pub_id != '*':
		match['publisher_id'] = ObjectId(pub_id)
	if ap_id != '*':
		match['adProvider_id'] = ObjectId(ap_id)

	now = pendulum.now(DEFAULT_TZ)
	update = [{
		'$set': {
			'zirconBTouchedAt': now,
			'zirconBState': ZIRCON_B_STATE_TOUCHED,
			'modifiedAt': now
		}
	}]

	result = zircon_trace_dao.update_trace_many(match, update)
	logging.info(
		f'{_LOG_PREFIX} _set_zircon_trace_to_touched() match={match} result.modified_count={result.modified_count}')


def _run_parallel_spark_zircon_b(**context):
	"""
	날짜별/매체별 Zircon B 스파크 트리거
	:param context:
	:return:
	"""
	# 병렬도 설정
	value = environment_dao.get_environment_value_by_name('zircon-b-parallel-level')
	_MAX_PARALLEL_LEVEL = int(value) if value else 3  # 디폴트 3
	logging.info(f'{_LOG_PREFIX} _MAX_PARALLEL_LEVEL={_MAX_PARALLEL_LEVEL}')

	ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)

	start_time = pendulum.now(DEFAULT_TZ)
	triggered_cnt = 0
	while True:
		remained_list = _get_remained_list(ymd)
		logging.info(f'{_LOG_PREFIX} remained_list_len={len(remained_list)}')

		if len(remained_list) == 0:
			end_time = pendulum.now(DEFAULT_TZ)
			elapsed_time = end_time.diff(start_time)
			minutes = elapsed_time.in_minutes()
			seconds = elapsed_time.in_seconds() % 60
			logging.info(f'{_LOG_PREFIX}\t{ymd} 정규처리 종료. 소요시간 {minutes}분 {seconds}초\n')

			break
		else:
			# 진행중인 작업 목록 조회
			ing_list = _get_ing_list(ymd)
			ing_cnt = len(ing_list) if ing_list else 0

			# 동시 처리를 위한 여분 설정
			spare_cnt = _MAX_PARALLEL_LEVEL - ing_cnt
			if spare_cnt == 0:  # 여유분 없이 꽉 찼음
				pass
			elif spare_cnt > 0:  # 여유분이 있을 경우
				logging.info(f'{_LOG_PREFIX} 여유분={spare_cnt}')
				# 여유분 만큼 job 목록 가겨오기
				standby_list = _get_standby_list(ymd, spare_cnt)

				# 여유분만큼 스파크 트리거 병렬처리
				if standby_list:
					logging.info(f'{_LOG_PREFIX} standby_list_len={len(standby_list)}')
					triggered_job_ids = []
					for job in standby_list:
						job_id = job.get('_id')

						ymd = job.get('datetime')
						kind = job.get('detail').get('kind')
						pub_id = str(job.get('detail').get('publisher_id')) \
							if job.get('detail').get('publisher_id') else '*'
						ap_id = str(job.get('detail').get('adProvider_id')) \
							if job.get('detail').get('adProvider_id') else '*'
						s_trace_id = str(job.get('detail').get('silverTrace_id')) \
							if job.get('detail').get('silverTrace_id') else '-'
						zb_trace_id = str(job.get('detail').get('zirconTrace_id')) \
							if job.get('detail').get('zirconTrace_id') else '-'

						conf = {
							XCOM_TARGET_YMD: ymd,  # YYYYMMDD
							XCOM_KIND: kind,
							XCOM_PUB_ID: pub_id,
							XCOM_AP_ID: ap_id,
							XCOM_SILVER_TRACE_ID: s_trace_id,
							XCOM_ZIRCON_TRACE_ID: zb_trace_id
						}

						# 스파크 트리거
						spark_dag_run_id = trigger_dagrun(spark_zircon_b.DAG_ID,
														  pendulum.now(DEFAULT_TZ),
														  conf,
														  dag_run_type=DagRunType.SCHEDULED)

						triggered_job_ids.append(job_id)
						triggered_cnt = triggered_cnt + 1

						logging.info(
							f'{_LOG_PREFIX} {spark_zircon_b.DAG_ID} 트리거시킴. ({triggered_cnt}) {ymd} job_id={job_id} conf={conf} spark_dag_run_id={spark_dag_run_id}')

					# job이 running=1로 바뀔 때까지 대기
					# 그래야 다음 루프에서 _get_remained_list()를 했을 때 다시 추출되지 않음
					# spark_zircon_b._upsert_job()에서 running=1로 바꿈
					_wait_for_job_running(triggered_job_ids)

			else:  # 여유분이 음수란 얘기는 진행 중인 작업의 개수가 _MAX_PARALLEL_LEVEL보다 많다는 뜻
				msg = f'-----------  _MAX_PARALLEL_LEVEL({_MAX_PARALLEL_LEVEL}) OVER  ing_cnt={ing_cnt} ------------\n'
				for ing in ing_list:
					msg = msg + f'\t{ing}\n'
				logging.warning(f'{_LOG_PREFIX}{msg}')

				raise AirflowException(
					f'진행중인 작업이 _MAX_PARALLEL_LEVEL({_MAX_PARALLEL_LEVEL})보다 많습니다. ing_cnt={ing_cnt}')

			sleep(15)


def _get_remained_list(ymd: str) -> list:
	"""
	남은 작업 수 조회
	:return:
	"""
	filter = {
		'type': spark_zircon_b.DAG_ID,
		'datetime': ymd,
		'detail.kind': _KIND,
		'manual': 0,
		'retryCnt': -1,  # 정규 처리 대상만
	}
	remained_list = job_dao.get_jobs(filter)
	return remained_list


def _get_ing_list(ymd: str) -> list:
	"""
	진행중인 작업 수 조회
	:return:
	"""
	filter = {
		'type': spark_zircon_b.DAG_ID,
		'datetime': ymd,
		'detail.kind': _KIND,
		'manual': 0,
		'retryCnt': -1,  # 정규 처리 대상만
		'running': 1,
	}
	ing_list = job_dao.get_jobs(filter)
	return ing_list


def _get_standby_list(ymd: str, spare_cnt: int) -> list:
	"""
	대기 작업 조회. spare_cnt 만큼만
	:return:
	"""
	filter = {
		'type': spark_zircon_b.DAG_ID,
		'datetime': ymd,
		'detail.kind': _KIND,
		'manual': 0,
		'retryCnt': -1,  # 정규 처리 대상만
		'running': 0,
	}
	sort = [('_id', 1)]
	standby_list = job_dao.get_jobs(filter, sort, spare_cnt)
	return standby_list


def _wait_for_job_running(triggered_job_ids: list):
	"""
	트리거 시킨 spark_dag_run과 관련된 Job.running = 0인 것이 하나라도 있다면 대기
	retryCnt:-1인 것만 조회. 실패한 것은 제외.
	혹은 너무 빨리 끝나서 job이 모두 없어졌다면 종료
	:param job_id:
	:return:
	"""
	sleep(10)
	while True:
		jobs = job_dao.get_jobs({'_id': {'$in': triggered_job_ids}, 'retryCnt': -1})
		if jobs:
			unrunning_cnt = 0
			for job in jobs:
				if job.get('running') == 0:
					unrunning_cnt = unrunning_cnt + 1

			logging.info(f'{_LOG_PREFIX} len(triggered_job_ids)={len(triggered_job_ids)} unrunning_cnt={unrunning_cnt}')
			if unrunning_cnt < 1:
				break
			else:
				sleep(10)
		else:
			break


with DAG(
		DAG_ID,
		description='ZIRCON B 생성을 위한 정규 DAG. spark_zircon_b 트리거시킴',
		default_args={
			'owner': 'juyoun.kim',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		tags=['trigger', 'zircon', 'zirconb', 'zb', '2d.ago', 'regular', 'juyoun.kim'],
		start_date=pendulum.datetime(2024, 4, 1, tz=DEFAULT_TZ),
		# D-2 지르콘은 D-1보다 빨리 실행되어도 됨.
		# D-2 ZBGFP는 이미 생성되었으므로 ZB 작업만 하면 되므로.
		schedule_interval='30 1,4,10,16 * * *',
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	# D-1 날짜 설정
	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	# 매체별 Zircon B job 추가
	insert_zircon_b_jobs = PythonOperator(
		task_id='insert_zircon_b_jobs',
		python_callable=_insert_zircon_b_jobs,
	)

	# 병렬로 spark_zirocn_b 트리거
	# 모든 매체를 처리할 때까지 대기. 타임아웃 = 3시간
	run_parallel_spark_zircon_b = PythonOperator(
		task_id='run_parallel_spark_zircon_b',
		python_callable=_run_parallel_spark_zircon_b,
		execution_timeout=timedelta(seconds=_ZIRCON_B_REGULAR_EXE_TIMEOUT),
	)

	# done
	done = DummyOperator(
		task_id='done',
	)

	# trigger_simple_performance_regular 트리거
	trigger_simple_performance_regular = TriggerDagRunOperator(
		trigger_dag_id=trigger_simple_performance_regular.DAG_ID,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id=trigger_simple_performance_regular.DAG_ID,
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=False,
		reset_dag_run=True,
		conf={XCOM_TARGET_YMD: f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}'}
	)

	# 파이프라인
	setup >> insert_zircon_b_jobs >> run_parallel_spark_zircon_b >> done >> trigger_simple_performance_regular
