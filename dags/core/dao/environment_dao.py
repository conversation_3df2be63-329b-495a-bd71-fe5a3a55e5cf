import logging
from datetime import datetime
from typing import Any

import pendulum
from airflow.providers.mongo.hooks.mongo import MongoHook

from core.base import CONN_MONGO_FOR_DATA, DEFAULT_TZ

_COLLECTION = 'Environments'


def get_environment(name: str):
	"""
	name 으로 environment doc 가져오기
	:param name:
	:return:
	"""
	with Mongo<PERSON><PERSON>(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]
		doc = coll.find_one({'name': name})
		if doc:
			return doc
		else:
			logging.warning(f'name "{name}" not found in {_COLLECTION} collection')


def get_environment_value_by_name(name: str):
	"""
	name 으로 environment value 가져오기
	:param name:
	:return:
	"""
	with Mon<PERSON><PERSON><PERSON>(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]
		doc = coll.find_one({'name': name})
		if doc:
			return doc.get('value')
		else:
			logging.warning(f'name "{name}" not found in {_COLLECTION} collection')


def set_environment(name: str, value: Any):
	"""
	name으로 environment 설정
	:param name:
	:param value:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]
		coll.update_one(
			{'name': name},
			{'$set': {'value': value, 'modifiedAt': pendulum.now(tz=DEFAULT_TZ)}},
			upsert=True,
		)
		logging.info(f'{_COLLECTION}["{name}"] set to {value}')


def update_environment(name: str, update: dict):
	"""
	name 으로 environment 설정 ( set, unset, obj value 처리 등등 )
	:param name:
	:param update:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]
		result = coll.update_one(
			{'name': name},
			update,
			upsert=True,
		)

		logging.info(f'{_COLLECTION}["{name}"] update = {update}')

		return result


def is_ready_bronze_log(target_ymdh: str):
	"""
	브론즈 로그가 준비되었는지 확인
	:param target_ymdh:
	:return:
	"""
	return _is_ready_metal('bronze-log-recent-accumulation-ymdh', target_ymdh)


def is_ready_silver_log(target_ymdh: str):
	"""
	실버 로그가 준비되었는지 확인
	:param target_ymdh:
	:return:
	"""
	return _is_ready_metal('silver-log-recent-compaction-ymdh', target_ymdh)


def is_ready_gold_index(target_ymdh: str):
	"""
	골드 인덱스가 준비되었는지 확인
	:param target_ymdh:
	:return:
	"""
	return _is_ready_metal('gold-index-recent-accumulation-ymdh', target_ymdh)


def is_ready_zircon_b_gfp(target_ymd: str):
	"""
	Zircon B GFP 가 준비되었는지 확인
	:param target_ymd:
	:return:
	"""
	env_nm = 'zircon-b-gfp-recent-compaction-ymd'
	recent_ymd = get_environment_value_by_name(env_nm)
	if recent_ymd:
		if recent_ymd >= target_ymd:
			logging.info(f'{env_nm} detected: {recent_ymd}')
			return True
		else:
			logging.info(f'{env_nm} is not yet satisfied. recent_ymd={recent_ymd} target_ymd={target_ymd}')
	else:
		logging.warning(f'get NULL from document having name "{env_nm}"')
	return False


def is_ready_zircon_b(target_ymd: str):
	"""
	Zircon B가 준비되었는지 확인
	:param target_ymd:
	:return:
	"""
	env_nm = 'zircon-b-recent-compaction-ymd'
	recent_ymd = get_environment_value_by_name(env_nm)
	if recent_ymd:
		if recent_ymd >= target_ymd:
			logging.info(f'{env_nm} detected: {recent_ymd}')
			return True
		else:
			logging.info(f'{env_nm} is not yet satisfied. recent_ymd={recent_ymd} target_ymd={target_ymd}')
	else:
		logging.warning(f'get NULL from document having name "{env_nm}"')

	return False


def _is_ready_metal(env_nm: str, target_ymdh: str):
	"""
	골드 인덱스가 준비되었는지 확인
	:param target_ymdh:
	:return:
	"""
	if len(target_ymdh) == 8:  # YYYYMMDD
		ymdh = f'{target_ymdh}23'
	else:  # YYYYMMDDHH
		ymdh = target_ymdh

	recent_ymdh = get_environment_value_by_name(env_nm)
	if recent_ymdh:
		if recent_ymdh >= ymdh:
			logging.info(f'{env_nm} detected: {recent_ymdh}')
			return True
		else:
			logging.info(f'{env_nm} is not yet satisfied. recent_ymdh={recent_ymdh} target_ymdh={ymdh}')
	else:
		logging.warning(f'get NULL from document having name "{env_nm}"')
	return False


def record_adprovider_performance_batch_done():
	"""
	NAM 광고공급자 성과 리포트 완료 시간 업데이트
	:return:
	"""
	set_environment('report-performance-ad-provider-last-batch-end-datetime', datetime.utcnow())


def record_adunit_performance_batch_done():
	"""
	NAM 광고유닛 성과 리포트 완료 시간 업데이트
	:return:
	"""
	set_environment('report-performance-ad-unit-last-batch-end-datetime', datetime.utcnow())


def check_delay_limit_exceed(cnt: int):
	"""
	허용 가능한 실버 로그 적재 지연 최대 시간을 넘었는지 확인
	:param cnt:
	:return:
	"""
	limit_hours = get_environment_value_by_name('silver-log-max-tolerable-delay-hours')
	return cnt > limit_hours, limit_hours


def get_nubes_backup_data_types():
	"""
	Nubes 백업의 대상이 되는 파일타입의 목록을 반환

	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]

		# aggregation 수행
		match = {
			'$match': {
				'name': 'backup-nubes-config'
			}
		}

		# document 의 dataInfo 를 Array 로 (dataInfo 하위의 key 들이 원하는 정보)
		project1 = {
			'$project': {
				'dataInfo': {
					'$objectToArray': '$value.dataInfo'
				}
			}
		}

		# Array unwind
		unwind = {
			'$unwind': "$dataInfo"
		}

		# unwind 한 내용 중 key 값만 project
		project2 = {
			'$project': {
				'_id': 0,
				'dataType': '$dataInfo.k'
			}
		}

		aggregation_pipeline = [match, project1, unwind, project2]
		cursor = coll.aggregate(aggregation_pipeline)

		if cursor:
			docs = list(cursor)  # [{'dataType': 'silver'}, {'dataType': 'zircon_b'}]
			return [doc['dataType'] for doc in docs]
		else:
			logging.warning(f'get_nubes_backup_data_types() failed')


def get_nubes_backup_time_info(data_type: str):
	"""
	파일의 타입에 따라 nubes 백업의 주기 및 대상을 추정하기 위한 날짜/시간 정보를 가져옴
	현재 Envrionments 에 존재하는 관련 정보는 아래 뿐.

	- silver 	: silver-log-recent-compaction-ymdh
	- zircon_b  : zircon-b-recent-compaction-ymd

	:param data_type:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]

		# aggregation 수행
		match = {
			'$match': {
				'name': 'backup-nubes-config'
			}
		}

		# backup-nubes-config 의 data_type 별 referDocName 에 맞는 Envrionmets 다큐먼트를 찾음
		lookup = {
			'$lookup': {
				'from': _COLLECTION,
				'localField': f'value.dataInfo.{data_type}.referDocName',
				'foreignField': 'name',
				'as': 'matched_docs'
			}
		}

		# 'silver-log-recent-compaction-ymdh' 등의 다큐먼트
		unwind = {
			'$unwind': '$matched_docs'
		}

		# 해당 다큐먼트의 value 와 원래 다큐먼트의 value 중 data_type 에 맞는 offset, base_path 정보를 리턴
		project = {
			'$project': {
				'_id': 0,
				'baseDt': '$matched_docs.value',
				'backupOffset': f'$value.dataInfo.{data_type}.backupOffset',
				'deleteOffset': f'$value.dataInfo.{data_type}.deleteOffset'
			}
		}

		aggregation_pipeline = [match, lookup, unwind, project]
		cursor = coll.aggregate(aggregation_pipeline)

		if cursor:
			doc = list(cursor)[0]
			return {
				'baseDt': doc.get('baseDt'),
				'backupOffset': doc.get('backupOffset'),
				'deleteOffset': doc.get('deleteOffset')
			}
		else:
			logging.warning(f'get_nubes_backup_time_info() failed')
