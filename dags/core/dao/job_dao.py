"""
PyMongo 3.12.3 documentation » API Documentation
    https://pymongo.readthedocs.io/en/3.12.3/api/pymongo/index.html
"""
import logging
from typing import List

import pymongo
from airflow import AirflowException
from airflow.models import DagRun
from airflow.providers.mongo.hooks.mongo import MongoHook
from airflow.utils.state import DagRunState
from airflow.utils.types import DagRunType
from bson import ObjectId
from pymongo.results import InsertOneResult, UpdateResult, DeleteResult
from sqlalchemy_utils.types.enriched_datetime.pendulum_datetime import pendulum

from core.base import CONN_MONGO_FOR_DATA, DEFAULT_TZ
from core.dao.environment_dao import get_environment_value_by_name

_COLL_JOBS = 'Jobs'
_JOB_TYPE = 'spark_silversmith'
MAX_RETRY_CNT = int(get_environment_value_by_name('job-max-retry-cnt'))


def set_to_run_flag(type: str, dt: str, running: int):
	"""
	Jobs.running 설정

	:param type:
	:param dt:
	:param running:
	:return:
	"""
	filter = {'type': type, 'datetime': dt}
	update = {'$set': {'running': running, 'modifiedAt': pendulum.now(tz=DEFAULT_TZ)}}
	update_job(filter, update)
	logging.info(f'{dt} set running={running}')


def clean_up(type: str, dt: str, is_success: bool):
	"""
	성공하면 Jobs 다큐먼트 삭제
	실패하면 Jobs.retryCnt를 증가시키고 Jobs.running=0 설정
	:return:
	"""
	filter = {'type': type, 'datetime': dt}
	if is_success:
		# 성공하면 Jobs 다큐먼트 삭제
		delete_job(filter)
		logging.info(f'{dt} 성공. Jobs 다큐먼트 삭제')
	else:
		# 실패하면 retryCnt를 증가시키고 running=0 설정
		update = {
			'$set': {'running': 0, 'modifiedAt': pendulum.now(tz=DEFAULT_TZ)},
			'$inc': {'retryCnt': 1}
		}
		update_job(filter, update)
		logging.info(f'{dt} 실패. retryCnt 증가시킴')


def clean_up_by_filter(filter: dict, is_success: bool):
	"""
	성공하면 Jobs 다큐먼트 삭제
	실패하면 Jobs.retryCnt를 증가시키고 Jobs.running=0 설정
	:return:
	"""
	if is_success:
		# 성공하면 Jobs 다큐먼트 삭제
		delete_job(filter)
		logging.info(f'{filter} 성공. Jobs 다큐먼트 삭제')
	else:
		# 실패하면 retryCnt를 증가시키고 running=0 설정
		update = {
			'$set': {'running': 0, 'modifiedAt': pendulum.now(tz=DEFAULT_TZ)},
			'$inc': {'retryCnt': 1}
		}
		update_job(filter, update)
		logging.info(f'{filter} 실패. retryCnt 증가시킴')


def get_jobs(filter: dict, sort: list = [('type', pymongo.ASCENDING), ('datetime', pymongo.ASCENDING)],
			 limit: int = None) -> list:
	"""
	filter에 해당하는 job 목록 가져오기
	type, datetime 오름차순
	:param filter:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLL_JOBS]

		if limit:
			cursor = coll.find(filter).sort(sort).limit(limit)
		else:
			cursor = coll.find(filter).sort(sort)

		return list(cursor)


def get_job(filter: dict, sort: list = [('datetime', pymongo.ASCENDING)]) -> dict:
	"""
	filter에 해당하는 job datetime 순으로 정렬하여 1 건 조회.
	:param filter:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLL_JOBS]

		cursor = coll.find(filter).sort(sort).limit(1)
		doc_list = list(cursor)

		doc = doc_list[0] if doc_list else None
		logging.info(f'get_job() job = {doc}')

		return doc


def insert_job(doc: dict) -> ObjectId:
	"""
	job 1건 추가
	createdAt, modifiedAt은 디폴트로 설정됨.
	:param doc:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLL_JOBS]

		now = pendulum.now(tz=DEFAULT_TZ)
		doc['createdAt'] = now
		doc['modifiedAt'] = now

		result: InsertOneResult = coll.insert_one(doc)
		logging.info(f'{_COLL_JOBS} {doc} inserted. inserted_id={result.inserted_id}')

		return result.inserted_id


def update_job(filter: dict, update: dict):
	"""
	filter에 해당하는 job의 내용을 update의 내용으로 변경.
	1 건 변경
	:param filter:
	:param update:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLL_JOBS]

		result: UpdateResult = coll.update_one(filter, update, upsert=True)
		if result.modified_count > 0:
			logging.info(f'{_COLL_JOBS} {filter} doc is updated. update={update}. '
						 f'result.matched_count={result.matched_count}, '
						 f'result.modified_count={result.modified_count}, '
						 f'result.upserted_id={result.upserted_id}')
		else:
			logging.warning(f'{_COLL_JOBS}: {filter} doc is not updated. update={update}. '
							f'result.matched_count={result.matched_count}, '
							f'result.modified_count={result.modified_count}, '
							f'result.upserted_id={result.upserted_id}')


def delete_job(filter: dict):
	"""
	filter에 해당하는 job 1 건 삭제
	:param filter:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLL_JOBS]

		result: DeleteResult = coll.delete_one(filter)
		if result.deleted_count > 0:
			logging.info(f'{_COLL_JOBS} {filter} doc is deleted. result={result}')
		else:
			logging.warning(f'{_COLL_JOBS}: {filter} doc is not delete. result={result}')


def delete_jobs(filter: dict):
	"""
	filter 에 해당하는 job N 건 삭제
	:param filter:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLL_JOBS]

		result: DeleteResult = coll.delete_many(filter)
		if result.deleted_count > 0:
			logging.info(f'{_COLL_JOBS} {filter} doc is deleted. result.deleted_count={result.deleted_count}')
		else:
			logging.warning(f'{_COLL_JOBS}: {filter} doc is not delete.')


def check_dup_run(dag_id, xcom_target_dt_key, running_task_id='setup', **context):
	"""
	중복 실행을 방지하기 위한 방어 로직

	:param dag_id:
	:param xcom_target_dt_key:
	:param running_task_id:
	:param context:
	:return:
	"""
	target_dt = context['ti'].xcom_pull(key=xcom_target_dt_key)
	doc = get_job({'type': dag_id, 'datetime': target_dt, 'running': 1})

	if doc:
		running_dag_runs: list[DagRun] = DagRun.find(dag_id=dag_id, state=DagRunState.RUNNING)
		for running_dag_run in running_dag_runs:
			ti = running_dag_run.get_task_instance(running_task_id)
			running_target_dt = ti.xcom_pull(key=xcom_target_dt_key)

			# 현재 이 함수를 실행 중인 dag_run 의 context 로부터 조회한 run_id 와
			# Jobs 컬렉션에서 실행 상태로 존재하는 dag_run 의 run_id 비교 (이 경우 run_id 는 airflow 에서 제공하는 함수로 알아냄)
			if context['dag_run'].run_id != running_dag_run.run_id and target_dt == running_target_dt:
				raise AirflowException(f'{_COLL_JOBS} 이미 실행 중인 DagRun 이 있음. '
									   f'dagRunId=({running_dag_run.run_id}'
									   f'target_ymd={running_target_dt})')
	else:
		pass


def upsert_job(dag_id, xcom_target_dt_key, **context):
	"""
	해당 시간대의 Job 추가 또는 running=1 로 업데이트

	:param dag_id:
	:param xcom_target_dt_key:
	:param context:
	:return:
	"""
	target_dt = context['ti'].xcom_pull(key=xcom_target_dt_key)
	exist_job = get_job({'type': dag_id, 'datetime': target_dt})
	if exist_job:
		set_to_run_flag(dag_id, target_dt, 1)
	else:
		new_job = {
			'type': dag_id,
			'datetime': target_dt,
			'retryCnt': -1,
			'running': 1,
			'manual': 0 if context['dag_run'].run_type == DagRunType.SCHEDULED else 1
		}
		insert_job(new_job)


def get_last_job_before(dt: str, extra_filter: dict = None) -> dict:
	"""
	dt 에 해당하는 일시보다 이전의 job 중 가장 최근의 job 조회
	:param dt:
	:param extra_filter:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLL_JOBS]

		dt_filter = {'datetime': {'$lt': dt}}
		final_filter = {**dt_filter, **extra_filter}

		cursor = coll.find(final_filter).sort('datetime', pymongo.DESCENDING).limit(1)
		doc_list = list(cursor)

		doc = doc_list[0] if doc_list else None
		logging.info(f'get_job() job = {doc}')

		return doc
