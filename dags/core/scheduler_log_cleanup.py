import logging
import os
import pathlib
import subprocess
import sys

_ENV_SCHEDULER_LOG_DIR = 'LOG_CLEANUP_SCHEDULER_LOG_DIR'
_ENV_MAX_AGE_IN_DAYS = 'LOG_CLEANUP_MAX_AGE_IN_DAYS'

DFT_SCHEDULER_LOG_DIR = '/opt/airflow/logs/scheduler'
DFT_MAX_AGE_IN_DAYS = 5

logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()]
)


def _run_bash_cmd(cmd: str, **kwargs):
    logging.info(f'CMD: {cmd}')

    return subprocess.run(
        ["bash", "-c", cmd],
        text=True,
        **kwargs
    )


def _cleanup(log_dir: str, max_age_in_days: int):
    logging.info(f'max_age={max_age_in_days} 가 지난 scheduler log 삭제. dir={log_dir}, ')

    find_cmd = f'find {log_dir}/* -type f -mtime +{max_age_in_days}'
    _run_bash_cmd(find_cmd)

    rm_cmd = f'{find_cmd} -exec rm -f {{}} \;'
    _run_bash_cmd(rm_cmd)

    find_cmd = f'find {log_dir}/* -type d -empty'
    _run_bash_cmd(find_cmd)

    rm_cmd = f'{find_cmd} -prune -exec rm -rf {{}} \;'
    _run_bash_cmd(rm_cmd)


if __name__ == '__main__':
    log_dir = os.environ.get(_ENV_SCHEDULER_LOG_DIR)
    if log_dir:
        logging.info(f'scheduler log dir (env={_ENV_SCHEDULER_LOG_DIR}): {log_dir}')
    else:
        log_dir = DFT_SCHEDULER_LOG_DIR
        logging.info(f'scheduler log dir (default): {log_dir}')

    if not pathlib.Path(log_dir).is_dir():
        logging.error(f'{log_dir} 해당 경로가 디렉토리가 아닙니다.')
        sys.exit(1)

    max_age_in_days = os.environ.get(_ENV_MAX_AGE_IN_DAYS)
    if max_age_in_days:
        max_age_in_days = int(max_age_in_days)
        logging.info(f'max age (env={_ENV_MAX_AGE_IN_DAYS}): {max_age_in_days}')
    else:
        max_age_in_days = DFT_MAX_AGE_IN_DAYS
        logging.info(f'max age (default): {max_age_in_days}')

    _cleanup(log_dir, max_age_in_days)
