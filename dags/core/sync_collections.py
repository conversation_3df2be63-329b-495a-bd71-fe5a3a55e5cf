import logging
import os
from datetime import datetime, timed<PERSON>ta

import pendulum
from airflow.decorators import dag, task
from airflow.models.variable import Variable
from airflow.operators.dummy import DummyOperator
from airflow.providers.mongo.hooks.mongo import MongoHook

from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, CONN_MONGO_FOR_CMS, CONN_MONGO_FOR_DATA
from core.dao.collection_dao import do_sync, delete_differences_from_target

DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")

# JSON
# {
#   "sourceCollections": ["coll1", "coll2", "coll3"],
#   "incrementalSyncCollections": ["coll1"],
#   "completeSyncCollections": ["coll2"],
#   "targetPrefix": "Sync", "targetSuffix": ""
# }
VAR_SYNC_COLLECTIONS = 'sync_collections'

KEY_SYNC_COLLECTIONS = 'sourceCollections'
KEY_INC_SYNC_COLLECTIONS = 'incrementalSyncCollections'
KEY_CMPL_SYNC_COLLECTIONS = 'completeSyncCollections'

INC_SYNC_DAYS_AGO = 3


@dag(
    dag_id=DAG_ID,
    description='Sync NAM collections between CMS and API',
    tags=['sync', 'collection'],
    default_args={
        'owner': 'ins.cho',
        'email': ALERT_EMAIL_ADDRESSES,
        'email_on_failure': True,
    },
    start_date=pendulum.datetime(2022, 3, 1, tz=DEFAULT_TZ),
    schedule_interval='*/30 * * * *',
    catchup=False,
)
def define_dag():
    spec = Variable.get(VAR_SYNC_COLLECTIONS, deserialize_json=True)
    incCollections = spec[KEY_INC_SYNC_COLLECTIONS] if KEY_INC_SYNC_COLLECTIONS in spec else []
    cmplCollections = spec[KEY_CMPL_SYNC_COLLECTIONS] if KEY_CMPL_SYNC_COLLECTIONS in spec else []

    curr_op = DummyOperator(task_id='start')
    for name in spec[KEY_SYNC_COLLECTIONS]:
        @task(task_id=f'sync_{name.lower()}', retries=1, retry_delay=timedelta(seconds=10))
        def sync_collection(src_cname, dest_cname, execution_date: datetime = None):
            if src_cname in cmplCollections:
                tmp_cname = f'{dest_cname}_AF'
                with MongoHook(CONN_MONGO_FOR_CMS) as cms_hook:
                    with MongoHook(CONN_MONGO_FOR_DATA) as api_hook:
                        api_client = api_hook.get_conn()

                        api_client.get_database()[tmp_cname].drop()
                        logging.info(f'drop: {tmp_cname}')

                        (updated, inserted) = do_sync(
                            cms_hook.get_conn().get_database()[src_cname],
                            api_client.get_database()[tmp_cname],
                            {}
                        )
                        logging.info(f'sync: {src_cname} -> {tmp_cname}: {updated} updated, {inserted} inserted')

                        deleted = delete_differences_from_target(
                            api_client.get_database()[dest_cname],
                            api_client.get_database()[tmp_cname],
                            False
                        )
                        logging.info(f'delete diff: {dest_cname} - {tmp_cname}: {deleted} deleted')

                        (updated, inserted) = do_sync(
                            api_client.get_database()[tmp_cname],
                            api_client.get_database()[dest_cname],
                            {}
                        )
                        logging.info(f'sync: {tmp_cname} -> {dest_cname}: {updated} updated, {inserted} inserted')
            else:
                filter = {}
                if src_cname in incCollections:
                    filter = {
                        'modifiedAt': {
                            '$gte': execution_date + timedelta(days=-INC_SYNC_DAYS_AGO)
                        }
                    }
                with MongoHook(CONN_MONGO_FOR_CMS) as cms_hook:
                    with MongoHook(CONN_MONGO_FOR_DATA) as api_hook:
                        (updated, inserted) = do_sync(
                            cms_hook.get_conn().get_database()[src_cname],
                            api_hook.get_conn().get_database()[dest_cname],
                            filter
                        )
                logging.info(f'sync: filter={filter}')
                logging.info(f'sync: {src_cname} -> {dest_cname}: {updated} updated, {inserted} inserted')

        target_name = f'{spec["targetPrefix"]}{name}{spec["targetSuffix"]}'
        next_op = sync_collection(name, target_name)

        curr_op >> next_op
        curr_op = next_op


main_dag = define_dag()
