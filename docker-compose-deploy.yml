version: "3"

services:

# ssp-batch
  ssp-batch:
    image: reg.navercorp.com/gfp/ssp-batch:__BRANCH__-__NODE_ENV__
    environment:
      NODE_ENV: __NODE_ENV__
      HOST_HOSTNAME: $HOSTNAME
    command: pm2-runtime start --no-auto-exit pm2.process.config.js
    ports:
      - 12000:3000
      - 3000:3000
    volumes:
      - /home1/irteam/logs/server:/home1/irteam/logs/server
      - /home/<USER>/download:/home1/irteam/deploy/download
      - /home/<USER>/local_download:/home1/irteam/deploy/local_download
      - /home/<USER>/sparkling:/home1/irteam/sparkling
      - /home1/irteam/logs/sparkling:/home1/irteam/logs/sparkling
    stop_grace_period: 1h
    networks:
          - shared

# nginx + naver auth module
  nginx:
    image: reg.navercorp.com/gfp/nginx-1.13.8:1.0.2
    ports:
      - 80:80
      - 443:443
    volumes:
      - /home1/irteam/logs/nginx:/home1/irteam/apps/nginx/logs
      - /home1/irteam/deploy/nginx.conf:/home1/irteam/apps/nginx/conf/nginx.conf
      - /home1/irteam/deploy/ssl/cert_chain.pem:/home1/irteam/apps/nginx/ssl/cert_chain.pem
      - /home1/irteam/deploy/ssl/private.key:/home1/irteam/apps/nginx/ssl/private.key
    links:
      - ssp-batch
    stop_grace_period: 1h
    networks:
      - shared

networks:
  shared:
    driver: bridge
    ipam:
     config:
       - subnet: *********/10
