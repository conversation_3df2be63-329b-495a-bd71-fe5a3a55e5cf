#FROM reg.navercorp.com/gfp/airflow-nam:2.2.5
FROM reg.navercorp.com/gfp/airflow-nam:2.2.5-2023062910

# User: airflow (uid=50000, gid=0, suppl_gid=50000)
# CWD : /opt/airflow
# Home: /home/<USER>
#
# AIRFLOW_HOME: /opt/airflow
#
# Packages: /home/<USER>/.local/lib/python3.8/site-packages

# USER root
# Install system libraries if any

COPY --chown=airflow:root requirements-airflow.txt requirements.txt

USER airflow
RUN pip install -r requirements.txt

RUN echo -e "\nalias l='ls -al'\nalias k='kubectl'" >> $HOME/.bashrc

#RUN n2c install -f
#RUN airflow connections import connections.json
#RUN airflow pools import pools.json
