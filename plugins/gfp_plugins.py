import datetime

from airflow.plugins_manager import AirflowPlugin
from airflow.models.baseoperator import Base<PERSON>perator, BaseOperatorLink
from airflow.models.taskinstance import TaskInstance

from core.operators.invoke_batch import GfpBatchOperator
from core.operators.gfp_python_operator import GfpPythonOperator

class GfpSparkBatchLink(BaseOperatorLink):
    operators = [GfpBatchOperator, GfpPythonOperator]

    @property
    def name(self) -> str:
        return 'Tracking URL'

    def get_link(self, task: BaseOperator, dttm: datetime.datetime):
        ti = TaskInstance(task=task, execution_date=dttm)
        return ti.xcom_pull(task_ids=task.task_id, key='tracking_url')
        # if not trackingUrl:
        #     try:
        #         return json.loads(
        #             ti.xcom_pull(task_ids=task.task_id, key=XCOM_RETURN_KEY)
        #         ).get('tracking_url')
        #     except:
        #         return None



class GfpPlugin(AirflowPlugin):
    name = 'gfp_plugins'
    operator_extra_links = [GfpSparkBatchLink(),]
