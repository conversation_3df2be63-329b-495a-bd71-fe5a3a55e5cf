import time
from datetime import datetime, timedelta

import requests
from bson.objectid import ObjectId
from pymongo import MongoClient

dbUrl = '**************************************************************************************************************************'
dbName = 'ssp'

client = MongoClient(dbUrl)
db = client[dbName]
coll_name = 'SummaryRevenueSharingReports'

# 배치 서버
# phase_beta = 'localhost'
phase_real = '**************'

'''
BETA
    publisher_id: 62932daa088af6001da85d31(GMARKET), schedule_id: 6475c0b34db6239e58c4d49d, keyGroup_id: 62932daa088af6001da85d32, beginYmd: 20221212

REAL
    publisher_id: 6371eb7b33337f002e664f20(애드팝콘), schedule_id: 639985f86a93f789ce4b93bb, keyGroup_id: 6371eb7b33337f002e664f21, beginYmd: 20221212
'''
# 수익쉐어 리포트 스케줄ID
schedule_id = '639985f86a93f789ce4b93bb'


def _is_complete(ymd):
    """
    날짜별 성과리포트(광고공급자) 조회
    :param ymd:
    :return:
    """
    coll = db[coll_name]

    filter = {
        'summaryRevenueSharingSchedule_id': ObjectId(schedule_id),
        'beginYmd': ymd,
    }
    report = coll.find_one(filter, {'progress': 1})
    # print(f'[{datetime.now().strftime("%Y.%m.%d %H:%M:%S")}] date={ymd} report={report}')

    if report:
        if report['progress'] == 'COMPLETE':
            return True
        else:
            return False
    else:
        return False


def _run():
    start_time = datetime(2023, 4, 1, 0, 0, 0)  # inclusive
    end_time   = datetime(2023, 6, 6, 0, 0, 0)  # inclusive
    curr_time = start_time

    while curr_time <= end_time:
        mydate = curr_time.strftime("%Y%m%d")

        print(f'\n[{datetime.now().strftime("%Y.%m.%d %H:%M:%S")}] ymd={mydate} 시작 ...')
        begin_time = datetime.now()

        # 환경 설정
        begin_md = mydate
        end_ymd = mydate

        # 수익쉐어 리포트 재처리 호출
        url = f"http://{phase_real}:12000/batch/report/revenuesharing/csv?isCheckPeriod=false&isForce=true&scheduleId={schedule_id}&beginYmd={begin_md}&endYmd={end_ymd}"
        # print(f'url={url}')
        response = requests.get(url)

        # 결과
        if response.status_code == 200:
            print(f'response.status_code = OK')
            print(f'response.body = {response.json()}')
        else:
            print(f'response.status_code = NOT OK')
            print(f'response.body = {response.json()}')

        time.sleep(10)  # 런칭될 때까지 약간 기다림

        # 리포트 상태 조회
        while not _is_complete(mydate):
            time.sleep(15)

        # 종료 및 소요시간 출력
        elapsed_time = datetime.now() - begin_time
        print(f'[{datetime.now().strftime("%Y.%m.%d %H:%M:%S")}] ymd={mydate} 종료. elapsed_time={elapsed_time}')

        # 다음 날짜 설정
        curr_time = curr_time + timedelta(days=1)


_run()
print('done')
