'use strict';

// Set the 'dev' environment configuration object
module.exports = {
	gracefulWaitTime: 0,

	// server port
	port: '12000',

	// CMS MongoDB - Replica Set - dev
	db_url: '***********************************************************************************************************',

	// Data MongoDB - Sharded Cluster
	data_db_url: '***************************************************************************************************************************************************',

	db_poolsize: 20,

	// logger
	log_level: 'debug',
	log_dir: '/home1/irteam/logs/server',

	neon_url: 'https://nfidev.navercorp.com:5002',

	nam_api_url: 'http://app-test.nam-api.svc.ad1.io.navercorp.com:8080/v1',

	// nelo
	nelo: {
		url: 'http://alpha-col.nelo2.navercorp.com/_store',
		projectName: 'DEV_da_ssp_batch',
		projectVersion: '1.0.0',
		transfer:true,
		silent:false
	},

	nubes: {
		nam_api: {
			bucket: 'nam_api_dev',
			gatewayAddress: 'a-dev.nubes.sto.navercorp.com:8000',
		},
		gfp_data: {
			bucket: 'gfp-data',
			gatewayAddress: 'a-dev.nubes.sto.navercorp.com:8000',
		}
	},

	nclavis: {
		url: 'https://dev-apis.nclavis.navercorp.com/kms/consumer',
		keyResourceId: '4HXcsOPwA43So3r16DxORi6NP7I=', // 서비스(GFP), 키(gfp-report)
	},

	c3: {
		home: '/home1/irteam/apps/c3',
		namespace: 'bizcloud',
		user: 'gfp-data',
		realm: 'C3X.NAVER.COM',
		pw: '1:Om1pVezYWhzcLB4N:i6ygsjVxJwYNE4wWTVRrzhJLFCaJAp5m',
		ccname: '/home1/irteam/apps/c3/etc/krb5cc', // ccache(credential cache) name

		protocol: 'http',
		host: 'adevthm002-sa.nfra.io', // adevthm003-sa.nfra.io
		port: 50070,
		path_prefix: '/webhdfs/v1',

		active_namenode: true, // true 인 경우, active namenode 찾아서 요청. false 인 경우, host 그대로 사용
		namenodes: ['adevthm002-sa.nfra.io', 'adevthm003-sa.nfra.io']
	},

	cms: {
		api: {
			url: 'https://gfp-admin-api-test.io.naver.com/api/',
		}
	},

	// AdProvider 리포트 API 연동 정보
	report_api: {
		gfd: {
			ext: {
				nubes: {
					gatewayAddress: 'a-dev.nubes.sto.navercorp.com:8000',
				},
			}
		},

		ndp: {
			ext: {
				report_request_ready_api: 'http://test.media.da.navercorp.com/api/report/ndpforgfp/status?ymd={{YYYYMMDD}}',
			}
		},
	},

	report: {
		revenue_sharing: {
			local_root: '/home1/irteam/deploy/local_download',
		},
		creator_advisor: {
			local_root: '/home1/irteam/deploy/local_download',
		},
		multi_dimensional: {
			local_root: '/home1/irteam/deploy/local_download',
			sparkling: {
				home: '/home1/irteam/sparkling'
			}
		},
		discrepancy: {
			// zip 커맨드는 임시파일을 만드는 과정이 있는데, 마운트된 네트워크 드라이브에서는 임시파일을 만들 수 없음
			// 불일치 리포트는 배치서버의 owfs 가 마운트 된 경로('~/deploy/download')에 생성되므로 zip 커맨드에서 옵션으로 임시 디렉토리를 지정함
			// 임시경로에는 파일이 남지 않음
			tmp_for_zip: '/home1/irteam/deploy/local_download/report/discrepancy_report',
		}
	}
};
