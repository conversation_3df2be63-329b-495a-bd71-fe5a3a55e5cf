'use strict';

import * as _ from 'lodash';
import moment from 'moment';

import * as logger from '../utils/logger.util';

import { BusinessError } from '../common/error';

import * as monitoringService from '../services/monitoring/monitoring.service';
import * as monitoringRevenueService from '../services/monitoring/monitoring-revenue.service';


module.exports.makeNccReportApiStats = async (ctx) => {
	const txId = Math.random();
	const query = ctx.request.query;
	let body;

	let date = moment().add(-1, 'days').format('YYYYMMDD');
	try {
		if (!_.isNil(query.date)) {
			date = query.date;
		}

		logger.debug(`monitoring.controller.makeNccReportApiStats() received.. txId:${txId} datetime:${date}`);
		await monitoringService.makeNccReportApiStats(txId, date);
		logger.debug(`monitoring.controller.makeNccReportApiStats() done. txId:${txId} datetime:${date}`);

		body = {
			code: 200,
			message: `Ncc Report Api's File 통계 생성 완료.`
		}
	} catch(e) {
		logger.error(`monitoring.controller.makeNccReportApiStats() txId:${txId} datetime:${date} ${e.stack}`);

		body = {
			code: 500,
			message: `Ncc Report Api's File 통계 생성 에러. txId:${txId} datetime:${date} ${e}`
		}
	}

	ctx.body = body;
};


/**
 * sendMailMonitoringZirconB : 모니터링 지르콘B 알림 메일 발송
 *
 * @RequestMapping(value='/batch/monitoring/zircon/b/send_mail')
 */
module.exports.sendMailMonitoringZirconB = async (ctx) => {
	logger.debug(`[monitoring.controller :: sendMailMonitoringZirconB] ${ctx.path} 호출됨`);

	try {
		const date = ctx.request.query.date;

		// Validation. 날짜 포맷 (YYYYMMDD)
		if (date && !moment(date, 'YYYYMMDD', true).isValid()) {
			throw new BusinessError({ message: `[monitoring.controller :: sendMailMonitoringZirconB] date format is invalid ( date= ${date} )` });
		}

		// 모니터링 지르콘B 알림 메일 발송
		await monitoringService.sendMailMonitoringZirconB(date);

		ctx.body = { code: 200, message: `모니터링 지르콘B 알림 메일 발송 완료` };
	} catch (e) {
		logger.error(`[monitoring.controller :: sendMailMonitoringZirconB] Error :: \n ${e.stack}\n\n`, e);

		ctx.body = { code: 500, message: `모니터링 지르콘B 알림 메일 발송 실패` };
	}
}

/**
 * monitoringRevenue : 수익률 모니터링 알림 메일 발송
 *
 * @RequestMapping(value='/batch/monitoring/revenue')
 */
module.exports.monitorRevenue = async (ctx) => {
	logger.debug(`[monitoring.controller :: monitorRevenue] ${ctx.path} 호출됨`);

	try {
		const startDate = ctx.request.query.startDate;
		const endDate = ctx.request.query.endDate;

		// Validation. 날짜 포맷 (YYYYMMDD)
		if (!(startDate && endDate) || !(moment(startDate, 'YYYYMMDD', true).isValid() && moment(endDate, 'YYYYMMDD', true).isValid())) {
			throw new BusinessError({ message: `[monitoring.controller :: monitorRevenue] startDate or endDate format is invalid ( startDate= ${startDate}, endDate= ${endDate} )` });
		}

		// 수익률 모니터링 알림 메일 발송
		await monitoringRevenueService.monitorRevenue(startDate, endDate);

		ctx.body = { code: 200, message: `광고공급자/매체 수익률 모니터링 알림 메일 발송 완료` };
	} catch (e) {
		logger.error(`[monitoring.controller :: monitorRevenue] Error :: \n ${e.stack}\n\n`, e);

		ctx.body = { code: 500, message: `광고공급자/매체 수익률 모니터링 알림 메일 발송 실패` };
	}
}
