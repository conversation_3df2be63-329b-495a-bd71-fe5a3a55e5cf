'use strict';

import _ from 'lodash';
import moment from 'moment';

import SBE from '../common/error-code';
import { BusinessError } from '../common/error';

import extendedLogger from '../utils/logger-ext.util';

import * as performanceReportService from '../services/report/performance/performance-report.service';

const logger = extendedLogger('performance'); // 확장 로거


/**
 * 맞춤 & 매체 성과 리포트 NUBES 업로드 ( HDFS -> NUBES )
 *
 * @RequestMapping(value='/batch/performance/upload')
 */
module.exports.upload = async (ctx) => {
	const txId = moment().unix();

	logger.debug(`[performance-report.controller :: upload] /batch/performance/upload 호출됨 ( txId= ${txId} )`);

	let body;

	const reportId = ctx.request.query.reportId;

	if (_.isEmpty(reportId)) {
		throw new BusinessError(SBE.Common.MissingRequiredParam, { field: 'reportId' });
	}

	try {
		// 업로드 처리중/성공/실패 상태에 해당 하는지 체크
		const result = await performanceReportService.checkTaskStateUpload(txId, reportId);

		if (!_.isEmpty(result)) {
			logger.debug(`[performance-report.controller :: upload] 맞춤 & 매체 성과 리포트 업로드 ${result.state} ( txId= ${txId}, reportId= ${reportId}, state= ${result.state}, taskState= ${result.taskState} )`);

			body = { code: 200, message: `맞춤 & 매체 성과 리포트 업로드 ${result.state} ( txId= ${txId}, reportId= ${reportId} )` };
		} else {
			await performanceReportService.upload(txId, reportId);

			logger.debug(`[performance-report.controller :: upload] 처리 완료 ( txId= ${txId}, reportId= ${reportId} )`);

			body = { code: 200, message: `맞춤 & 매체 성과 리포트 업로드 완료 ( txId= ${txId}, reportId= ${reportId} )` };
		}
	} catch (e) {
		logger.error(`[performance-report.controller :: upload] ERROR ( txId= ${txId} ) \n ${e.stack} \n`);

		body = { code: 500, message: `맞춤 & 매체 성과 리포트 업로드 실패 ( txId= ${txId}, reportId= ${reportId} ) ${e.stack}` };
	} finally {
		ctx.body = body;
	}
};


/**
 * 매체 성과 리포트 HDFS & NUBES 삭제
 *     - 1주 전 한달치 리포트 HDFS & NUBES 일괄 삭제
 *     - ex> 12월 8일에 실행 시, 10월 30일 ~ 11월 30일 일괄 삭제 처리됨
 *     - ex> 12월 15일에 실행 시, 11월 7일 ~ 12월 7일 일괄 삭제 처리됨
 *     - ex> 12월 22일에 실행 시, 11월 14일 ~ 12월 14일 일괄 삭제 처리됨
 *
 * @RequestMapping(value='/batch/performance/delete')
 */
module.exports.delete = async ctx => {
	const txId = moment().unix();

	logger.debug(`[performance-report.controller :: delete] /batch/performance/delete 호출됨 ( txId= ${txId} )`);

	let body;

	try {
		await performanceReportService.delete(txId);

		body = { code: 200, message: `매체 성과 리포트 HDFS & NUBES 삭제 완료 ( txId= ${txId} )` };
	} catch (e) {
		logger.error(`[performance-report.controller :: delete] ERROR ( txId= ${txId} ) \n ${e.stack} \n`);

		body = { code: 500, message: `매체 성과 리포트 HDFS & NUBES 삭제 에러 ( txId= ${txId} ) ${e.stack}` };
	} finally {
		ctx.body = body;
	}
};
