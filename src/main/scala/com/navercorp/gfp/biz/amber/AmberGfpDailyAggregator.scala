package com.navercorp.gfp.biz.amber

import java.util.Date
import scala.collection.JavaConverters._
import scala.concurrent.Future
import scala.util.Try
import scala.util.control.Breaks.{break, breakable}

import org.apache.spark.sql._
import org.apache.spark.sql.functions.{array, expr, lit}
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTimeZone, Period}

import com.navercorp.gfp.core.BaseEnv.{AMBER_GFP_PATH, GOLD_ADPROVIDER_PATH}
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory, SummaryHistoryDetail}
import com.navercorp.gfp.core.{BaseAggregator, BaseSchema, BizAggregator}
import com.navercorp.gfp.exception.BusinessException
import com.navercorp.gfp.meta.adproviderinfo.{AdProviderInfo, AdProviderInfoDao}
import com.navercorp.gfp.meta.summarytargetcountry.SummaryTargetCountryDao
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil, ObjectIdUtil}

/*
	- 골드 인덱스 기반의 AMBER(중간 집계 데이터) 생성
		- input gold : /user/gfp-data/gold/adprovider
		- output amber : /user/gfp-data/amber/gfp/yyyy/mm/dd/adProviderId=xxxx/publisherId=xxx
	- AP 리포트 연동을 하는 매체들만 대상으로 한다.
		- 스케쥴 생성 조건 : reportStat=COMPLETE/NA, reportApiStatus=ON

	publisher_ids=[N], adProvider_ids=[N]

	spark 설정
		--num-executors 8
		--executor-cores 3
		--executor-memory 500m
		--conf spark.sql.shuffle.partitions=5
		--conf spark.executor.memoryOverhead=500m
*/
object AmberGfpDailyAggregator extends BaseAggregator {
	val LOG_PREFIX = ".......... [AMBER-GFP]"

	private var sparkAppId: String = ""

	private val adProviderInfoDao = new AdProviderInfoDao

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		// 처리 대상 일자
		val endDate = args(0)

		val rkUse = args(1).toInt
		val adProviderIds: Seq[String] = args(2).split(",").distinct
		val publisherIds: Seq[String] = args(3).split(",").distinct

		// 이력쌓기 상세정보
		val detail = summaryHistoryId match {
			case Some(_) => None
			case None => Option(SummaryHistoryDetail(
				publisherIds = Option(publisherIds), adProviderIds = Option(adProviderIds), rkUse = Option(rkUse),
			))
		}

		try {
			// args 밸리데이션 체크
			// require 는 runtime 시점에 Exception 을 던진다.
			// Exception 없이, 인스턴스 생성 시점에 validation 체크를 하고 싶으면 apply 를 구현하도록 한다.
			//  - https://gist.github.com/jkpl/4932e8730c1810261381851b13dfd29d
			//  - https://www.47deg.com/blog/smart-constructors-in-scala/#smart-constructors-for-case-classes-0
			//  - https://stackoverflow.com/questions/5982484/scala-lift-check-if-date-is-correctly-formatted
			require(ObjectIdUtil.isValidObjectIds(publisherIds), s"publisherIds($publisherIds) is invalid ObjectIds")
			require(ObjectIdUtil.isValidObjectIds(adProviderIds), s"adProviderIds($adProviderIds) is invalid ObjectIds")
			require(Try(DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(endDate)).isSuccess, s"endDate($endDate) is invalid format (must be yyyyMMdd)")
			require(rkUse == 0 || rkUse == 1, s"rkUse($rkUse) is invalid")


			// 이력쌓기 - 진행중 (IN_PROGRESS)
			sparkAppId = spark.sparkContext.applicationId
			val inProgressHist = SummaryHistory(
				datetime = Option(endDate),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(Spark(
					sparkAppId = Option(sparkAppId),
					sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
					sparkStartedAt = Option(new Date)
				)),
				detail = detail
			)
			this.upsertSummaryHistory(inProgressHist)

			// adProviderIds 에 해당하는 timezone 정보 가져오기
			// 	- adProviderIds 는 동일한 timezone 을 갖는 ap 들 목록임
			val timezone = adProviderDao.getAdProviderCurrencyAndTimezone(adProviderIds.head) match {
				case Some(ap) if ap.timezone.getOrElse("") != "" => ap.timezone.get
				case _ => throw BusinessException(s"adProviderIds= $adProviderIds does not exist in DB")
			}

			logger.debug(s"$LOG_PREFIX timezone= $timezone")
			logger.debug(s"$LOG_PREFIX publisherIds= $publisherIds")

			// 집계 인스턴스 생성
			val aggregator = new AmberGfpDailyAggregator(endDate, rkUse)

			aggregator.init()

			// breakable 을 loop 안에 넣으면 break 가 continue 로 동작하고, loop 밖에 넣으면 break 로 동작한다.
			// 	- https://m.blog.naver.com/jiwon2772/221317139127
			publisherIds.foreach { publisherId =>
				breakable {
					logger.debug(s"$LOG_PREFIX 매체별 처리 시작 ( publisherId= $publisherId )")

					// AdProviderInfo 정보 가져오기
					val adProviderInfos = adProviderInfoDao.getAdProviderInfos(publisherId, adProviderIds).into(new java.util.ArrayList[AdProviderInfo]()).asScala
					val validAdProviderIds = adProviderInfos.map(_.adProvider_id.toString)

					// 처리할 adProviderIds 가 없는 경우, break
					if (validAdProviderIds.length < 1) {
						logger.warn(s"$LOG_PREFIX 처리할 adProviderIds 가 없음 ( publisherId= $publisherId )")
						break
					}

					// 타임존에 해당하는 한국시간대 경로 가져오기
					val pathList = getPathList(publisherId, endDate, timezone)

					// 골드 인덱스 로딩
					val goldDf = loadParquetLog(pathList) match {
						case Some(df) => df
						case _ =>
							logger.warn(s"$LOG_PREFIX publisherId= $publisherId 에 해당하는 HDFS 로그 경로가 없음")
							break
					}

					val aggregatedDf = aggregator.aggregate(Option(goldDf, validAdProviderIds))

					// HDFS - 파케이 파일 쓰기
					aggregator.write(aggregatedDf, Option(publisherId, validAdProviderIds))
				}
			}

			// 이력쌓기 - 완료 (COMPLETE)
			val completeHist = SummaryHistory(
				spark = Option(Spark(
					sparkAppState = Option(SparkAppState.COMPLETE.toString),
					sparkEndedAt = Option(new Date)
				))
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			// Throwable 클래스는 예외 처리를 할 수 있는 최상위 클래스이다. Exception 과 Error 는 Throwable 를 상속 받는다. ( https://sjh836.tistory.com/122 )
			case t: Throwable =>
				// 이력쌓기 - 실패 (FAILURE)
				val failureHist = SummaryHistory(
					spark = Option(Spark(
						sparkAppState = Option(SparkAppState.FAILURE.toString),
						sparkAppError = Option(t.getMessage),
						sparkEndedAt = Option(new Date)
					)),
					detail = detail
				)
				this.upsertSummaryHistory(failureHist)

				t match {
					case BusinessException(_, _) =>
						logger.warn(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
					case _ =>
						logger.error(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
						throw t
				}
		} finally {
			NeloUtil.waitFor()
		}
	}

	/**
	 * 타임존 적용된 골드 인덱스 경로 구하기
	 *    - 골드 인덱스는 한국시간으로 되어있음
	 *    - AP 데이터는 타임존이 적용되어야 함
	 *
	 * @param publisherId
	 * @param targetDate
	 * @param timezone
	 * @return pathList
	 */
	def getPathList(publisherId: String, targetDate: String, timezone: String): List[String] = {
		// 타임존 적용된 날짜의 시간대별 경로 추출 (골드 인덱스는 한국시간으로 되어있어 타임존 변환하여 경로를 가져와야 한다)
		val dtRange = getDateTimeRange(targetDate, Period.hours(1), DateTimeZone.forID(timezone))
		val pathList = dtRange.map { dt =>
			val path: String = s"$GOLD_ADPROVIDER_PATH/${dt.toString("yyyy/MM/dd/HH")}/publisherId=$publisherId"

			// hdfs 에 존재하는 경로만 따로 추출
			if (HdfsUtil.exists(hdfs, path)) path else ""
		}.filter(_.nonEmpty)

		// logger.debug(s"pathList $pathList")

		pathList
	}

	/**
	 * 로그 로딩
	 *
	 * @param pathList
	 * @return Dataset[Row]
	 */
	override def loadParquetLog(pathList: Seq[String]): Option[Dataset[Row]] = {
		logger.debug(s"$LOG_PREFIX profile=$PROFILE")

		// HDFS 경로가 하나도 없는 경우, None
		if (pathList.isEmpty) None else super.loadParquetLog(pathList, BaseSchema.GOLD_SCHEMA)
	}
}

/**
 * 데이터 삭제 / 집계 / 추가 등의 작업을 한다.
 *
 * spark-submit에서 호출할 때나 test case에서 호출할 때 사용함
 * 환경에 따라 달라지는 값은 생성자 파라미터로 받아서 처리함.
 *
 * @param date
 * @param rkUse
 */
class AmberGfpDailyAggregator(date: String, rkUse: Int)(implicit spark: SparkSession) extends BizAggregator with AmberHelper {
	val LOG_PREFIX = ".......... [AMBER-GFP]"

	private val datetime = DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(date)

	private val summaryTargetCountryDao = new SummaryTargetCountryDao()

	// Option[Seq[String]] 보다 Seq[Option[String]] 를 사용하는게 좋다.
	//  - https://stackoverflow.com/questions/32371382/scala-optionseqstring-vs-seqoptionstring/32371849#32371849
	private var countryList: Seq[String] = Seq()

	// country 는 aggregate 시 쓰이는데, countryList 는 init 할 때 생성 된다.
	// country 를 lazy 없이 쓰면, countryList 가 empty 이기 때문에, country 구문이 원하는 대로 생성 되지 않는다.
	// 물론 country 를 var 로 선언 하고, init 시점에 수정할 수는 있으나, 공통 쿼리 구문을 상단에 모아두고 싶어서 lazy 를 사용하였다.
	private lazy val country = s"CASE WHEN country IN ( ${countryList.mkString("'", "','", "'")} ) THEN country ELSE 'OTHERS' END AS country"

	private val baseList = Seq("publisherId", "adProviderId")
	private val baseAggList = Seq(
		expr("NVL(SUM(impressions), 0)").as("gfpImpressions"),
		expr("NVL(SUM(viewableImpressions), 0)").as("gfpViewableImpressions"),
		expr("NVL(SUM(clicks), 0)").as("gfpClicks"),
		expr("NVL(SUM(estimatedImpressions), 0)").as("gfpEstimatedImpressions"),
	)

	import spark.implicits._
	// https://www.scala-lang.org/api/2.13.3/scala/collection/JavaConverters$.html

	/**
	 * 집계에 필요한 정보 조회하기
	 *    - SummaryTargetCountries 에서 countryCd 정보 조회
	 */
	override def init(): Unit = {
		// SummaryTargetCountries 에서 countryCd 정보 조회하기
		val countries = summaryTargetCountryDao.getCountries

		countryList = countries.map(_.countryCd).into(new java.util.ArrayList[String]()).asScala

		logger.debug(s"$LOG_PREFIX countries= ${countryList.toString()}")
	}

	/**
	 * 데이터 집계
	 *
	 * @return finalDf Dataset[Row]
	 */
	def aggregate(aggParam: Option[A] = None): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX country= $country ")

		val (logDf, adProviderIds) = aggParam.get

		// rk 인 경우, placeKey 가 매우 많기 때문에 dimensions 에서 제외함
		val adProviderPlaceKey = Option(rkUse) match {
			case Some(0) => "placeKey AS adProviderPlaceKey" // nonRK
			case Some(1) => "CAST(null AS string) AS adProviderPlaceKey" // rk
			case _ => throw BusinessException("rkUse is missing")
		}

		val selectList = baseList ++ Seq(country, adProviderPlaceKey, "adUnitId", "adProviderPlaceId") ++ Seq("impressions", "viewableImpressions", "clicks", "estimatedImpressions")
		val groupByList = baseList ++ Seq("country", "adProviderPlaceKey", "adUnitId", "adProviderPlaceId")

		// 골드 인덱스에서 가져온 데이터
		val finalDf = logDf
			.filter($"adProviderId".isin(adProviderIds: _*))
			.selectExpr(selectList: _*)
			.groupBy(groupByList.head, groupByList.tail: _*)
			.agg(baseAggList.head, baseAggList.tail: _*)
			// 지표가 모두 0인 경우 제외 처리
			.filter("NOT(gfpImpressions == 0 AND gfpViewableImpressions == 0 AND gfpClicks == 0 AND gfpEstimatedImpressions == 0)")
			// 요약 대상 날짜
			.withColumn("date", lit(date))
			// type=GFP 추가
			.withColumn("type", lit("GFP"))
			// adProviderPlaceIds, adUnitIds 단건 배열 처리
			.withColumn("adUnitIds", array($"adUnitId"))
			.withColumn("adProviderPlaceIds", array($"adProviderPlaceId"))
			.drop("adUnitId", "adProviderPlaceId")

		// logger.debug("finalDf schema >>>>>>")
		// finalDf.printSchema()
		// finalDf.show(30, false)
		// finalDf.explain()

		finalDf
	}

	type A = (Dataset[Row], Seq[String])

	/**
	 * 데이터 추가
	 */
	def write(df: DataFrame, writeParam: Option[W] = None): Unit = {
		val (publisherId, adProviderIds) = writeParam.get

		logger.debug(s"$LOG_PREFIX date= $date, adProviderIds= $adProviderIds")

		adProviderIds.foreach { adProviderId =>
			val path = s"$AMBER_GFP_PATH/${datetime.toString("yyyy/MM/dd")}/adProviderId=$adProviderId/publisherId=$publisherId"
			val filteredDf = df.filter(s"adProviderId == '$adProviderId'")

			val COALESCE_CNT = getNumPartitions(filteredDf)

			this.writeToHdfs(filteredDf, path, SaveMode.Overwrite.name(), COALESCE_CNT)
		}
	}

	type W = (String, Seq[String])

	def getFuturesForDelete(param: Option[T]): List[Future[Option[Boolean]]] = {
		null
	}
}
