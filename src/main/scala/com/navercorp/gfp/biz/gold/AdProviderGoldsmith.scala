package com.navercorp.gfp.biz.gold

import java.util.Date
import java.util.concurrent.{ExecutorService, ForkJoinPool}
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext, Future}

import com.mongodb.spark.sql.toMongoDataFrameReaderFunctions
import org.apache.hadoop.fs.FileSystem
import org.apache.spark.sql._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._

import com.navercorp.gfp.biz.gold.AdProviderGoldsmith.LOG_PREFIX
import com.navercorp.gfp.biz.gold.GoldsmithIndexType.GoldsmithIndexType
import com.navercorp.gfp.biz.silver.SilverLogType
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory}
import com.navercorp.gfp.core.{AdProviderAggregator, BaseEnv, <PERSON>iz<PERSON>ggregator, DeleteFutureHelper}
import com.navercorp.gfp.util.{AggregatorUtil, HdfsUtil, Nelo<PERSON>til, TimeUtil}

object AdProviderGoldsmith extends Goldsmith {
	val LOG_PREFIX = ".......... [GOLD-AP]"

	override def indexType: GoldsmithIndexType = GoldsmithIndexType.adprovider

	val aggregator = new AdProviderGoldsmith()

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		// 대상 일시 설정
		val ymdh = args(0)

		try {
			logger.debug(s"$LOG_PREFIX target ymdh: $ymdh")

			// 이력쌓기 - IN_PROGRESS
			val sparkDocForInProgress = Spark(
				sparkAppId = Option(spark.sparkContext.applicationId),
				sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
				sparkStartedAt = Option(new Date)
			)
			val inProgressHist = SummaryHistory(
				datetime = Option(ymdh),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(sparkDocForInProgress)
			)
			this.upsertSummaryHistory(inProgressHist)

			aggregator.init()

			makeGoldIndex(ymdh)

			createGoldSuccessFile(ymdh, indexType)

			// 이력쌓기 - COMPLETE
			val sparkDocForComplete = Spark(
				sparkAppState = Option(SparkAppState.COMPLETE.toString),
				sparkEndedAt = Option(new Date)
			)
			val completeHist = SummaryHistory(
				spark = Option(sparkDocForComplete)
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			case t: Throwable =>
				// 이력쌓기 - FAILURE
				val sparkDocForFailure = Spark(
					sparkAppState = Option(SparkAppState.FAILURE.toString),
					sparkAppError = Option(t.getMessage),
					sparkEndedAt = Option(new Date)
				)
				val failureHist = SummaryHistory(
					spark = Option(sparkDocForFailure)
				)
				this.upsertSummaryHistory(failureHist)
				logger.error(s"$LOG_PREFIX 광고공급자 골드 인덱스 생성 실패. sparkAppId=${spark.sparkContext.applicationId} ymdh=$ymdh summaryHistoryId=${summaryHistoryId.get} ${t.getMessage}")

				throw t
		} finally {
			NeloUtil.waitFor()
		}
	}

	def makeGoldIndex(ymdh: String): Unit = {
		// 시간대 디렉토리 삭제
		deleteYmdhPath(ymdh, indexType)

		logger.debug(s"$LOG_PREFIX $ymdh 생성 시작")

		val threadNum = getThreadNum() // 동시 처리 개수
		val forkJoinPool: ExecutorService = new ForkJoinPool(threadNum)
		implicit val ec: ExecutionContext = ExecutionContext.fromExecutorService(forkJoinPool)
		var futures = Vector[Future[Option[Boolean]]]()

		val allPubIds = publisherDao.getPublisherIds().toVector
		// 5f3b9bcd5f281f001db91d9d (네이버알림)
		// 5d9ef168e933ef00170334a1 (그룹앤CIC)
		// 5b8f669428b373001fe56ea8 (네이버서비스)
		//		val allPubIds = Vector("5b8f669428b373001fe56ea8")

		for (pubId <- allPubIds) {
			// 로그 로딩
			val sPath = getSilverPath(ymdh, pubId, SilverLogType.ErSspServer)
			val cPath = getSilverPath(ymdh, pubId, SilverLogType.ErSspClient)

			if (sPath.isEmpty && cPath.isEmpty) {
				logger.warn(s"$LOG_PREFIX 서버 & 클라이언트 실버 로그가 존재하지 않음. sparkAppId=${spark.sparkContext.applicationId} ymdh=$ymdh pubId=$pubId sPaths=$sPath cPaths=$cPath")
			} else { // 둘 중 어느 하나 로그라도 있으면 진행
				val future = Future {
					try {
						process(ymdh, pubId, sPath, cPath)

						// 성공 시, true 리턴
						Option(true)
					} catch {
						case t: Throwable =>
							logger.error(s"$LOG_PREFIX 광고공급자 골드 인덱스 생성 실패. sparkAppId=${spark.sparkContext.applicationId} ymdh=$ymdh pubId=$pubId ${t.getMessage}", t)
							Option(false) // 실패 시, false 리턴
					}
				}
				futures = futures :+ future
			}
		}

		if (futures.nonEmpty) {
			// 퍼블리셔별 결과 모으기
			val reduced = Future.reduceLeft(futures) { case (accu, value) =>
				Option(accu.getOrElse(false) && value.getOrElse(false))
			}

			// 모든 future 가 끝날 때까지 기다렸다가 결과 받기
			val result = Await.result(reduced, Duration.Inf)

			// 모두 성공적으로 끝났는지 확인
			result match {
				case Some(false) =>
					throw new Exception(s"$ymdh 생성 실패")
				case _ =>
					logger.debug(s"$LOG_PREFIX $ymdh 생성 완료")
			}
		} else {
			throw new Exception(s"$ymdh 생성 대상 없음. futures is empty")
		}
	}

	def process(ymdh: String, pubId: String, sPath: Option[String], cPath: Option[String]): Unit = {
		// 로그 로딩
		val serverDf = if (sPath.isEmpty) None else Option(loadSilverLog(Seq(sPath.get)))
		val clientDf = if (cPath.isEmpty) None else Option(loadSilverLog(Seq(cPath.get)))

		// 집계
		val aggDf = aggregator.aggregate(Option(AggParam(ymdh, serverDf, clientDf)))

		// 경로 설정
		val splitYmdh = TimeUtil.getSplitYmdh(ymdh)
		val outputPath = s"${BaseEnv.GOLD_ROOT}/$indexType/${splitYmdh.yyyy}/${splitYmdh.mm}/${splitYmdh.dd}/${splitYmdh.hh}/publisherId=$pubId"

		// 기존 데이터 삭제
		aggregator.delete(aggregator.getFuturesForDelete(Option(hdfs, outputPath)))

		// HDFS에 저장
		aggregator.write(aggDf, Option(outputPath))
	}
}

class AdProviderGoldsmith()(implicit spark: SparkSession) extends BizAggregator with DeleteFutureHelper with AdProviderAggregator {

	import spark.implicits._

	val SHUFFLE_PARTITION = spark.conf.get("spark.sql.shuffle.partitions").toInt
	val dimensions = Vector(
		"publisherId", // 매체
		"country", // 국가
		"area1", // 광역시,도
		"area2", // 시,군,구
		"area3", // 읍,면,동
		"gender", // 나이
		"age", // 성별
		"os", // OS
		"osVer", // OS 버전
		"browser", // 브라우저
		"browserVer", // 브라우저 버전
		"device", // 디바이스
		"serviceId", // 서비스
		"adUnitId", // 광고유닛
		"adProviderId", // 광고공급자
		"adProviderPlaceId", // 광고공급단위
		"placeKey", // 광고공급단위키
		"dealId", // 거래
		"dealYn", // 거래여부
		"biddingGroupId", // 비딩그룹
		"connectionType", // 광고공급유형
		"rank", // 호출순위
		"requestCreativeType", // 호출 상품유형
		"multiSizeAdRequestYn", // 멀티사이즈 호출 여부
		"biddingGroupExpectedCpm" // 비딩그룹 기대 CPM (C2S만 해당)
		//        "responseCreativeType",
		//        "responseSize",
	)
	type A = AggParam

	val metrics = Vector(
		"filledRequestsOfAp", // 낙찰수(AP 단위의 선출수)
		"impressions", // 노촐수
		"viewableImpressions", // 유효노출수
		"clicks", // 클릭수
		"completions", // 보상수
		"adMute", // 광고끈수
		"estimatedImpressions", // 추정 과금 노출수

		"adProviderRequests", // 광고공급자 호출수
		"adProviderResponses", // 광고공급자 응답수
		"noAdsResponses", // 광고없음 응답수
		"timeout", // 타임아웃수
		"invalidConditionsResponses", // 무효조건응답수
		"invalidBidpriceResponses", // 무효CPM응답수

		"krwBidPriceSum", // AP 추정수익 기준에 따른 비딩가 합(KRW)
		"usdBidPriceSum", // AP 추정수익 기준에 따른 비딩가 합(USD)

		"bidPriceSumBasedOnFilledRequests", // 선출 기준 비딩가 합. S2S만 해당
		"bidPriceSumBasedOnImpressions", // 노출 기준 비딩가 합. S2S만 해당
		"bidPriceSumBasedOnAdProviderResponses", // 응답 기준 비딩가 합. S2S만 해당
	)

	/**
	 * 광고공급자의 추정수익집계기준(estimatedReportType) 로드
	 *
	 * @return
	 */
	def loadAdProviders(): Dataset[Row] = {
		val oidStructType = StructType(List(StructField("oid", StringType, true)))
		val schema = StructType(List(
			StructField("_id", oidStructType, true),
			StructField("report", StructType(List(
				StructField("estimatedReportType", MapType(StringType, StringType), true),
			)))
		))

		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncAdProviders")
		val df = spark.read.schema(schema).mongo(readConfig)
			.select(
				$"_id.oid".as("adProviderId"),
				$"report.estimatedReportType"
			)

		applyDefaultEstRptType(df)
	}

	/**
	 * 집계식
	 *
	 * @return
	 */
	def getAggExprs(): Vector[Column] = {
		val estimatedImpressions =
			"""SUM(CASE
				WHEN estimatedReportType == 'FILL' AND eventId == 1 THEN 1
				WHEN estimatedReportType == 'IMP'  AND eventId == 11 AND isValid == '1' THEN 1
				WHEN estimatedReportType == 'VIEW' AND eventId == 12 AND isValid == '1' THEN 1
				ELSE 0 END)"""

		val bidPriceSum =
			"""SUM(CASE
				WHEN estimatedReportType == 'FILL' AND eventId == 1 THEN bidPrice
				WHEN estimatedReportType == 'IMP'  AND eventId == 11 THEN bidPrice
				WHEN estimatedReportType == 'VIEW' AND eventId == 12 THEN bidPrice
				ELSE 0 END)"""
		val krwBidPriceSum = bidPriceSum.replaceAll("bidPrice", "krwBidPrice")
		val usdBidPriceSum = bidPriceSum.replaceAll("bidPrice", "usdBidPrice")

		Vector(
			expr("SUM(CASE WHEN eventId == 1 THEN 1 ELSE 0 END)").as("filledRequestsOfAp"), // 낙찰수(AP 단위의 선출수)
			expr("SUM(CASE WHEN (eventId == 11 AND isValid == '1') THEN 1 ELSE 0 END)").as("impressions"), // 노출수
			expr("SUM(CASE WHEN (eventId == 12 AND isValid == '1') THEN 1 ELSE 0 END)").as("viewableImpressions"), // 유효노출수
			expr("SUM(CASE WHEN (eventId == 3 AND isValid == '1') THEN 1 ELSE 0 END)").as("clicks"), // 클릭수
			expr("SUM(CASE WHEN (eventId == 4) THEN 1 ELSE 0 END)").as("completions"), // 보상
			expr("SUM(CASE WHEN (eventId == 5) THEN 1 ELSE 0 END)").as("adMute"), // 광고끈수

			// 광고공급자 호출수
			expr("SUM(CASE WHEN (eventId IS NULL AND connectionType == 'S2S') OR (eventId ==  1 AND connectionType == 'C2S') OR (eventId == 91 AND connectionType == 'C2S') THEN 1 ELSE 0 END)").as("adProviderRequests"),
			// 광고공급자 응답수
			expr("SUM(CASE WHEN (eventId IS NULL AND connectionType == 'S2S' AND stat == 1) OR (eventId == 1 AND connectionType == 'C2S') THEN 1 ELSE 0 END)").as("adProviderResponses"),

			// 광고없음 응답수
			expr("SUM(CASE WHEN (eventId IS NULL AND connectionType == 'S2S' AND stat == 2) OR (eventId == 91 AND connectionType == 'C2S' AND (stat == 2 OR stat == null)) THEN 1 ELSE 0 END)").as("noAdsResponses"),
			// 타임아웃
			expr("SUM(CASE WHEN (eventId IS NULL AND connectionType == 'S2S' AND stat == 3) OR (eventId == 91 AND connectionType == 'C2S' AND stat == 3) THEN 1 ELSE 0 END)").as("timeout"),
			// 무효 조건 응답수
			expr("SUM(CASE WHEN (eventId IS NULL AND connectionType == 'S2S' AND stat == 4) OR (eventId == 91 AND connectionType == 'C2S' AND stat == 4 ) THEN 1 ELSE 0 END)").as("invalidConditionsResponses"),
			// 무효 CPM 응답수
			expr("SUM(CASE WHEN (eventId IS NULL AND connectionType =='S2S' AND stat == 5) OR (eventId == 91 AND connectionType =='C2S' AND stat == 5) THEN 1 ELSE 0 END)").as("invalidBidpriceResponses"),

			// 추정 과금 노출수 (responseCreativeType 에 따른 estimatedReportType 별 노출수)
			expr(estimatedImpressions).as("estimatedImpressions"),
			// 비딩가 합(KRW) - 추정수익
			expr(krwBidPriceSum).as("krwBidPriceSum"),
			// 비딩가 합(USD) - 추정수익
			expr(usdBidPriceSum).as("usdBidPriceSum"),

			// 선출 기준 비딩가 합(USD)
			expr("SUM(CASE WHEN eventId == 1  AND connectionType == 'S2S' THEN bidPrice ELSE 0 END)").as("bidPriceSumBasedOnFilledRequests"),
			// 노출 기준 비딩가 합(USD)
			expr("SUM(CASE WHEN eventId == 11 AND connectionType == 'S2S' AND isValid == '1' THEN bidPrice ELSE 0 END)").as("bidPriceSumBasedOnImpressions"),
			// 광고공급자 응답 기준 비딩가 합(USD)
			expr("SUM(CASE WHEN eventId IS NULL AND connectionType == 'S2S' AND stat == 1 THEN bidPrice ELSE 0 END)").as("bidPriceSumBasedOnAdProviderResponses")
		)
	}

	/**
	 * 파생 컬럼 추가
	 *
	 * @param df
	 * @return
	 */
	def addDerivedColumns(df: Dataset[Row]): Dataset[Row] = {
		df
			.withColumn("dealYn", when($"dealId".isNull || $"dealId" === "", lit("N")).otherwise(lit("Y")))
			.withColumn("multiSizeAdRequestYn", when(size($"requestCreativeTypes") > 1, lit("Y")).otherwise(lit("N")))
			.withColumn("biddingGroupExpectedCpm", when($"connectionType" === "C2S", $"bidPrice").otherwise(lit("-")))
			.drop("requestCreativeTypes")
	}

	def addEstimatedReportType(df: Dataset[Row], apDf: Dataset[Row]): Dataset[Row] = {
		df.join(apDf,
				(df.col("adProviderId") === apDf.col("adProviderId")) &&
					(df.col("responseCreativeType") === apDf.col("creativeType")), "left")
			.select(
				df.col("*"),
				apDf.col("creativeType"),
				apDf.col("estimatedReportType")
			)
	}

	def aggregate(aggParamOption: Option[A]): Dataset[Row] = {
		val aggParam = aggParamOption.get
		val (ymdh, serverDfOpt, clientDfOpt) = (aggParam.ymdh, aggParam.serverDf, aggParam.clientDf)

		val apDf = broadcast(loadAdProviders())
		val feeRateDf = broadcast(loadGfpFeeRate(ymdh.substring(0, 8)))

		val aggExprs = getAggExprs()
		var aggDf: Dataset[Row] = null

		if (serverDfOpt.isDefined && clientDfOpt.isDefined) { // 서버 & 클라 모두 존재
			// 광고공급자 성과 지표 집계 (S2S)
			val sDf = prepareServerDf(serverDfOpt.get, apDf)
			val s2sStats = getAdProviderStats(sDf, aggExprs)

			// 광고공급자 성과 지표 집계 (C2S)
			val cDf = prepareClientDf(clientDfOpt.get, apDf)
			val c2sStats = getAdProviderStats(cDf, aggExprs)

			// 데이터 볼륨을 줄인 stats들로 union
			val apStats = getUnionedAdProviderStats(s2sStats, c2sStats)

			// AdProviderPlaces.creativeType=COMBINED 인 경우
			// 광고공급자 호출수, 광고공급자 응답수 추가 처리
			val apStats2 = applyReqRes(apStats)

			// GFP 수수료율 적용
			val apStats3 = applyGfpFeeRate(apStats2, feeRateDf)

			// 선출수 집계 및 조인
			val filledRequests = getFilledRequests(cDf)
			aggDf = addFilledRequestsWithSalt(apStats3, filledRequests)

		} else if (serverDfOpt.isDefined && clientDfOpt.isEmpty) { // 서버 로그만 존재
			// 광고공급자 성과 지표 집계 (S2S)
			val sDf = prepareServerDf(serverDfOpt.get, apDf)
			val s2sStats = getAdProviderStats(sDf, aggExprs)

			// AdProviderPlaces.creativeType=COMBINED 인 경우
			// 광고공급자 호출수, 광고공급자 응답수 추가 처리
			val apStats = applyReqRes(s2sStats)

			// GFP 수수료율 적용
			val apStats2 = applyGfpFeeRate(apStats, feeRateDf)

			// 클라 로그가 없으므로 선출수 = 0 으로 추가
			aggDf = apStats2.withColumn("filledRequestsOfAu", lit(0))

		} else if (serverDfOpt.isEmpty && clientDfOpt.isDefined) { // 클라 로그만 존재
			// 광고공급자 성과 지표 집계 (C2S)
			val cDf = prepareClientDf(clientDfOpt.get, apDf)
			val c2sStats = getAdProviderStats(cDf, aggExprs)

			// AdProviderPlaces.creativeType=COMBINED 인 경우
			// 광고공급자 호출수, 광고공급자 응답수 추가 처리
			val apStats = applyReqRes(c2sStats)

			// GFP 수수료율 적용
			val apStats2 = applyGfpFeeRate(apStats, feeRateDf)

			// 선출수 집계
			val filledRequests = getFilledRequests(cDf)
			aggDf = addFilledRequestsWithSalt(apStats2, filledRequests)

		} else {
			val msg = s"ymdh=$ymdh 서버 & 클라 데이터프레임 모두 존재하지 않음"
			logger.error(s"$LOG_PREFIX $msg")
			throw new Exception(msg)
		}

		filterAllZeroMetrics(aggDf, metrics)
			.withColumn("ymdh", lit(ymdh))
			.withColumn("filledRequestsOfAu", $"filledRequestsOfAu".cast(LongType))
	}

	/**
	 * - 서버 데이터셋에서 필요한 필드만 추출
	 * - 서버 데이터셋에서는 isValid, eventId를 null 처리 함
	 * - 추정수익집계기준(estimatedReportType) 필드 추가
	 *
	 * @param df
	 * @return
	 */
	def prepareServerDf(df: Dataset[Row], apDf: Dataset[Row]): Dataset[Row] = {
		val df1 = filterTest(df)
			.withColumn("res", explode($"responses"))
			.filter($"res.adProviderId".isNotNull && $"res.adProviderId" =!= "")
			.selectExpr(
				"requestId",
				"null AS isValid",
				"null AS eventId",
				"publisherId",
				"country",
				"area1",
				"area2",
				"area3",
				"gender",
				"age",
				"os",
				"osVer",
				"browser",
				"browserVer",
				"device",
				"serviceId",
				"adUnitId",
				"res.adProviderId",
				"res.adProviderPlaceId",
				"res.placeKey",
				"res.dealId",
				"res.biddingGroupId",
				"res.connectionType",
				"res.rank",
				"res.requestCreativeTypes",
				"NVL(res.requestCreativeTypes[0], '-') AS requestCreativeType",
				"res.requestSizes",
				"res.responseCreativeType",
				"res.responseSize",
				"res.bidPrice",
				"res.bidPriceInKRW AS krwBidPrice",
				"res.bidPriceInUSD AS usdBidPrice",
				"res.stat"
			)

		val df2 = addEstimatedReportType(df1, apDf)

		val df3 = addDerivedColumns(df2)

		df3
	}

	/**
	 * - 클라이언트 데이터셋에서 필요한 필드만 추출 후
	 * - 추정수익집계기준(estimatedReportType) 필드 추가
	 * - empty 광고도 포함(adProviderId == null or "" 인것도 포함)
	 *
	 * @param df
	 * @return
	 */
	def prepareClientDf(df: Dataset[Row], apDf: Dataset[Row]): Dataset[Row] = {
		val df1 = filterTest(df)
			.filter($"adProviderId".isNotNull && $"adProviderId" =!= "")
			.selectExpr(
				"requestId",
				AggregatorUtil.selectExprOfIsValid(),
				"eventId",
				"publisherId",
				"serviceId",
				"adUnitId",
				"country",
				"area1",
				"area2",
				"area3",
				"gender",
				"age",
				"os",
				"osVer",
				"browser",
				"browserVer",
				"device",
				"adProviderId",
				"adProviderPlaceId",
				"placeKey",
				"dealId",
				"biddingGroupId",
				"connectionType",
				"rank",
				"requestCreativeTypes",
				"NVL(requestCreativeTypes[0], '-') AS requestCreativeType",
				"requestSizes",
				"responseCreativeType",
				"responseSize",
				"bidPrice",
				"bidPriceInKRW AS krwBidPrice",
				"bidPriceInUSD AS usdBidPrice",
				"stat"
			)

		val df2 = addEstimatedReportType(df1, apDf)

		val df3 = addDerivedColumns(df2)

		df3
	}

	/**
	 * 광고공급자 성과 지표 집계
	 *
	 * @param df
	 * @param exprs
	 * @return
	 */
	def getAdProviderStats(df: Dataset[Row], exprs: Vector[Column]): Dataset[Row] = {
		// AdProviderPlaces.creativeType=COMBINED 인 경우에 대한 처리를 위해 디멘전 추가
		val tmpDims = dimensions :+ "responseCreativeType"
		val groupByCols = tmpDims.map(dim => col(dim))

		val df1 = df
			.repartition(groupByCols: _*)
			.groupBy(tmpDims.head, tmpDims.tail: _*)
			.agg(exprs.head, exprs.tail: _*)

		df1
	}

	/**
	 * 광고공급자 성과 지표 S2S + C2S
	 *
	 * @param s2sDf
	 * @param c2sDf
	 * @return
	 */
	def getUnionedAdProviderStats(s2sDf: Dataset[Row], c2sDf: Dataset[Row]): Dataset[Row] = {
		val aggExprs = metrics.map(metric => sum(metric).as(metric))

		// AdProviderPlaces.creativeType=COMBINED 인 경우에 대한 처리를 위해 디멘전 추가
		val tmpDims = dimensions :+ "responseCreativeType"
		val groupByCols = tmpDims.map(dim => col(dim))

		val s2sDf3 = s2sDf.repartition(groupByCols: _*)
		val c2sDf3 = c2sDf.repartition(groupByCols: _*)

		val df1 = s2sDf3
			.unionByName(c2sDf3, true)
			.groupBy(tmpDims.head, tmpDims.tail: _*)
			.agg(aggExprs.head, aggExprs.tail: _*)

		df1
	}

	/**
	 * GFP 수수료율 로드
	 *
	 * @param ymd
	 * @return
	 */
	def loadGfpFeeRate(ymd: String): Dataset[Row] = {
		val readConfig = BaseEnv.mdbDefaultReadConfig
			.withOption("spark.mongodb.input.collection", "GfpFeeRate")
			.withOption("spark.mongodb.input.batchSize", "2000")

		val df = spark.read
			.mongo(readConfig)
			.filter("date == " + ymd)
			.select(
				$"publisher_id.oid".as("publisherId"),
				$"adProvider_id.oid".as("adProviderId"),
				$"feeRate",
			)

		df
	}

	def applyReqRes(statsDf: Dataset[Row]): Dataset[Row] = {
		val exprs = metrics.map(metric => sum(coalesce(col(metric), lit(0))).as(metric))

		// adPvoiderPlaceId, placeCreativeType, responseCreativeType 이 포함된 상태
		val appliedDf = super.applyReqRes(statsDf)

		// 리포트 디멘전만으로 합산
		appliedDf.groupBy(dimensions.head, dimensions.tail: _*)
			.agg(exprs.head, exprs.tail: _*)
	}

	/**
	 * GFP 수수료 적용
	 *
	 * @param statsDf
	 * @param feeRateDf
	 * @return
	 */
	def applyGfpFeeRate(statsDf: Dataset[Row], feeRateDf: Dataset[Row]): Dataset[Row] = {
		val df = statsDf.as("s")
			.join(feeRateDf.as("f"), $"s.publisherId" === $"f.publisherId" && $"s.adProviderId" === $"f.adProviderId", "left")
			.selectExpr(
				"s.*",

				/*
					- GFP estimatedNetRevenue 용.
					- GFP estimatedRevenue에 수수료 적용한 매출(GFP 순수익).
					- 아직까지 본 지표의 사용처는 없으나, eventId in (1, 11, 12) 기준으로 추정수익을 구하는 AP에 한해서 지표를 남김.
				*/
				"NVL(s.krwBidPriceSum * (1 - NVL(f.feeRate, 0)), 0) AS krwNetBidPriceSum",
				"NVL(s.usdBidPriceSum * (1 - NVL(f.feeRate, 0)), 0) AS usdNetBidPriceSum",
			)
		df
	}

	/**
	 * 광고유닛의 선출수 집계
	 *
	 * @param df
	 * @return
	 */
	def getFilledRequests(df: Dataset[Row]): Dataset[Row] = {
		val df1 = df
			.groupBy("publisherId", "adUnitId")
			.agg(expr("SUM(CASE WHEN eventId == 1 THEN 1 ELSE 0 END)").as("filledRequestsOfAu"))
		df1
	}

	/**
	 * 광고유닛의 선출수 추가
	 *
	 * @param filledReqOfAu
	 * @param apStats
	 * @return
	 */
	def addFilledRequestsWithSalt(apStats: Dataset[Row], filledReqOfAu: Dataset[Row]): Dataset[Row] = {
		val SALT_RANGE = SHUFFLE_PARTITION

		val saltValues = new Array[Int](SALT_RANGE)
		for (i <- 0 to SALT_RANGE - 1) saltValues(i) = i

		// 데이터가 큰 apStats에서 skewed publisherId에 salting
		val left = apStats.withColumn("publisherId_", concat($"publisherId", lit("_"), lit(floor(rand * SALT_RANGE)))).as("a")

		// 데이터가 작은 filledReqOfAu에서 salt 심기
		val right = filledReqOfAu.withColumn("salt", explode(lit(saltValues))).as("f")

		// salt 컬럼을 포함한 join
		// apStats.publisherId_ === filledReqOfAu.publisherId + "-" + filledReqOfAu.salt
		//     &&
		// apStats.adUnitId === filledReqOfAu.adUnitId
		val joinExpr =
			$"a.publisherId_" === concat($"f.publisherId", lit("_"), $"f.salt") &&
				$"a.adUnitId" === $"f.adUnitId"

		val df = left
			.join(right, joinExpr, "left")
			.drop($"a.publisherId_")
			.select($"a.*", $"f.filledRequestsOfAu")
			.na.fill(0, Vector("filledRequestsOfAu"))

		df
	}

	/**
	 * 데이터 삭제를 위한 futures 생성
	 */

	import scala.concurrent.ExecutionContext.Implicits.global

	type T = (FileSystem, String) // ABSTRACT TYPE MEMBERS :: https://docs.scala-lang.org/tour/abstract-type-members.html

	override def getFuturesForDelete(helpableParam: Option[T] = None): List[Future[Option[Boolean]]] = {
		val (hdfs, path) = helpableParam.get

		List(Future {
			val result = HdfsUtil.delete(hdfs, path)
			logger.debug(s"$LOG_PREFIX 삭제 결과 $path ($result)")

			Option(true)
		})
	}

	type W = String

	def write(df: DataFrame, writeParam: Option[W] = None): Unit = {
		val outputPath = writeParam.get

		var parallelism = 8
		if (Option(spark.conf.get("spark.executor.cores")).nonEmpty) {
			val c = spark.conf.get("spark.executor.cores").toInt
			val i = spark.conf.get("spark.executor.instances").toInt
			parallelism = c * i
		}
		logger.debug(s"$LOG_PREFIX parallelism:$parallelism")

		df.repartition(parallelism, dimensions.map(col(_)): _*)
			.write
			.mode("overwrite")
			.option("dfs.blocksize", 64 * 1024 * 1024)
			.option("parquet.block.size", 64 * 1024 * 1024)
			.parquet(outputPath)

		logger.debug(s"$LOG_PREFIX HDFS 쓰기 완료. $outputPath")
	}
}

/*
[ hadoop 파일 개수 및 용량 확인 ]
hadoop fs -count -h hdfs://tesseract-dev/data/log/ssp/gfp-silver/er-ssp-client/20220118-*
	-count : DIR_COUNT, FILE_COUNT, CONTENT_SIZE FILE_NAME 을 보여줌
	-count -q : QUOTA, REMAINING_QUATA, SPACE_QUOTA, REMAINING_SPACE_QUOTA, DIR_COUNT, FILE_COUNT, CONTENT_SIZE, FILE_NAME 을 보여줌
	-h : Show sizes human readable format


[ 당첨된 실행 환경 - 리얼 ]
spark-submit --class %aggregator% ^
	--name %aggregator%-%dateTime%-%execTime% ^
	--executor-cores 2 ^
	--num-executors 12 ^
	--executor-memory 6g ^
	--driver-memory 2g ^
	--conf spark.executor.memoryOverhead=1g ^
	--conf spark.driver.memoryOverhead=1g ^
	--conf spark.sql.shuffle.partitions=240 ^
	--conf spark.sql.files.maxPartitionBytes=64mb ^
	--conf spark.scheduler.mode=FAIR ^
	--conf spark.scheduler.allocation.file=hdfs://BizCloud/data/log/gfp/fairscheduler.xml ^
	--conf spark.scheduler.pool=gold_index ^
	--deploy-mode cluster ^
	--master yarn ^
	--queue root.users.gfp ^
	--conf spark.driver.extraJavaOptions=-XX:+UseG1GC ^
	--conf spark.executor.extraJavaOptions=-XX:+UseG1GC ^
	--conf spark.eventLog.enabled=true ^
	--conf spark.sql.caseSensitive=true ^
	--conf spark.sql.parquet.mergeSchema=true ^
	--conf spark.serializer=org.apache.spark.serializer.KryoSerializer ^
	--conf spark.yarn.archive=hdfs://BizCloud/user/irteam/spark3LibArchives/spark-3.2.1-libs.jar ^
	%sparkling_home%/jar/sparkling-s.jar %dateTime%


[ 당첨된 실행 환경 - 테스트 ]
spark-submit --class %aggregator% ^
	--name %aggregator%-%dateTime%-%execTime% ^
	--executor-cores 9
	--num-executors 4
	--executor-memory 2g
	--driver-memory 2g
	--conf spark.sql.shuffle.partitions=800
	--conf spark.scheduler.mode=FAIR ^
	--conf spark.scheduler.allocation.file=hdfs://BizCloud/data/log/gfp/fairscheduler.xml ^
	--conf spark.scheduler.pool=gold_index ^
	--deploy-mode cluster ^
	--master yarn ^
	--queue root.users.gfp ^
	--conf spark.driver.extraJavaOptions=-XX:+UseG1GC ^
	--conf spark.executor.extraJavaOptions=-XX:+UseG1GC ^
	--conf spark.eventLog.enabled=true ^
	--conf spark.sql.caseSensitive=true ^
	--conf spark.sql.parquet.mergeSchema=true ^
	--conf spark.serializer=org.apache.spark.serializer.KryoSerializer ^
	--conf spark.yarn.archive=hdfs://BizCloud/user/irteam/spark3LibArchives/spark-3.2.1-libs.jar ^
	%sparkling_home%/jar/sparkling-s.jar %dateTime%


[ 소요시간 ]
리얼 - 20분
*/
