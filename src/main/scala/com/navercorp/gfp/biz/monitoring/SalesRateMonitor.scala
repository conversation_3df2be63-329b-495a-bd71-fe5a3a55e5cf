package com.navercorp.gfp.biz.monitoring

import java.util.Date
import scala.collection.JavaConverters._
import scala.concurrent.Future

import org.apache.spark.sql._
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTime, Period}

import com.navercorp.gfp.core.BaseEnv.{SILVERGREY_ROOT, mdbDefaultWriteOptions}
import com.navercorp.gfp.core.BaseUdf.toObjectId
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory, SummaryHistoryDetail}
import com.navercorp.gfp.core.{BaseAggregator, BizAggregator, DeleteFutureHelper}
import com.navercorp.gfp.meta.adprovider.{AdProvider, AdProviderType}
import com.navercorp.gfp.util.{HdfsUtil, <PERSON>elo<PERSON>til, TimeUtil}


object SalesRateMonitor extends BaseAggregator {
	val LOG_PREFIX = ".......... [MONITORING-SALES-RATE]"
	val sparkAppId: String = spark.sparkContext.applicationId

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		// 처리 대상 일자
		val startDate = args(0)
		val endDate = args(1)

		// 결과 파일 관련 정보
		val format = DateTimeFormat.forPattern("yyyyMMdd")
		// 전날 salesRate 과의 비교를 위해 하루 전 로그까지 load
		val dateTimeList = dateTimeRange(format.parseDateTime(startDate).minusDays(1), format.parseDateTime(endDate), Period.days(1)).toList

		try {
			val adProviderIds: Seq[String] = getInNaverApWithOutGfd()
			val publisherIds: Seq[String] = publisherDao.getPublisherIds()

			logger.debug(s"$LOG_PREFIX startDate: $startDate")
			logger.debug(s"$LOG_PREFIX endDate: $endDate")
			logger.debug(s"$LOG_PREFIX adProviderIds: $adProviderIds")
			logger.debug(s"$LOG_PREFIX publisherIds: $publisherIds")

			// 이력쌓기 - IN_PROGRESS
			val detail = summaryHistoryId match {
				case Some(_) => None
				case None => Option(SummaryHistoryDetail(
					startDate = Option(startDate), endDate = Option(endDate),
					publisherIds = Option(publisherIds), adProviderIds = Option(adProviderIds)
				))
			}
			val inProgressHist = SummaryHistory(
				datetime = Option(endDate),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(Spark(
					sparkAppId = Option(sparkAppId),
					sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
					sparkStartedAt = Option(new Date)
				)),
				detail = detail
			)
			this.upsertSummaryHistory(inProgressHist)

			// rk, nonRk 경로
			val silverGreyPathList = getSilverGreyPathList(adProviderIds, publisherIds, dateTimeList)

			val silverGreyDf = loadParquetLog(silverGreyPathList) match {
				case Some(df) => df
				case _ =>
					logger.warn(s"$LOG_PREFIX publisherId= $publisherIds 에 해당하는 HDFS 로그 경로가 없음")
					spark.emptyDataFrame
			}

			val aggregator = new SalesRateMonitor(silverGreyDf, startDate, endDate)

			val aggdDf = aggregator.aggregate()

			// MongoDB 기존 데이터 삭제
			aggregator.delete(aggregator.getFuturesForDelete(Option(dateTimeList)))

			// MongoDB 데이터 쓰기
			aggregator.write(aggdDf)

			// 이력쌓기 - COMPLETE
			val completeHist = SummaryHistory(
				spark = Option(Spark(
					sparkAppState = Option(SparkAppState.COMPLETE.toString),
					sparkEndedAt = Option(new Date)
				))
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			case t: Throwable =>
				// 이력쌓기 - FAILURE
				val failureHist = SummaryHistory(
					spark = Option(Spark(
						sparkAppState = Option(SparkAppState.FAILURE.toString),
						sparkAppError = Option(t.getMessage),
						sparkEndedAt = Option(new Date)
					))
				)
				this.upsertSummaryHistory(failureHist)
				logger.error(s"$LOG_PREFIX GAP report 생성 실패. sparkAppId=$sparkAppId, startDate: $startDate, endDate: $endDate, summaryHistoryId=${summaryHistoryId.get} ${t.getMessage}", t)

				throw t
		} finally {
			NeloUtil.waitFor()
		}
	}

	/**
	 * 실버 그레이 로그 경로 구하기
	 *
	 * @param adProviderIds
	 * @param publisherIds
	 * @param dateList
	 * @return pathList
	 */
	def getSilverGreyPathList(adProviderIds: Seq[String], publisherIds: Seq[String], dateList: List[DateTime]): Seq[String] = {
		val pathList = adProviderIds.flatMap { apId =>
			publisherIds.flatMap { pubId =>
				// 광고공급자: 'NDP Video', 매체: '네이버TV' 인 쌍은 제외
				if ((PROFILE == "real" || PROFILE == "stage") && (apId == "5d1085eb38a0a3c9eaaa343d" && pubId == "5f07c40db85575001dd194aa"))
					List("")
				else {
					dateList.map { date =>
						val path = s"$SILVERGREY_ROOT/{rk, nonrk}/${date.toString("yyyy/MM/dd")}/adProviderId=$apId/publisherId=$pubId"

						if (HdfsUtil.existsPathPattern(hdfs, path))
							path
						else {
							logger.debug(s"존재하지 않음 $path")
							""
						}
					}
				}
			}
		}.filter(_.nonEmpty)

		logger.debug(s"pathList $pathList")
		pathList
	}

	/**
	 * 주어진 경로로부터 parquet 로그 로드
	 *
	 * @param pathList
	 * @return Dataset[Row]
	 */
	override def loadParquetLog(pathList: Seq[String]): Option[Dataset[Row]] = {
		// HDFS 경로가 하나도 없는 경우, None
		if (pathList.isEmpty) {
			None
		} else {
			super.loadParquetLog(pathList)
		}
	}

	/**
	 * 'IN_NAVER' 이면서 reportApi.type 이 'GFD' 가 아닌 adProviderIds 구하기
	 *
	 * @return
	 */
	def getInNaverApWithOutGfd(): Seq[String] = {
		val adProviders = adProviderDao.getAdProviderIdsByType(AdProviderType.IN_NAVER.toString).into(new java.util.ArrayList[AdProvider]())

		adProviders.asScala.filter(ap => ap.reportApi.isDefined && ap.reportApi.get.`type`.getOrElse("") != "GFD").map(_._id.toString)
	}
}

class SalesRateMonitor(sgDf: Dataset[Row], startDate: String, endDate: String)(implicit spark: SparkSession) extends BizAggregator with DeleteFutureHelper {
	val LOG_PREFIX = ".......... [MONITORING-SALES-RATE]"
	COLLECTION_NAME = "MonitoringSalesRate"

	import spark.implicits._

	private val dimensions = Seq("date", "adProviderId", "publisherId")
	private val metrics = Seq("revenueKRW", "netRevenueKRW", "revenueUSD", "netRevenueUSD")
	private val selectListForAgg = dimensions ++ metrics
	private val exprs = metrics.map(met => coalesce(sum(met), lit(0)).as(met))

	private val renameHeader = Seq(
		("date", "date"),
		("adProviderId", "adProvider_id"),
		("publisherId", "publisher_id"),

		("revenueKRW", "krwGross"),
		("netRevenueKRW", "krwSales"),
		("salesRateKRW", "krwSalesRate"),
		("deviationSalesRateKRW", "krwSalesRateDeviation"),

		("revenueUSD", "usdGross"),
		("netRevenueUSD", "usdSales"),
		("salesRateUSD", "usdSalesRate"),
		("deviationSalesRateUSD", "usdSalesRateDeviation")
	)

	/**
	 * 데이터 집계
	 *
	 * @return finalDf Dataset[Row]
	 */
	def aggregate(aggParam: Option[A] = None): Dataset[Row] = {
		// 1. 실버그레이로부터 수익률 계산을 위한 기본 집계
		val aggDf = sgDf
			.selectExpr(selectListForAgg: _*)
			.groupBy(dimensions.head, dimensions.tail: _*)
			.agg(exprs.head, exprs.tail: _*)

		// 2. 수익 정보가 0 인 Row 제외 및 수익률 추가
		val rateDf = filterAllZeroMetrics(aggDf, metrics)
			.withColumn("salesRateKRW", when($"revenueKRW" === 0, 0).otherwise(round($"netRevenueKRW" / $"revenueKRW", 3)))
			.withColumn("salesRateUSD", when($"revenueUSD" === 0, 0).otherwise(round($"netRevenueUSD" / $"revenueUSD", 3)))

		// 3. 어제 salesRate 과 비교하기 위한 Window Spec 정의 및 적용
		val windowSpec = Window.partitionBy("adProviderId", "publisherId").orderBy("date")
		val deviationDf = rateDf
			.withColumn("prevSalesRateKRW", lag("salesRateKRW", 1).over(windowSpec))
			.withColumn("prevSalesRateUSD", lag("salesRateUSD", 1).over(windowSpec))
			.withColumn("deviationSalesRateKRW", when($"prevSalesRateKRW".isNull || $"prevSalesRateKRW" === 0, 0).otherwise(round(($"salesRateKRW" - $"prevSalesRateKRW") / $"prevSalesRateKRW", 3)))
			.withColumn("deviationSalesRateUSD", when($"prevSalesRateUSD".isNull || $"prevSalesRateUSD" === 0, 0).otherwise(round(($"salesRateUSD" - $"prevSalesRateUSD") / $"prevSalesRateUSD", 3)))

		// 4. DB 스키마에 맞게 컬럼명 수정 및 추가
		val finalDf = renameColumn(deviationDf.filter($"date".between(startDate, endDate)), renameHeader)
			// 몽고디비 ObjectId 타입으로 변환
			.withColumn("publisher_id", toObjectId($"publisher_id"))
			.withColumn("adProvider_id", toObjectId($"adProvider_id"))
			// 생성한 날짜
			.withColumn("createdAt", functions.current_timestamp)
			// MongoDB TTL Index 에 의해 삭제될 날짜
			.withColumn("expiredAt", TimeUtil.getExpiredAtMonthlyAsColumn($"date", 4))

		println(s"$LOG_PREFIX 최종 스키마 ........")
		finalDf.printSchema()
		//		finalDf.show(finalDf.count().toInt, false)

		finalDf
	}

	/**
	 * 데이터 삭제를 위한 futures 생성
	 */
	type T = List[DateTime]

	def getFuturesForDelete(param: Option[T]): List[Future[Option[Boolean]]] = {
		val dateTimeList = param.get
		logger.debug(s"$LOG_PREFIX dateTimeList= ${dateTimeList.tail.head.toString("yyyyMMdd")} ~ ${dateTimeList.last.toString("yyyyMMdd")}")

		val futures: Seq[Future[Option[Boolean]]] = super.getFuturesForDeleteByDateRange(COLLECTION_NAME, dateTimeList.tail.map(_.toString("yyyyMMdd")))

		futures.toList
	}

	/**
	 * 데이터 추가
	 */
	def write(df: DataFrame, writeParam: Option[W] = None): Unit = {
		logger.debug(s"$LOG_PREFIX COLLECTION_NAME=$COLLECTION_NAME")

		val writeOptions = mdbDefaultWriteOptions
			.updated("spark.mongodb.output.collection", COLLECTION_NAME)

		super.writeToMongoDB(df, writeOptions)
	}
}
