package com.navercorp.gfp.biz.partner.zircon.b

import java.util.Date

import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.hadoop.hive.ql.exec.vector.VectorizedRowBatch
import org.apache.orc.{OrcFile, Reader}
import org.apache.spark.sql.functions.{hash, lit, pmod, rand}
import org.apache.spark.sql.{Dataset, Row}
import org.joda.time.DateTime

import com.navercorp.gfp.core.BaseAggregator
import com.navercorp.gfp.core.BaseEnv._
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory}
import com.navercorp.gfp.util.TimeUtil.SplitYmd
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil, TimeUtil}

/*
	- 파트너 제공을 위해 Zircon B를 일자별 통파일로 컴팩션 처리
		- input  : /user/gfp-data/zircon/b/warehouse/yyyy/MM/dd
		- output : /user/gfp-data/partner/zircon/b/{yyyy}/{MM}/{yyyyMMdd}.orc


요약
3GB 데이터 처리 시 GC 문제와 성능 이슈를 해결하기 위해 다음과 같은 최적화를 적용했습니다:
🔧 주요 최적화 사항
1. 메모리 설정 개선
	Executor: 4GB → 6GB
	Driver: 3GB → 4GB
	Executor 수: 3개 → 4개
2. 파티셔닝 전략 변경
	기존: repartition(1) (즉시 1개 파티션으로 강제 변환)
	개선: repartition(4-8개) → cache() → coalesce(1) (단계적 처리)
3. GC 튜닝
	G1GC 사용 + 세부 튜닝 옵션 추가
	MaxGCPauseMillis=200ms로 pause time 제한
4. Spark 설정 최적화
	Adaptive Query Execution 활성화
	셔플 파티션 수: 120 → 8로 감소
	Parquet 메타데이터 최적화
5. ORC 압축 최적화
	ZLIB 압축 적용
	Stripe 크기 64MB로 설정

예상 성능 개선 효과
	처리 시간: 30-60분 → 10-20분 (50-70% 단축)
	GC 압박: 빈번한 Full GC → 최적화된 G1GC
	메모리 효율성: 단일 파티션 처리 → 분산 처리 후 병합

*/
object PartnerZirconBCompactor extends BaseAggregator {
	val LOG_PREFIX = ".......... [PARTNER-ZB-COMPACTOR]"

	import spark.implicits._

	private val BLOCK_SIZE = 256 * 1024 * 1024 // 256 MB

	/**
	 * 3GB 데이터 처리를 위한 Spark 설정 최적화
	 * Input partition 단계에서 GC 방지 포함
	 */
	def optimizeSparkSettings(): Unit = {
		val conf = spark.sparkContext.getConf
		//
		//		// Input partition 단계 GC 방지 설정
		//		if (!conf.contains("spark.sql.files.maxPartitionBytes")) {
		//			conf.set("spark.sql.files.maxPartitionBytes", "134217728") // 128MB per partition
		//		}
		//		if (!conf.contains("spark.sql.files.openCostInBytes")) {
		//			conf.set("spark.sql.files.openCostInBytes", "4194304") // 4MB file open cost
		//		}
		//		if (!conf.contains("spark.sql.parquet.columnarReaderBatchSize")) {
		//			conf.set("spark.sql.parquet.columnarReaderBatchSize", "2048") // 기본 4096에서 감소
		//		}

		// 파일 리스팅 단계 GC 방지 설정
		if (!conf.contains("spark.sql.sources.parallelPartitionDiscovery.threshold")) {
			conf.set("spark.sql.sources.parallelPartitionDiscovery.threshold", "16") // 기본 32에서 감소
		}
		if (!conf.contains("spark.sql.sources.parallelPartitionDiscovery.parallelism")) {
			conf.set("spark.sql.sources.parallelPartitionDiscovery.parallelism", "4") // 병렬 파일 스캔 제한
		}
		//
		//		// Adaptive Query Execution 설정
		//		if (!conf.contains("spark.sql.adaptive.enabled")) {
		//			conf.set("spark.sql.adaptive.enabled", "true")
		//		}
		//		if (!conf.contains("spark.sql.adaptive.coalescePartitions.enabled")) {
		//			conf.set("spark.sql.adaptive.coalescePartitions.enabled", "true")
		//		}
		//
		//		// 셔플 최적화
		//		if (!conf.contains("spark.sql.shuffle.partitions")) {
		//			conf.set("spark.sql.shuffle.partitions", "8")
		//		}
		//
		//		println(s"$LOG_PREFIX Spark 설정 최적화 완료")
		//		println(s"$LOG_PREFIX maxPartitionBytes = ${conf.get("spark.sql.files.maxPartitionBytes")}")
		//		println(s"$LOG_PREFIX columnarReaderBatchSize = ${conf.get("spark.sql.parquet.columnarReaderBatchSize")}")
	}

	def main(rawArgs: Array[String]): Unit = {
		val startTime = DateTime.now()

		val args = initArgs(rawArgs)
		args.foreach(println)

		// 대상 일자: yyyyMMdd
		val ymd = args(0)

		// Spark 설정 최적화 (3GB 데이터 처리용)
		//		optimizeSparkSettings()

		try {
			// 이력쌓기 - IN_PROGRESS
			val sparkDocForInProgress = Spark(
				sparkAppId = Option(spark.sparkContext.applicationId),
				sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
				sparkStartedAt = Option(new Date)
			)

			val inProgressHist = SummaryHistory(
				datetime = Option(ymd),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(sparkDocForInProgress),
			)
			this.upsertSummaryHistory(inProgressHist)

			val splitYmd: SplitYmd = TimeUtil.getSplitYmd(ymd)

			// 1. Zircon B 원본 경로 가져오기
			val zirconBYmdPath = s"$ZIRCON_B_WAREHOUSE_PATH/${splitYmd.yyyy}/${splitYmd.mm}/${splitYmd.dd}"
			val pathList = getPathList(zirconBYmdPath) match {
				case Some(paths) => paths
				case _ =>
					throw new Exception(s"로드할 Zircon B 경로 없음")
			}

			// 2. Zircon B 로딩 (메모리 최적화)
			val df = loadLogOptimized(pathList)

			// 3. camel case -> snake case
			val renamedDf = toSnakeCase(df)

			// 4. 파트너 임시 경로로 일별 컴팩션 처리
			// 		- outputPath = /user/gfp-data/partner/zircon/b/temp/_ymd=yyyyMMdd
			val outputPath = s"$PARTNER_ZIRCON_B_PATH/temp/_ymd=${ymd}"

			// 동적 파티션 수 계산
			val numPartition = getOptimalPartitions(zirconBYmdPath)
			compact(renamedDf, outputPath, numPartition)

			// 5. 파트너 임시 경로를 파트너 경로로 이동
			// 		- finalFilePath = /user/gfp-data/partner/zircon/b/yyyy/mm/yyyyMMdd.orc
			val finalFilePath = s"$PARTNER_ZIRCON_B_PATH/${splitYmd.yyyy}/${splitYmd.mm}/${ymd}.orc"
			//			//			move(outputPath, destFilePath)
			//			mergeOrcFiles(outputPath, destFilePath)

			//			// 6. 임시 경로 삭제
			//			HdfsUtil.delete(hdfs, outputPath)

			//			mergeOrcFilesParallel(outputPath, finalFilePath, parallelism = 4)

			// 이력쌓기 - COMPLETE
			val sparkDocForComplete = Spark(
				sparkAppState = Option(SparkAppState.COMPLETE.toString),
				sparkEndedAt = Option(new Date)
			)
			val completeHist = SummaryHistory(
				spark = Option(sparkDocForComplete)
			)
			this.upsertSummaryHistory(completeHist)

			logger.debug(s"$LOG_PREFIX 생성 완료. sparkAppId=${spark.sparkContext.applicationId} summaryHistoryId=${summaryHistoryId.get}")
		} catch {
			case t: Throwable =>
				// 이력쌓기 - FAILURE
				val sparkDocForFailure = Spark(
					sparkAppState = Option(SparkAppState.FAILURE.toString),
					sparkAppError = Option(t.getMessage),
					sparkEndedAt = Option(new Date)
				)
				val failureHist = SummaryHistory(
					spark = Option(sparkDocForFailure)
				)
				this.upsertSummaryHistory(failureHist)

				logger.error(s"$LOG_PREFIX 생성 실패. sparkAppId=${spark.sparkContext.applicationId} summaryHistoryId=${summaryHistoryId.get} ${t.getMessage}")
				throw t;
		} finally {
			NeloUtil.waitFor()

			// 소요 시간
			val endTime = DateTime.now()
			val elapsedTime = endTime.getMillis - startTime.getMillis
			logger.debug(s"$LOG_PREFIX 소요시간=${elapsedTime / 1000}sec\n\n")
		}
	}

	/**
	 * ymd에 해당하는 Zircon B Warehouse 경로 가져오기
	 *
	 * @param zirconBYmdPath "/user/gfp-data/zircon/b/warehouse//yyyy/MM/dd"
	 * @return
	 */
	def getPathList(zirconBYmdPath: String): Option[Seq[String]] = {
		try {
			if (HdfsUtil.exists(hdfs, zirconBYmdPath)) {
				println(s"$LOG_PREFIX 경로 존재 확인 완료: $zirconBYmdPath")
				Some(Seq(zirconBYmdPath))
			} else {
				println(s"$LOG_PREFIX 경로 없음: $zirconBYmdPath")
				None
			}
		} catch {
			case ex: Exception =>
				println(s"$LOG_PREFIX 경로 확인 중 오류: ${ex.getMessage}")
				None
		}
	}

	/**
	 * Input partition 단계에서 GC 방지를 위한 메모리 효율적인 로그 로딩
	 *
	 * .option("parquet.read.allocation.size", "8388608") // 8MB 할당 크기 (기본 64MB보다 작게)
	 * Parquet 파일을 읽을 때 메모리에 할당하는 버퍼 크기
	 * 기본값 64MB → 8MB로 87.5% 감소
	 * --num-executors 3  x  --executor-cores 2 => 6 태스크 × 8MB = 32MB 읽기 버퍼
	 *
	 * Before (64MB 버퍼):
	 * 			- 큰 버퍼 객체 → Old Generation 직행
	 * 			- Major GC 유발 가능성 높음
	 * 			- GC Pause Time 증가
	 *
	 * After (8MB 버퍼):
	 * 			- 작은 버퍼 객체 → Young Generation 처리
	 * 			- Minor GC로 대부분 해결
	 * 			- GC Pause Time 감소
	 *
	 * 3GB 데이터 읽기:
	 * 			- 기본값: 3GB ÷ 64MB = 48회 I/O × 4 태스크 = 192회
	 * 			- 최적화: 3GB ÷ 8MB = 384회 I/O × 4 태스크 = 1,536회
	 *
	 * I/O 횟수는 증가하지만:
	 * 			- 메모리 압박 해소로 GC 시간 대폭 감소
	 * 			- 작은 버퍼로 빠른 처리 시작 가능
	 * 			- 전체 처리 시간은 오히려 단축
	 */
	def loadLogOptimized(paths: Seq[String]): Dataset[Row] = {
		val df = spark.read
			.format("parquet")
			.option("recursiveFileLookup", "true")
			.option("parquet.enable.summary-metadata", "false") // 메타데이터 최적화
			.option("parquet.read.allocation.size", "8388608") // 8MB 할당 크기 (기본 64MB보다 작게)
			.option("parquet.compression", "snappy") // 빠른 압축 해제
			.load(paths: _*)

		println(s"$LOG_PREFIX 로드된 데이터의 최종 파티션 수: ${df.rdd.getNumPartitions}")
		df
	}

	/**
	 * 데이터 처리에 최적화된 파티션 수 계산
	 */
	def getOptimalPartitions(path: String): Int = {
		val fsPath = new Path(path)
		val totalFileSize = hdfs.getContentSummary(fsPath).getLength // bytes
		val totalSizeGB = totalFileSize / (1024.0 * 1024.0 * 1024.0) // giga bytes

		logger.debug(s"$LOG_PREFIX 총 파일 크기: ${totalSizeGB} GB")

		// 파티션당 256MB 목표로 계산
		val targetSizePerPartitionMB = 256.0
		val totalSizeMB = totalFileSize / (1024.0 * 1024.0)
		val partitionsBasedOnSize = Math.ceil(totalSizeMB / targetSizePerPartitionMB).toInt

		// SparkContext에서 num-executors와 executor-cores 옵션 가져오기
		val numExecutors = spark.conf.get("spark.executor.instances", "default_value").toInt
		val executorCores = spark.conf.get("spark.executor.cores", "default_value").toInt
		val defaultParallelism = numExecutors * executorCores // spark-submit 옵션에 따름. 현재 6개.
		logger.debug(s"defaultParallelism=$defaultParallelism partitionsBasedOnSize=${partitionsBasedOnSize}")

		// defaultParallelism과 256MB으로 나눈 파티션 개수 중 큰 것으로 제한
		val optimalPartitions = Math.max(defaultParallelism, partitionsBasedOnSize)

		logger.debug(s"$LOG_PREFIX 최적화된 파티션 수: $optimalPartitions (목표 파티션 크기: ${targetSizePerPartitionMB} MB)")
		optimalPartitions
	}

	/**
	 * 일별 컴팩션
	 * 	- stripe?
	 *      ORC 파일의 기본 저장 단위 (행 그룹)
	 *      parquet block size와 같은 개념으로 orc 파일 읽기 시 메모리에 영향을 줌
	 *      기본값 256MB -> 64MB로 줄임
	 *      ORC 파일
	 *      	- ㄴStripe 1 (64MB)
	 *      	- ㄴStripe 2 (64MB)
	 *      	- ㄴStripe 3 (64MB)
	 *      	- ㄴFooter (메타데이터)
	 *
	 * @param df           하루치 데이터프레임
	 * @param outputPath   임시 저장 경로
	 * @param numPartition 파티션 수 (기본값: 13, 3GB 기준)
	 */
	def compact(df: Dataset[Row], outputPath: String, numPartition: Int = 13): Unit = {
		logger.debug(s"$LOG_PREFIX outputPath=$outputPath \n\tnumPartition= $numPartition")

		//		// 1. 복합 키 기반 균등 분배 (시간 편중 문제 해결)
		//		val balancedDf = df.withColumn("partition_key",
		//			pmod(hash($"ap_timestamp", $"publisher_id", $"ad_provider_id", rand()), lit(numPartition)))
		//
		//		// 2. 임시 디렉토리에 여러 파일로 저장
		//		val tempOutputPath = s"${outputPath}_temp"
		//
		//		balancedDf
		//			.repartition(numPartition, $"partition_key") // 복합 키로 더 균등한 분배
		//			.drop("partition_key") // 임시 컬럼 제거
		df
			.coalesce(numPartition)
			.write
			.mode("overwrite")
			.option("orc.stripe.size", "67108864") // 64MB stripe size
			.option("orc.row.index.stride", "10000") // 10K rows per index
			.orc(outputPath)
	}

	/**
	 * 파트너 임시 경로를 파트너 경로로 이동
	 * 	- partner/zircon/b/temp/_ymd=yyyymmdd/xxx.orc -> partner/zircon/b/yyyy/MM/yyyyMMdd.orc
	 *
	 * @param srcPath
	 * @param destFilePath
	 */
	def move(srcPath: String, destFilePath: String): Unit = {
		// destFilePath 의 디렉토리 경로가 없으면 생성
		val destParentPath = new Path(destFilePath).getParent
		if (!HdfsUtil.exists(hdfs, destParentPath.toString)) hdfs.mkdirs(destParentPath)

		val files = hdfs.globStatus(new Path(s"$srcPath/*.orc"))
		if (files.length == 1) {
			val srcFilePath = files.head.getPath.toString
			val moveResult = HdfsUtil.move(hdfs, srcFilePath, destFilePath)

			if (moveResult) {
				logger.debug(s"$LOG_PREFIX moveResult= $moveResult")
				logger.debug(s"$LOG_PREFIX srcFilePath= $srcFilePath")
				logger.debug(s"$LOG_PREFIX destFilePath= $destFilePath")
			} else {
				throw new Exception(s"임시 경로를 파트너 경로로 이동 실패 ( moveResult= $moveResult )")
			}
		} else {
			throw new Exception(s"$LOG_PREFIX $srcPath 에 parquet 파일이 1개가 아님")
		}
	}

	def mergeOrcFiles(inputDir: String, outputFile: String): Unit = {
		logger.debug(s"$LOG_PREFIX ORC 파일 병합 시작.. inputDir=$inputDir, outputFile=$outputFile")
		val conf = spark.sparkContext.hadoopConfiguration
		val fs = FileSystem.get(conf)

		// ORC Writer 설정
		val writerOptions = OrcFile.writerOptions(conf)

		// inputDir에 있는 파일을 하나 읽어 orc schema를 추출하는 코드 생성
		val files = fs.listStatus(new Path(inputDir)).filter(_.getPath.getName.endsWith(".orc"))
		if (files.isEmpty) {
			throw new Exception(s"입력 디렉토리 $inputDir 에 ORC 파일이 없습니다.")
		}

		// 첫 번째 ORC 파일에서 스키마 추출
		val firstOrcFile = files.head.getPath
		val reader: Reader = OrcFile.createReader(firstOrcFile, OrcFile.readerOptions(conf))
		val schema = reader.getSchema

		// writerOptions에 스키마 설정
		writerOptions.setSchema(schema)

		logger.debug(s"inputDir=${inputDir} outputFile=${outputFile} writerOptions=${writerOptions.getSchema()}")
		logger.debug(s"new Path(outputFile)=${new Path(outputFile)}")
		val writer = OrcFile.createWriter(new Path(outputFile), writerOptions)

		try {
			// 입력 디렉토리의 ORC 파일 목록 가져오기
			val files = fs.listStatus(new Path(inputDir)).filter(_.getPath.getName.endsWith(".orc"))

			files.foreach { fileStatus =>
				val reader: Reader = OrcFile.createReader(fileStatus.getPath, OrcFile.readerOptions(conf))
				val rows: VectorizedRowBatch = reader.getSchema.createRowBatch()

				val readerRows = reader.rows()
				while (readerRows.nextBatch(rows)) {
					writer.addRowBatch(rows) // 데이터를 병합
				}
				readerRows.close()
			}
		} finally {
			writer.close() // Writer 닫기
		}

		logger.debug(s"ORC 파일 병합 완료: $outputFile")
	}


	def mergeOrcFilesParallel(inputDir: String, outputFile: String, parallelism: Int = 4): Unit = {
		val conf = spark.sparkContext.hadoopConfiguration
		val fs = FileSystem.get(conf)

		val files = fs.listStatus(new Path(inputDir)).filter(_.getPath.getName.endsWith(".orc"))

		if (files.length <= 1) {
			// 파일이 1개 이하면 단순 복사 또는 그대로 사용
			if (files.length == 1) {
				fs.rename(files.head.getPath, new Path(outputFile))
			}
			return
		}

		// 파일들을 그룹으로 나누어 병렬 처리
		val fileGroups = files.grouped(Math.ceil(files.length.toDouble / parallelism).toInt).toSeq

		logger.debug(s"병렬 병합 시작: ${files.length}개 파일을 ${fileGroups.length}개 그룹으로 분할")

		// 각 그룹을 병렬로 임시 파일로 병합
		val tempFiles = fileGroups.zipWithIndex.par.map { case (group, index) =>
			val tempFile = s"${outputFile}_temp_$index.orc"
			mergeOrcFilesGroup(group, tempFile)
			tempFile
		}.seq.toArray

		try {
			// 임시 파일들을 최종 병합
			mergeOrcFilesSequential(tempFiles, outputFile)
		} finally {
			// 임시 파일들 정리
			tempFiles.foreach { tempFile =>
				try {
					fs.delete(new Path(tempFile), false)
				} catch {
					case e: Exception => logger.warn(s"임시 파일 삭제 실패: $tempFile", e)
				}
			}
		}

		logger.debug(s"병렬 ORC 파일 병합 완료: $outputFile")
	}

	/**
	 * 파일 그룹을 하나의 임시 파일로 병합
	 */
	def mergeOrcFilesGroup(fileGroup: Array[org.apache.hadoop.fs.FileStatus], outputFile: String): Unit = {
		val conf = spark.sparkContext.hadoopConfiguration
		val fs = FileSystem.get(conf)

		if (fileGroup.isEmpty) return

		// Writer 옵션 설정
		val writerOptions = OrcFile.writerOptions(conf)
			.compress(org.apache.orc.CompressionKind.ZLIB)
			.stripeSize(67108864) // 64MB stripe size
			.rowIndexStride(10000) // 10K rows per index

		// 첫 번째 파일에서 스키마 추출
		val firstReader: Reader = OrcFile.createReader(fileGroup.head.getPath, OrcFile.readerOptions(conf))
		val schema = firstReader.getSchema
		writerOptions.setSchema(schema)

		val writer = OrcFile.createWriter(new Path(outputFile), writerOptions)

		try {
			fileGroup.foreach { fileStatus =>
				val reader: Reader = OrcFile.createReader(fileStatus.getPath, OrcFile.readerOptions(conf))
				val rows: VectorizedRowBatch = reader.getSchema.createRowBatch()

				val readerRows = reader.rows()
				while (readerRows.nextBatch(rows)) {
					writer.addRowBatch(rows)
				}
				readerRows.close()
			}
		} finally {
			writer.close()
		}

		logger.debug(s"그룹 병합 완료: ${fileGroup.length}개 파일 → $outputFile")
	}

	/**
	 * 임시 파일들을 최종 파일로 순차 병합
	 */
	def mergeOrcFilesSequential(tempFiles: Array[String], outputFile: String): Unit = {
		val conf = spark.sparkContext.hadoopConfiguration
		val fs = FileSystem.get(conf)

		if (tempFiles.isEmpty) return

		// 임시 파일이 1개면 단순 이동
		if (tempFiles.length == 1) {
			fs.rename(new Path(tempFiles.head), new Path(outputFile))
			return
		}

		// Writer 옵션 설정
		val writerOptions = OrcFile.writerOptions(conf)
			.compress(org.apache.orc.CompressionKind.ZLIB)
			.stripeSize(67108864) // 64MB stripe size
			.rowIndexStride(10000) // 10K rows per index

		// 첫 번째 임시 파일에서 스키마 추출
		val firstReader: Reader = OrcFile.createReader(new Path(tempFiles.head), OrcFile.readerOptions(conf))
		val schema = firstReader.getSchema
		writerOptions.setSchema(schema)

		val writer = OrcFile.createWriter(new Path(outputFile), writerOptions)

		try {
			tempFiles.foreach { tempFile =>
				val reader: Reader = OrcFile.createReader(new Path(tempFile), OrcFile.readerOptions(conf))
				val rows: VectorizedRowBatch = reader.getSchema.createRowBatch()

				val readerRows = reader.rows()
				while (readerRows.nextBatch(rows)) {
					writer.addRowBatch(rows)
				}
				readerRows.close()
			}
		} finally {
			writer.close()
		}

		logger.debug(s"최종 병합 완료: ${tempFiles.length}개 임시 파일 → $outputFile")
	}


	/*
	// orc 포맷을 무시하고 단순 병합이기에 사용할 수 없음
	def merge(hdfs: FileSystem, inputDir: String, outputFile: String): Boolean = {
		val inputPath = new Path(inputDir)
		val outputPath = new Path(outputFile)

		if (!hdfs.exists(inputPath)) {
			throw new IllegalArgumentException(s"Input directory $inputDir does not exist.")
		}

		val outputStream = hdfs.create(outputPath, true)
		try {
			val files = hdfs.listStatus(inputPath).filter(_.isFile).map(_.getPath)
			files.foreach { file =>
				val inputStream = hdfs.open(file)
				try {
					org.apache.hadoop.io.IOUtils.copyBytes(inputStream, outputStream, hdfs.getConf, false)
				} finally {
					inputStream.close()
				}
			}
			true
		} catch {
			case e: Exception =>
				e.printStackTrace()
				false
		} finally {
			outputStream.close()
		}
	}*/

}
