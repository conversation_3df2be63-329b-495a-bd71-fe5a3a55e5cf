package com.navercorp.gfp.biz.partner.zircon.b

import java.util.Date

import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.hadoop.hive.ql.exec.vector.VectorizedRowBatch
import org.apache.orc.{OrcFile, Reader}
import org.apache.spark.sql.functions.{hash, lit, pmod}
import org.apache.spark.sql.{Dataset, Row}
import org.joda.time.DateTime

import com.navercorp.gfp.core.BaseAggregator
import com.navercorp.gfp.core.BaseEnv._
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory}
import com.navercorp.gfp.util.TimeUtil.SplitYmd
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil, TimeUtil}

/*
	- 파트너 제공을 위해 Zircon B를 일자별 통파일로 컴팩션 처리
		- input  : /user/gfp-data/zircon/b/warehouse/yyyy/MM/dd
		- output : /user/gfp-data/partner/zircon/b/{yyyy}/{MM}/{yyyyMMdd}.orc


요약
3GB 데이터 처리 시 GC 문제와 성능 이슈를 해결하기 위해 다음과 같은 최적화를 적용했습니다:
🔧 주요 최적화 사항
1. 메모리 설정 개선
	Executor: 4GB → 6GB
	Driver: 3GB → 4GB
	Executor 수: 3개 → 4개
2. 파티셔닝 전략 변경
	기존: repartition(1) (즉시 1개 파티션으로 강제 변환)
	개선: repartition(4-8개) → cache() → coalesce(1) (단계적 처리)
3. GC 튜닝
	G1GC 사용 + 세부 튜닝 옵션 추가
	MaxGCPauseMillis=200ms로 pause time 제한
4. Spark 설정 최적화
	Adaptive Query Execution 활성화
	셔플 파티션 수: 120 → 8로 감소
	Parquet 메타데이터 최적화
5. ORC 압축 최적화
	ZLIB 압축 적용
	Stripe 크기 64MB로 설정

예상 성능 개선 효과
	처리 시간: 30-60분 → 10-20분 (50-70% 단축)
	GC 압박: 빈번한 Full GC → 최적화된 G1GC
	메모리 효율성: 단일 파티션 처리 → 분산 처리 후 병합

*/
object PartnerZirconBCompactor extends BaseAggregator {
	val LOG_PREFIX = ".......... [PARTNER-ZB-COMPACTOR]"

	import spark.implicits._

	private val BLOCK_SIZE = 256 * 1024 * 1024 // 256 MB

	/**
	 * 3GB 데이터 처리를 위한 Spark 설정 최적화
	 */
	def optimizeSparkSettings(): Unit = {
		val sc = spark.sparkContext

		// GC 최적화를 위한 설정
		//		sc.setLocalProperty("spark.sql.adaptive.coalescePartitions.minPartitionNum", "1")

		// 셔플 최적화
		//		sc.setLocalProperty("spark.sql.adaptive.shuffle.targetPostShuffleInputSize", "134217728") // 128MB

		logger.debug(s"$LOG_PREFIX Spark 설정 최적화 완료")
	}

	def main(rawArgs: Array[String]): Unit = {
		val startTime = DateTime.now()

		val args = initArgs(rawArgs)
		args.foreach(println)

		// 대상 일자: yyyyMMdd
		val ymd = args(0)

		// Spark 설정 최적화 (3GB 데이터 처리용)
		//		optimizeSparkSettings()

		try {
			// 이력쌓기 - IN_PROGRESS
			val sparkDocForInProgress = Spark(
				sparkAppId = Option(spark.sparkContext.applicationId),
				sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
				sparkStartedAt = Option(new Date)
			)

			val inProgressHist = SummaryHistory(
				datetime = Option(ymd),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(sparkDocForInProgress),
			)
			this.upsertSummaryHistory(inProgressHist)

			val splitYmd: SplitYmd = TimeUtil.getSplitYmd(ymd)

			// 1. Zircon B 원본 경로 가져오기
			val zirconBYmdPath = s"$ZIRCON_B_WAREHOUSE_PATH/${splitYmd.yyyy}/${splitYmd.mm}/${splitYmd.dd}"
			val pathList = getPathList(zirconBYmdPath) match {
				case Some(paths) => paths
				case _ =>
					throw new Exception(s"로드할 Zircon B 경로 없음")
			}

			// 2. Zircon B 로딩 (메모리 최적화)
//			val df = loadLogOptimized(pathList)
			val df = loadLog(pathList)

			// 3. camel case -> snake case
			val renamedDf = toSnakeCase(df)

			// 4. 파트너 임시 경로로 일별 컴팩션 처리
			//   outputPath = /user/gfp-data/partner/zircon/b/temp/_ymd=yyyyMMdd
			val outputPath = s"$PARTNER_ZIRCON_B_PATH/temp/_ymd=${ymd}"

			// 동적 파티션 수 계산
			val numPartition = getOptimalPartitions(zirconBYmdPath)
			compact(renamedDf, outputPath, numPartition)

//			// 5. 파트너 임시 경로를 파트너 경로로 이동
//			// destFilePath = /user/gfp-data/partner/zircon/b/yyyy/mm/yyyyMMdd.orc
//			val destFilePath = s"$PARTNER_ZIRCON_B_PATH/${splitYmd.yyyy}/${splitYmd.mm}/${ymd}.orc"
//			//			move(outputPath, destFilePath)
//			mergeOrcFiles(outputPath, destFilePath)

			//			// 6. 임시 경로 삭제
			//			HdfsUtil.delete(hdfs, outputPath)


			// 이력쌓기 - COMPLETE
			val sparkDocForComplete = Spark(
				sparkAppState = Option(SparkAppState.COMPLETE.toString),
				sparkEndedAt = Option(new Date)
			)
			val completeHist = SummaryHistory(
				spark = Option(sparkDocForComplete)
			)
			this.upsertSummaryHistory(completeHist)

			logger.debug(s"$LOG_PREFIX 생성 완료. sparkAppId=${spark.sparkContext.applicationId} summaryHistoryId=${summaryHistoryId.get}")
		} catch {
			case t: Throwable =>
				// 이력쌓기 - FAILURE
				val sparkDocForFailure = Spark(
					sparkAppState = Option(SparkAppState.FAILURE.toString),
					sparkAppError = Option(t.getMessage),
					sparkEndedAt = Option(new Date)
				)
				val failureHist = SummaryHistory(
					spark = Option(sparkDocForFailure)
				)
				this.upsertSummaryHistory(failureHist)

				logger.error(s"$LOG_PREFIX 생성 실패. sparkAppId=${spark.sparkContext.applicationId} summaryHistoryId=${summaryHistoryId.get} ${t.getMessage}")
				throw t;
		} finally {
			NeloUtil.waitFor()

			// 소요 시간
			val endTime = DateTime.now()
			val elapsedTime = endTime.getMillis - startTime.getMillis
			logger.debug(s"$LOG_PREFIX 소요시간=${elapsedTime / 1000}sec\n\n")
		}
	}

	/**
	 * ymd 에 해당하는 경로 가져오기
	 *
	 * @param splitYmd
	 * @return
	 */
	def getPathList(zirconBYmdPath: String): Option[Seq[String]] = {
		// 참고 :: 마지막 하위 경로까지 풀어서 써야, Spark Stage 에서 한 단계로 처리됨
		//  path = "/user/gfp-data/zircon/b/warehouse//yyyy/MM/dd/*/_publisherId=*/_adProviderId=*"
		//		val path = s"$zirconBYmdPath/*/_publisherId=*/_adProviderId=*"
		val path = s"$zirconBYmdPath"

		// hdfs 에 존재하는 경로만 추출
		//		if (HdfsUtil.existsPathPattern(hdfs, s"$zirconBYmdPath/*/_publisherId=*/_adProviderId=*")) Some(Seq(path)) else None
		if (HdfsUtil.exists(hdfs, zirconBYmdPath)) Some(Seq(path)) else None
	}

	/**
	 * 메모리 효율적인 로그 로딩
	 * 큰 데이터셋 처리를 위한 최적화 적용
	 */
	def loadLogOptimized(paths: Seq[String]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX 최적화된 로그 로딩 시작")

		val df = spark.read
			.format("parquet")
			.option("recursiveFileLookup", "true")
			.option("parquet.enable.summary-metadata", "false") // 메타데이터 최적화
			.load(paths: _*)

		// 데이터 통계 출력
		logger.debug(s"$LOG_PREFIX 로딩된 파티션 수: ${df.rdd.getNumPartitions}")

		df
	}

	/**
	 * 데이터 처리에 최적화된 파티션 수 계산
	 * 메모리 효율성과 병렬 처리 성능의 균형을 고려
	 */
	def getOptimalPartitions(path: String): Int = {
		val fsPath = new Path(path)
		val totalFileSize = hdfs.getContentSummary(fsPath).getLength // bytes
		val totalSizeGB = totalFileSize / (1024.0 * 1024.0 * 1024.0) // giga bytes

		logger.debug(s"$LOG_PREFIX 총 파일 크기: ${totalSizeGB}GB")

		// 파티션당 256MB 목표로 계산
		val targetSizePerPartitionMB = 256.0
		val totalSizeMB = totalFileSize / (1024.0 * 1024.0)
		val partitionsBasedOnSize = Math.ceil(totalSizeMB / targetSizePerPartitionMB).toInt

		// SparkContext에서 num-executors와 executor-cores 옵션 가져오기
		val numExecutors = spark.conf.get("spark.executor.instances", "default_value").toInt
		val executorCores = spark.conf.get("spark.executor.cores", "default_value").toInt
		val maxPartitions = numExecutors * executorCores

		logger.debug(s"numExecutors=$numExecutors, executorCores=$executorCores")

		// 최소 12개(3GB), 최대 maxPartitions 파티션으로 제한
		val optimalPartitions = Math.max(12, Math.min(maxPartitions, partitionsBasedOnSize))

		logger.debug(s"$LOG_PREFIX 최적화된 파티션 수: $optimalPartitions (목표 파티션 크기: ${targetSizePerPartitionMB}MB)")
		optimalPartitions
	}

	/**
	 * 일별 컴팩션 (최적화 버전)
	 * 	- 메모리 효율적인 파티셔닝 적용
	 *
	 * @param df           하루치 데이터프레임
	 * @param outputPath   임시 저장 경로
	 * @param numPartition 파티션 수 (기본값: 12, 3GB 기준)
	 */
	def compact(df: Dataset[Row], outputPath: String, numPartition: Int = 12): Unit = {
		logger.debug(s"$LOG_PREFIX outputPath=$outputPath \n\tnumPartition= $numPartition")

		// 1. 균등 분배를 위한 키 생성 (해시 기반 분산)
		val balancedDf = df.withColumn("partition_key",	pmod(hash($"ap_timestamp"), lit(numPartition)))

		// 2. 파티션 키로 균등하게 분배
		balancedDf
			.repartition(numPartition, $"partition_key")
			.drop("partition_key") // 임시 컬럼 제거
			// 데이터를 균등하게 분배
			.write
			.mode("overwrite")
			//				.option("orc.compress", "ZLIB") // 압축 적용
			.option("orc.stripe.size", "67108864")
			// 64MB stripe size
			.option("orc.row.index.stride", "10000")
			// 인덱스 최적화
			.orc(outputPath)
	}

	/**
	 * 파트너 임시 경로를 파트너 경로로 이동
	 * 	- partner/zircon/b/temp/_ymd=yyyymmdd/xxx.orc -> partner/zircon/b/yyyy/MM/yyyyMMdd.orc
	 *
	 * @param srcPath
	 * @param destFilePath
	 */
	def move(srcPath: String, destFilePath: String): Unit = {
		// destFilePath 의 디렉토리 경로가 없으면 생성
		val destParentPath = new Path(destFilePath).getParent
		if (!HdfsUtil.exists(hdfs, destParentPath.toString)) hdfs.mkdirs(destParentPath)

		val files = hdfs.globStatus(new Path(s"$srcPath/*.orc"))
		if (files.length == 1) {
			val srcFilePath = files.head.getPath.toString
			val moveResult = HdfsUtil.move(hdfs, srcFilePath, destFilePath)

			if (moveResult) {
				logger.debug(s"$LOG_PREFIX moveResult= $moveResult")
				logger.debug(s"$LOG_PREFIX srcFilePath= $srcFilePath")
				logger.debug(s"$LOG_PREFIX destFilePath= $destFilePath")
			} else {
				throw new Exception(s"임시 경로를 파트너 경로로 이동 실패 ( moveResult= $moveResult )")
			}
		} else {
			throw new Exception(s"$LOG_PREFIX $srcPath 에 parquet 파일이 1개가 아님")
		}
	}

	def mergeOrcFiles(inputDir: String, outputFile: String): Unit = {
		logger.debug(s"$LOG_PREFIX ORC 파일 병합 시작.. inputDir=$inputDir, outputFile=$outputFile")
		val conf = spark.sparkContext.hadoopConfiguration
		val fs = FileSystem.get(conf)

		// ORC Writer 설정
		val writerOptions = OrcFile.writerOptions(conf)

		// inputDir에 있는 파일을 하나 읽어 orc schema를 추출하는 코드 생성
		val files = fs.listStatus(new Path(inputDir)).filter(_.getPath.getName.endsWith(".orc"))
		if (files.isEmpty) {
			throw new Exception(s"입력 디렉토리 $inputDir 에 ORC 파일이 없습니다.")
		}

		// 첫 번째 ORC 파일에서 스키마 추출
		val firstOrcFile = files.head.getPath
		val reader: Reader = OrcFile.createReader(firstOrcFile, OrcFile.readerOptions(conf))
		val schema = reader.getSchema

		// writerOptions에 스키마 설정
		writerOptions.setSchema(schema)

		logger.debug(s"inputDir=${inputDir} outputFile=${outputFile} writerOptions=${writerOptions.getSchema()}")
		logger.debug(s"new Path(outputFile)=${new Path(outputFile)}")
		val writer = OrcFile.createWriter(new Path(outputFile), writerOptions)

		try {
			// 입력 디렉토리의 ORC 파일 목록 가져오기
			val files = fs.listStatus(new Path(inputDir)).filter(_.getPath.getName.endsWith(".orc"))

			files.foreach { fileStatus =>
				val reader: Reader = OrcFile.createReader(fileStatus.getPath, OrcFile.readerOptions(conf))
				val rows: VectorizedRowBatch = reader.getSchema.createRowBatch()

				val readerRows = reader.rows()
				while (readerRows.nextBatch(rows)) {
					writer.addRowBatch(rows) // 데이터를 병합
				}
				readerRows.close()
			}
		} finally {
			writer.close() // Writer 닫기
		}

		logger.debug(s"ORC 파일 병합 완료: $outputFile")
	}



	/*
	// orc 포맷을 무시하고 단순 병합이기에 사용할 수 없음
	def merge(hdfs: FileSystem, inputDir: String, outputFile: String): Boolean = {
		val inputPath = new Path(inputDir)
		val outputPath = new Path(outputFile)

		if (!hdfs.exists(inputPath)) {
			throw new IllegalArgumentException(s"Input directory $inputDir does not exist.")
		}

		val outputStream = hdfs.create(outputPath, true)
		try {
			val files = hdfs.listStatus(inputPath).filter(_.isFile).map(_.getPath)
			files.foreach { file =>
				val inputStream = hdfs.open(file)
				try {
					org.apache.hadoop.io.IOUtils.copyBytes(inputStream, outputStream, hdfs.getConf, false)
				} finally {
					inputStream.close()
				}
			}
			true
		} catch {
			case e: Exception =>
				e.printStackTrace()
				false
		} finally {
			outputStream.close()
		}
	}*/

}
