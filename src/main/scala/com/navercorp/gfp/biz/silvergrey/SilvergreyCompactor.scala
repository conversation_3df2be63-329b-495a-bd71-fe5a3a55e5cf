package com.navercorp.gfp.biz.silvergrey

import java.util.Date
import scala.collection.mutable
import scala.util.Try

import org.apache.hadoop.fs.{FileStatus, Path}
import org.joda.time.format.DateTimeFormat

import com.navercorp.gfp.core.BaseAggregator
import com.navercorp.gfp.core.BaseEnv.{SILVERGREY_NONRK_PATH, SILVERGREY_RK_PATH, SILVERGREY_ROOT}
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory}
import com.navercorp.gfp.exception.BusinessException
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil}

/*
	- 대상 경로 : /user/gfp-data/silvergrey/{rk,nonrk}/yyyy/mm/dd
	- 하루치 대상으로 컴팩션 처리 (90 일 전 데이터 대상)
		- 구글 62일치가 매일 갱신 되고 있고, 재처리 고려해서 90일로 설정함

	spark 설정
		--num-executors 3
		--executor-cores 2
		--executor-memory 4g
		--conf spark.sql.shuffle.partitions=200
		--conf spark.sql.files.maxPartitionBytes=32mb
*/
object SilvergreyCompactor extends BaseAggregator {
	val LOG_PREFIX = ".......... [SILVERGREY-COMPACTOR]"

	val BACKUP_DIR = s"$SILVERGREY_ROOT/backup"
	val COMPACTION_DIR = s"$SILVERGREY_ROOT/compaction"

	private var sparkAppId: String = ""

	private val silvergreyDao = new SilvergreyDao

	import spark.implicits._

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		var backupComplete = false
		var compactionComplete = false
		var rollbackComplete = false

		var rkPath, nonRkPath,
		rkBackupPath, nonRkBackupPath,
		rkCompactionPath, nonRkCompactionPath = ""

		// 처리 대상 일자
		val targetDate = args(0)

		try {
			// args 밸리데이션 체크
			// require 는 runtime 시점에 Exception 을 던진다.
			// Exception 없이, 인스턴스 생성 시점에 validation 체크를 하고 싶으면 apply 를 구현하도록 한다.
			//  - https://gist.github.com/jkpl/4932e8730c1810261381851b13dfd29d
			//  - https://www.47deg.com/blog/smart-constructors-in-scala/#smart-constructors-for-case-classes-0
			//  - https://stackoverflow.com/questions/5982484/scala-lift-check-if-date-is-correctly-formatted
			require(Try(DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(targetDate)).isSuccess, s"targetDate($targetDate) is invalid format (must be yyyyMMdd)")


			// 이력쌓기 - 진행중 (IN_PROGRESS)
			sparkAppId = spark.sparkContext.applicationId
			val inProgressHist = SummaryHistory(
				datetime = Option(targetDate),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(Spark(
					sparkAppId = Option(sparkAppId),
					sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
					sparkStartedAt = Option(new Date)
				)),
			)
			this.upsertSummaryHistory(inProgressHist)


			// 대상 날짜에 해당하는 기간(period.endDate)의 리포트 연동 스케쥴이 모두 처리 완료 상태인지 체크
			if (!this.checkSilvergreySchedulesComplete(targetDate)) {
				throw BusinessException(s"targetDate= $targetDate 에 해당 하는 기간(endDate)의 리포트 연동 스케쥴이 모두 완료 되지 않았음")
			}

			val targetDt = DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(targetDate)

			// 0. 실버그레이 원본 & 백업 & 컴팩션 경로
			// 	- /user/gfp-data/silvergrey/{rk,nonrk}/2024/01/01
			// 	- /user/gfp-data/silvergrey/backup/{rk,nonrk}/2024/01/01
			// 	- /user/gfp-data/silvergrey/compaction/{rk,nonrk}/2024/01/01
			rkPath = s"$SILVERGREY_RK_PATH/${targetDt.toString("yyyy/MM/dd")}"
			nonRkPath = s"$SILVERGREY_NONRK_PATH/${targetDt.toString("yyyy/MM/dd")}"
			rkBackupPath = s"${rkPath.replace(SILVERGREY_ROOT, BACKUP_DIR)}"
			nonRkBackupPath = s"${nonRkPath.replace(SILVERGREY_ROOT, BACKUP_DIR)}"
			rkCompactionPath = s"${rkPath.replace(SILVERGREY_ROOT, COMPACTION_DIR)}"
			nonRkCompactionPath = s"${nonRkPath.replace(SILVERGREY_ROOT, COMPACTION_DIR)}"

			logger.debug(s"rkPath= $rkPath")
			logger.debug(s"nonRkPath= $nonRkPath")
			logger.debug(s"rkBackupPath= $rkBackupPath")
			logger.debug(s"nonRkBackupPath= $nonRkBackupPath")
			logger.debug(s"rkCompactionPath= $rkCompactionPath")
			logger.debug(s"nonRkCompactionPath= $nonRkCompactionPath")


			// 1. silvergrey backup 경로에 원본 데이터 복사
			// 	- dstDir = /user/gfp-data/silvergrey/backup/{rk,nonrk}/2024/01/01
			val rkBackupCopyResult = HdfsUtil.copy(hdfs, rkPath, rkBackupPath)
			val nonRkBackupCopyResult = HdfsUtil.copy(hdfs, nonRkPath, nonRkBackupPath)

			if (rkBackupCopyResult && nonRkBackupCopyResult) {
				logger.debug(s"$LOG_PREFIX silvergrey backup 경로에 원본 데이터 복사 성공")
			} else {
				throw new Exception(s"silvergrey backup 경로에 원본 데이터 복사 실패 ( rkBackupCopyResult= $rkBackupCopyResult, nonRkBackupCopyResult= $nonRkBackupCopyResult )")
			}

			backupComplete = true


			// 2. 컴팩션 처리
			this.compact(rkPath, rkCompactionPath)
			this.compact(nonRkPath, nonRkCompactionPath)

			// AP & PUB 경로명 수정
			// 	- _adProviderId=xxx/_publisherId=xxx -> adProviderId=xxx/publisherId=xxx
			val rkPathRenameResult = this.rename(rkCompactionPath)
			val nonRkPathRenameResult = this.rename(nonRkCompactionPath)

			if (rkPathRenameResult && nonRkPathRenameResult) {
				logger.debug(s"$LOG_PREFIX AP & PUB 경로명 수정 성공")
			} else {
				throw new Exception(s"AP & PUB 경로명 수정 실패 ( rkPathRenameResult= $rkPathRenameResult, nonRkPathRenameResult= $nonRkPathRenameResult )")
			}

			// 컴팩션 완료 파일 생성 (_COMPACTION_SUCCESS)
			HdfsUtil.create(hdfs, s"$rkCompactionPath/_COMPACTION_SUCCESS", overwrite = true)
			HdfsUtil.create(hdfs, s"$nonRkCompactionPath/_COMPACTION_SUCCESS", overwrite = true)

			logger.debug(s"$LOG_PREFIX $targetDate rk/nonrk _COMPACTION_SUCCESS 생성 완료")


			// 3. 컴팩션 경로를 원본 경로로 이동
			val rkCompactionMoveResult = HdfsUtil.move(hdfs, rkCompactionPath, rkPath)
			val nonRkCompactionMoveResult = HdfsUtil.move(hdfs, nonRkCompactionPath, nonRkPath)

			if (rkCompactionMoveResult && nonRkCompactionMoveResult) {
				logger.debug(s"$LOG_PREFIX 컴팩션 경로를 원본 경로로 이동 성공")
			} else {
				throw new Exception(s"컴팩션 경로를 원본 경로로 이동 실패 ( rkCompactionMoveResult= $rkCompactionMoveResult, nonRkCompactionMoveResult= $nonRkCompactionMoveResult )")
			}

			logger.debug(s"$LOG_PREFIX $targetDate rk/nonrk 컴팩션 경로를 원본 경로로 이동 완료 (컴팩션 성공)")

			compactionComplete = true


			// 이력쌓기 - 완료 (COMPLETE)
			val completeHist = SummaryHistory(
				spark = Option(Spark(
					sparkAppState = Option(SparkAppState.COMPLETE.toString),
					sparkEndedAt = Option(new Date)
				)),
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			// Throwable 클래스는 예외 처리를 할 수 있는 최상위 클래스이다. Exception 과 Error 는 Throwable 를 상속 받는다. ( https://sjh836.tistory.com/122 )
			case t: Throwable =>

				// 4. 에러 발생 시, backup 경로 롤백
				if (backupComplete) {
					val rkBackupRollbackMoveResult = HdfsUtil.move(hdfs, rkBackupPath, rkPath)
					val nonRkBackupRollbackMoveResult = HdfsUtil.move(hdfs, nonRkBackupPath, nonRkPath)

					if (rkBackupRollbackMoveResult && nonRkBackupRollbackMoveResult) {
						logger.debug(s"$LOG_PREFIX rk/nonrk 백업 경로를 원본 경로로 이동 성공 (컴팩션 실패)")
					} else {
						throw new Exception(s"rk/nonrk 백업 경로를 원본 경로로 이동 실패 ( rkBackupRollbackMoveResult= $rkBackupRollbackMoveResult, nonRkBackupRollbackMoveResult= $nonRkBackupRollbackMoveResult )")
					}

					rollbackComplete = true
				}


				// 이력쌓기 - 실패 (FAILURE)
				val failureHist = SummaryHistory(
					spark = Option(Spark(
						sparkAppState = Option(SparkAppState.FAILURE.toString),
						sparkAppError = Option(t.getMessage),
						sparkEndedAt = Option(new Date)
					)),
				)
				this.upsertSummaryHistory(failureHist)

				t match {
					case BusinessException(_, _) =>
						logger.warn(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
					case _ =>
						logger.error(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
						throw t
				}
		} finally {
			// 5. backup 경로 지우기
			if (backupComplete && (compactionComplete || rollbackComplete)) {
				HdfsUtil.delete(hdfs, rkBackupPath)
				HdfsUtil.delete(hdfs, nonRkBackupPath)
			}

			logger.debug(s"$LOG_PREFIX $targetDate rk/nonrk 백업 경로 삭제 완료")

			NeloUtil.waitFor()
		}
	}

	def checkSilvergreySchedulesComplete(targetDate: String): Boolean = {
		if (PROFILE == "real" || PROFILE == "stage") {
			silvergreyDao.findSilvergreyScheduleNotComplete(targetDate) match {
				case Some(doc) if !doc.isEmpty => false
				case None => true
				case _ => throw BusinessException(s"리포트 연동 스케쥴 조회 에러")
			}
		} else true
	}

	def compact(inputPath: String, outputPath: String): Unit = {
		logger.debug(s"$LOG_PREFIX inputPath= $inputPath")
		logger.debug(s"$LOG_PREFIX outputPath= $outputPath")

		// 실버 그레이 로그 로딩
		val sgDf = loadParquetLog(Seq(inputPath)) match {
			case Some(df) => df
			case _ =>
				throw new Exception(s"실버 그레이 로그 로딩 실패 ( inputPath= $inputPath )")
		}

		sgDf.withColumn("_adProviderId", $"adProviderId")
			.withColumn("_publisherId", $"publisherId")
			.repartition(20, $"adProviderId", $"publisherId")
			.write
			.mode("overwrite")
			.option("dfs.blocksize", 32 * 1024 * 1024)
			.option("parquet.block.size", 32 * 1024 * 1024)
			.partitionBy("_adProviderId", "_publisherId")
			.parquet(outputPath)
	}

	def rename(path: String): Boolean = {
		val apPathNames = getSubPathNames(path)

		logger.debug(s"target apPathNames = $apPathNames")

		apPathNames.map { apPathName =>
			// apPath= /user/gfp-data/silvergrey/compaction/{rk,nonrk}/2024/01/01/_adProviderId=xxx
			val apPath = s"$path/$apPathName"

			// pub 목록 추출
			val pubPathNames = getSubPathNames(apPath)

			logger.debug(s"target pubPathNames = $pubPathNames")

			val pubRenameResult = pubPathNames.map { pubPathName =>
				// from= /_adProviderId=xxx/_publisherId=xxx
				// dest= /_adProviderId=xxx/publisherId=xxx
				val renameResult = HdfsUtil.rename(hdfs,
					s"$apPath/$pubPathName",
					s"$apPath/${pubPathName.replace("_", "")}"
				)

				// /_adProviderId=xxx/publisherId=xxx 하위 _SUCCESS 파일 생성
				HdfsUtil.create(hdfs, s"$apPath/${pubPathName.replace("_", "")}/_SUCCESS", overwrite = true)

				renameResult
			}.forall(identity)

			// from= /_adProviderId=xxx
			// dest= /adProviderId=xxx
			val apRenameResult = HdfsUtil.rename(hdfs,
				apPath,
				s"$path/${apPathName.replace("_", "")}"
			)

			pubRenameResult && apRenameResult
		}.forall(identity)
	}

	def getSubPathNames(path: String): Seq[String] = {
		val pathNames = mutable.ArrayBuffer.empty[String]

		// path = /user/gfp-data/silvergrey/{rk,nonrk}/2024/01/01/
		val fileStatusList: Array[FileStatus] = hdfs.listStatus(new Path(path))
		for (fileStat <- fileStatusList) {
			if (fileStat.isDirectory) {
				// _adProviderId=xxxxxx or _publisherId=xxxxxx
				pathNames += fileStat.getPath.getName
			}
		}

		pathNames
	}
}
