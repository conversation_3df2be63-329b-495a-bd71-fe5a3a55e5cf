package com.navercorp.gfp.biz.zircon.b

import java.util.Date
import java.util.concurrent.{ExecutorService, ForkJoinPool}
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext, Future}

import org.apache.hadoop.fs.Path
import org.bson.Document
import org.bson.types.ObjectId
import org.joda.time.DateTime

import com.navercorp.gfp.core.BaseAggregator
import com.navercorp.gfp.core.BaseEnv.{ZIRCON_B_COMPACTION_PATH, ZIRCON_B_INTERMEDIATE_PATH, ZIRCON_B_WAREHOUSE_PATH}
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory}
import com.navercorp.gfp.util.TimeUtil.SplitYmd
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil, TimeUtil}

/**
 * [ fair scheduler 적용 ]
 * 참고
 * https://towardsdatascience.com/apache-spark-sharing-fairly-between-concurrent-jobs-d1caba6e77c2
 * https://coder-question-ko.com/cq-ko-blog/78615
 *
 * spark.sparkContext.setLocalProperty("spark.scheduler.mode", "FAIR")
 * 이 같은 형식은 무슨 이유에서인지 동작하지 않아 spark-submit 시 --conf 옵션으로 해결함.
 */
object ZirconBCompactor extends BaseAggregator {
	val LOG_PREFIX = ".......... [ZB-COMPACTOR]"
	private val ONE_HUNDRED_MB = 100 * 1024 * 1024

	def main(rawArgs: Array[String]): Unit = {
		val startTime = DateTime.now()

		val args = initArgs(rawArgs)
		args.foreach(println)

		try {
			logger.debug(s"$LOG_PREFIX applicationId: ${spark.sparkContext.applicationId} args:${args.mkString(",")}")

			// 처리유형:
			//		regular: 정규처리
			//		trace_silver: 실버 재처리에 의한 지르콘 재처리
			//		trace_silvergrey: 실버그레이 재처리에 의한 지르콘 재처리
			//		manual: 수동처리
			val kind = args(0)

			// 대상
			val ymd = args(1)

			// publisherId: "*" 또는 특정 publisherId
			val pubId = args(2)

			// adProviderId: "*" 또는 특정 adProviderId
			val apId = args(3)

			// kind=trace_silver 일 때 SilverTrace._id
			val sTraceId = if (args.length > 4 && Some(args(4)).nonEmpty) args(4) else "-"

			// kind=trace_silvergrey 일 때 ZirconTrace._id
			val zTraceId = if (args.length > 5 && Some(args(5)).nonEmpty) args(5) else "-"

			// 이력쌓기 - IN_PROGRESS
			val sparkDocForInProgress = Spark(
				sparkAppId = Option(spark.sparkContext.applicationId),
				sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
				sparkStartedAt = Option(new Date)
			)

			val publisher_id = if (pubId == "*") null else new ObjectId(pubId)
			val adProvider_id = if (apId == "*") null else new ObjectId(apId)
			val silverTrace_id = if (sTraceId == "-") null else new ObjectId(sTraceId)
			val zirconTrace_id = if (zTraceId == "-") null else new ObjectId(zTraceId)

			val inProgressHist = SummaryHistory(
				datetime = Option(ymd),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(sparkDocForInProgress),
				detail = Option(Map( // 처리유형 기술
					"kind" -> kind,
					"publisher_id" -> publisher_id,
					"adProvider_id" -> adProvider_id,
					"silverTrace_id" -> silverTrace_id,
					"zirconTrace_id" -> zirconTrace_id,
				))
			)
			this.upsertSummaryHistory(inProgressHist)

			// 컴팩션
			optimize(ymd, pubId, apId)

			// 이력쌓기 - COMPLETE
			val sparkDocForComplete = Spark(
				sparkAppState = Option(SparkAppState.COMPLETE.toString),
				sparkEndedAt = Option(new Date)
			)
			val completeHist = SummaryHistory(
				spark = Option(sparkDocForComplete)
			)
			this.upsertSummaryHistory(completeHist)

			logger.debug(s"$LOG_PREFIX 생성 완료. sparkAppId=${spark.sparkContext.applicationId} summaryHistoryId=${summaryHistoryId.get}")
		} catch {
			case t: Throwable =>
				// 이력쌓기 - FAILURE
				val sparkDocForFailure = Spark(
					sparkAppState = Option(SparkAppState.FAILURE.toString),
					sparkAppError = Option(t.getMessage),
					sparkEndedAt = Option(new Date)
				)
				val failureHist = SummaryHistory(
					spark = Option(sparkDocForFailure)
				)
				this.upsertSummaryHistory(failureHist)

				logger.error(s"$LOG_PREFIX 생성 실패. sparkAppId=${spark.sparkContext.applicationId} summaryHistoryId=${summaryHistoryId.get} ${t.getMessage}")
				throw t;
		} finally {
			NeloUtil.waitFor()

			// 소요 시간 측정
			val endTime = DateTime.now()
			val elapsedTime = endTime.getMillis - startTime.getMillis
			logger.debug(s"$LOG_PREFIX 소요시간=${elapsedTime / 1000}sec\n\n")
		}
	}

	/**
	 * 동시 실행할 스레드 개수 조회
	 *
	 * @return
	 */
	private def getThreadNum(): Int = {
		val envDoc: Option[Document] = baseDao.getEnvironment("zircon-b-compaction-thread-num")
		val threadNum = envDoc.get.getInteger("value")
		logger.debug(s"$LOG_PREFIX future threadNum:$threadNum")
		threadNum
	}

	def optimize(ymd: String, pubId: String, apId: String): Unit = {
		/*
		[ Executor Context ]

			// java의 Executor를 사용하는 방식
			implicit val ec = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))

			// java의 ForkJoinPool을 사용하는 방식. 스레드 개수 지정 없음
			implicit val ec = ExecutionContext.fromExecutorService(new scala.concurrent.forkjoin.ForkJoinPool())

			// java의 ForkJoinPool을 사용하는 방식. 스레드 개수 지정 가능
			val forkJoinPool: ExecutorService = new ForkJoinPool(16)
			implicit val ec: ExecutionContext = ExecutionContext.fromExecutorService(forkJoinPool)

			참고
				- 스칼라 강좌 (37) 동시성을 위한 ExecutorContext (https://hamait.tistory.com/768)
				- SCALA BOOK > SCALA FUTURES: https://docs.scala-lang.org/overviews/scala-book/futures.html#inner-main
				- FUTURES > FUTURES AND PROMISES: https://docs.scala-lang.org/overviews/core/futures.html#inner-main
		 */

		// 동시 처리 개수
		val threadNum = getThreadNum()
		val forkJoinPool: ExecutorService = new ForkJoinPool(threadNum)
		implicit val ec: ExecutionContext = ExecutionContext.fromExecutorService(forkJoinPool)

		var futures = Vector[Future[Option[Boolean]]]()
		var renameListForNonComp = Vector[(String, String)]()
		val split = TimeUtil.getSplitYmd(ymd)

		val asIsWarehouseApPaths = getPaths(ZIRCON_B_WAREHOUSE_PATH, split, pubId, apId, false)
		val intermediateApPaths = getPaths(ZIRCON_B_INTERMEDIATE_PATH, split, pubId, apId, true)

		/*
			컴팩션 & 리네임 퓨처 생성
		 */
		for (intermediateApPath <- intermediateApPaths) {
			// 해당 경로 아래 파일의 최대 크기가 100 MB 미만인 경우만 컴팩션
			if (isLessThanOneHundredMbOfLargestFileLength(intermediateApPath)) {
				/*
					파티션 개수 및 경로 설정
					intermediateApPath = "/user/gfp-data/zircon/b/intermediate/{yyyy}/{mm}/{dd}/{hour}/_publisherId=xx/_adProviderId=yy"
					compactionApPath   = "/user/gfp-data/zircon/b/compaction/{yyyy}/{mm}/{dd}/{hour}/_publisherId=xx/_adProviderId=yy"
					warehouseApPath    = "/user/gfp-data/zircon/b/warehouse/{yyyy}/{mm}/{dd}/{hour}/_publisherId=xx/_adProviderId=yy"
				 */
				val numPartition = getNumPartitions(intermediateApPath)
				val compactionApPath = transformPath(intermediateApPath, ZIRCON_B_COMPACTION_PATH)
				val warehouseApPath = transformPath(compactionApPath, ZIRCON_B_WAREHOUSE_PATH)
				logger.debug(
					s"""$LOG_PREFIX 컴팩션 대상
					   |	numPartition:$numPartition
					   |	intermediatePath: $intermediateApPath
					   |	compactionPath:   $compactionApPath
					   |	warehousePath:    $warehouseApPath
						""".stripMargin)

				// AP별 컴팩션 퓨처 만들기
				// ZBGFP와는 다르게 켬팩션 & 리네임을 퓨처 안에서 함
				val future = Future {
					try {
						// 컴팩트
						compact(intermediateApPath, numPartition, compactionApPath)

						// 리네임. compaction -> warehouse
						rename(compactionApPath, warehouseApPath)

						/*
							컴팩션 경로의 삭제에 대하여 ...
							- compaction 경로에서 리네임을 하면 말단 AP 경로가 warehouse로 이동되나 부모 경로는 그대로 남는다.
							- ZB spark는 PUB:AP의 관계가 다양하기 때문에 다른 spark에 의해 부모 경로가 사용중일 수 있어서
							  부모 경로를 삭제하지 않는다.(빈 통으로 경로만 존재할 수 있음)
						 */

						// warehouse에 _COMPACTION_SUCCESS 파일 생성
						HdfsUtil.create(hdfs, s"$warehouseApPath/_COMPACTION_SUCCESS")

						/*
							- intermediate ap 경로 삭제
							- 상위 pub 경로는 그대로 남아 있음
								- 동일 매체에 서로 다른 AP가 동시에 수행될 가능성이 있으므로 진행중인 데이터 유지를 위해 상위 경로는 삭제하지 않고 남겨둠
								- 지르콘 보관기간이 지난 경로에 대해서 일괄 삭제하는 배치에 의해 삭제됨
						 */
						HdfsUtil.delete(hdfs, intermediateApPath)

						// 컴팩션 성공 시, true 리턴
						Option(true)
					} catch {
						case t: Throwable =>
							logger.error(s"$LOG_PREFIX ${t.getMessage}", t)
							Option(false) // 컴팩션 실패 시, false 리턴
					}
				}
				futures = futures :+ future
			} else {
				// 컴팩션 비대상 리네임 목록 만들기. 컴팩션은 하지 않지만 리네임은 해야 함.
				val warehouseApPath = transformPath(intermediateApPath, ZIRCON_B_WAREHOUSE_PATH)
				//				logger.debug(
				//					s"""$LOG_PREFIX 컴팩션 비대상
				//					   |	intermediatePath: $intermediateApPath
				//					   |	warehousePath:    $warehouseApPath
				//								""".stripMargin)
				renameListForNonComp = renameListForNonComp :+ (intermediateApPath, warehouseApPath)
			}
		}

		/*
			컴팩션 & 리네임 퓨처 결과 확인
		 */
		if (futures.nonEmpty) {
			logger.debug(s"$LOG_PREFIX future cnt = ${futures.length}")

			// 퍼블리셔별 컴팩션 결과 모으기
			val reduced = Future.reduceLeft(futures) { case (accu, value) =>
				Option(accu.getOrElse(false) && value.getOrElse(false))
			}

			// 모든 future 가 끝날 때까지 기다렸다가 결과 받기
			val result = Await.result(reduced, Duration.Inf)

			// 컴팩션이 모두 성공적으로 끝났는지 확인
			result match {
				case Some(false) =>
					throw new Exception(s"$ymd 컴팩션 실패")
				case _ =>
					logger.debug(s"$LOG_PREFIX $ymd 컴팩션 성공")
			}
		} else {
			logger.debug(s"$LOG_PREFIX $ymd 컴팩션 대상 없음")
		}

		/*
			컴팩션 비대상 리네임. intermediate -> warehouse
		 */
		if (renameListForNonComp.nonEmpty) {
			logger.info(s"$LOG_PREFIX 컴팩션 비대상 경로 리네임 시작 ...")
			renameListForNonComp.foreach(item => {
				// 리네임. intermediate -> warehouse
				val intermediateApPath = item._1
				val warehouseApPath = item._2
				rename(intermediateApPath, warehouseApPath)

				// warehouse에 _COMPACTION_SUCCESS 파일 생성
				HdfsUtil.create(hdfs, s"$warehouseApPath/_COMPACTION_SUCCESS")
			})
			logger.info(s"$LOG_PREFIX 컴팩션 비대상 경로 리네임 성공")
		}

		/*
			- 리네임 후 존재할 필요 없는 경로 삭제
			- 이전에 존재했지만 새로 생성했을 때는 존재하지 않는 경로를 삭제하는 것
			- 컴팩션 전에 꼭 적재가 먼저 이루어져야 함 !!
				- 그렇지 않으면 warehouse에 있는 모든 데이터가 out of date로 판단되어 삭제됨
		 */
		val toBeWarehouseApPaths = intermediateApPaths.map(p => transformPath(p, ZIRCON_B_WAREHOUSE_PATH))
		deleteUnworthyPaths(asIsWarehouseApPaths, toBeWarehouseApPaths)
	}

	/**
	 * 파라미터로 전달받은 rootPath의 하위 경로 얻기
	 *
	 * @param split
	 * @param pubId
	 * @param apId
	 * @return
	 */
	private def getPaths(rootPath: String, split: SplitYmd, pubId: String, apId: String, usingSuccess: Boolean = true): Vector[String] = {
		var apPaths = Vector[String]()

		val pathRoot = s"$rootPath/${split.yyyy}/${split.mm}/${split.dd}"
		HdfsUtil.list(hdfs, pathRoot, true).foreach(hourPath => {
			if (pubId.equals("*")) {
				HdfsUtil.list(hdfs, s"$hourPath", true).foreach(pubPath => {
					val apPath = s"$pubPath/_adProviderId=$apId"
					if (usingSuccess) {
						if (HdfsUtil.exists(hdfs, s"$apPath/_SUCCESS")) apPaths = apPaths :+ apPath
					} else {
						if (HdfsUtil.exists(hdfs, s"$apPath")) apPaths = apPaths :+ apPath
					}
				})
			} else {
				val pubPath = s"$hourPath/_publisherId=$pubId"
				if (HdfsUtil.exists(hdfs, pubPath)) {
					if (apId.equals("*")) {
						HdfsUtil.list(hdfs, s"$pubPath", true).foreach(apPath => {
							if (usingSuccess) {
								if (HdfsUtil.exists(hdfs, s"$apPath/_SUCCESS")) apPaths = apPaths :+ apPath
							} else {
								if (HdfsUtil.exists(hdfs, s"$apPath")) apPaths = apPaths :+ apPath
							}
						})
					} else {
						val apPath = s"$pubPath/_adProviderId=$apId"
						if (usingSuccess) {
							if (HdfsUtil.exists(hdfs, s"$apPath/_SUCCESS")) apPaths = apPaths :+ apPath
						} else {
							if (HdfsUtil.exists(hdfs, s"$apPath")) apPaths = apPaths :+ apPath
						}
					}
				}
			}
		})

		apPaths
	}

	/**
	 * 해당 경로 아래 파일의 최대 크기가 100 MB 미만인지
	 *
	 * @param path
	 * @return
	 */
	def isLessThanOneHundredMbOfLargestFileLength(path: String): Boolean = {
		val fsPath = new Path(path)
		var maxLength = 0L
		val listStatus = hdfs.listStatus(fsPath)
		for (status <- listStatus) {
			maxLength = Math.max(maxLength, status.getLen)
		}
		if (maxLength < ONE_HUNDRED_MB) {
			true
		} else {
			false
		}
	}

	/**
	 * 퍼블리셔 경로 아래에 있는 파케이 파일의 총 사이즈에 따른 파티션 개수 구하기
	 * 100 MB 단위로 잘랐을 때의 파티션 개수
	 *
	 * @param path
	 * @return
	 */
	def getNumPartitions(path: String): Int = {
		val fsPath = new Path(path)

		// Calculates disk usage without pay attention to replication factor.
		// Result will be the same with hadopp fs -du /hdfs/path/to/directory
		val totalFileSize = hdfs.getContentSummary(fsPath).getLength
		var numPartition = totalFileSize / ONE_HUNDRED_MB
		val rest = totalFileSize % ONE_HUNDRED_MB
		if (rest > 0) {
			numPartition += 1
		}
		if (numPartition < 1) {
			numPartition = 1
		}
		numPartition.toInt
	}

	/**
	 * @param input
	 * "/user/gfp-data/zircon/b/intermediate/2024/06/01/00/_publisherId=xx/_adProviderId=yy"
	 * @param targetPathPrefix
	 * 바꿀 경로의 접두어
	 * "/user/gfp-data/zircon/b/compaction
	 * @return
	 * "/user/gfp-data/zircon/b/compaction/2024/06/01/00/_publisherId=xx/_adProviderId=yy"
	 */
	def transformPath(input: String, targetPathPrefix: String): String = {
		// 정규식을 사용하여 필요한 부분 추출
		val pattern = """.*/(\d{4}/\d{2}/\d{2}/\d{2})/_publisherId=([^/]+)/_adProviderId=([^/]+)""".r

		input match {
			case pattern(ymdh, pubId, apId) =>
				// warehouse 경로로 치환. 시간 정보를 date 아래에 붙임
				s"$targetPathPrefix/$ymdh/_publisherId=$pubId/_adProviderId=$apId"
			case _ =>
				// 패턴이 맞지 않는 경우 원래 문자열 반환
				input
		}
	}

	/**
	 * 컴팩트(리파티션)
	 *
	 * @param inputPath
	 * @param numPartition
	 * @param outputPath
	 */
	def compact(inputPath: String, numPartition: Int, outputPath: String): Unit = {
		val df = loadLog(Vector(inputPath))
		df
			.repartition(numPartition)
			.write
			.mode("overwrite")
			.parquet(outputPath)
	}

	/**
	 * 재처리 후 존재하지 않는 경로 삭제
	 *
	 * @param asIsPaths
	 * @param toBePaths
	 */
	private def deleteUnworthyPaths(asIsPaths: Vector[String], toBePaths: Vector[String]) = {
		val unworthyPaths = asIsPaths.diff(toBePaths)
		for (path <- unworthyPaths) {
			logger.info(s"$LOG_PREFIX Out Of Date 경로 삭제 $path")
			HdfsUtil.delete(hdfs, path)
		}
	}
}
