/**
 * Apache Commons Lang JAVA Doc
 * https://commons.apache.org/proper/commons-lang/apidocs/index.html
 * <p>
 * 웍스 알림 연동: https://wiki.navercorp.com/pages/viewpage.action?pageId=525441819
 */
package com.navercorp.gfp.c3

import com.mongodb.spark.config.ReadConfig
import com.mongodb.spark.sql.toMongoDataFrameReaderFunctions
import com.typesafe.config.Config
import org.apache.spark.sql.SparkSession

import com.navercorp.gfp.core.{BaseDao, BaseEnv}
import com.navercorp.gfp.core.conf.Conf
import com.navercorp.gfp.core.database.{Database, DatabaseInitializer}


object Example1 extends DatabaseInitializer {
	val baseDao = new BaseDao()

	def main(rawArgs: Array[String]): Unit = {
		val conf: Config = Conf.getConf()
        val namespace = conf.getString("hadoop.nameservice.bizcloud.host")
        println(s"........................................namespace=${namespace}")

		val spark = SparkSession.builder
			.getOrCreate()

		import spark.implicits._

        rawArgs.foreach(a => println(s"rawArg = $a"))
        initDB(rawArgs)

        // ----------- 1. MongoDB > AdProviderAdUnitRkDaily 확인
		val readConfig = ReadConfig(Map(
			"spark.mongodb.input.uri" -> Database.getDecryptedDatabaseUri,
			"spark.mongodb.input.database" -> Database.getDatabase().getName,
			"spark.mongodb.input.collection" -> "AdProviderAdUnitRkDaily",
			"spark.mongodb.input.readPreference.name" -> "secondaryPreferred",
			"spark.mongodb.input.batchSize" -> "2000",
			"spark.mongodb.input.partitionerOptions.shardKey" -> "{publisher_id:1, date:1, adUnitId:1, adProvider_id:1}"))

		val df = spark.read
			.mongo(readConfig)
			.filter("date == '20230701'")
			.select(
				$"publisher_id.oid".as("publisherId"),
				$"adProvider_id.oid".as("adProviderId"),
				$"adUnitId",
				$"netRevenueKRW",
				$"netRevenueUSD",
			)
        println(s"................................. MongoDB > AdProviderAdUnitRkDaily 확인")
		df.show(5, false)


        // ----------- 2. MongoDB > Environments 확인
		val envDoc1 = baseDao.getEnvironment("silver-log-recent-accumulation-ymdh")
		val recentSilverYmdh = if (envDoc1.isEmpty) "**********" else envDoc1.get.getString("value")
		println(s"................................. MongoDB > Environments 확인. recentSilverYmdh:$recentSilverYmdh")


        // ----------- 3. c3 hdfs로부터 df 생성
        val path = "hdfs://pg07/user/gfp-data/gfp-silver/er-ssp-client/20230501-00/publisherId=5b8f695b422d910019805eff"
        val df2 = spark.read
            .format("parquet")
            .option("recursiveFileLookup", "true")
            .load(path)

        println(s"................................. c3 hdfs path 확인:$path")
        df2.show(false)


        val writeOptions = BaseEnv.mdbDefaultWriteOptions
        println(s"......................... writeOptions=$writeOptions")
	}
}
