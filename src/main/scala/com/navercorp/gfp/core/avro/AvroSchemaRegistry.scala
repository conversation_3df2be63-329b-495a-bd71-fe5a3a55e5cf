package com.navercorp.gfp.core.avro

import com.navercorp.gfp.core.conf.Conf
import com.typesafe.config.Config
import org.apache.avro.Schema
import org.apache.spark.sql.avro.SchemaConverters
import org.apache.spark.sql.types.StructType

import scala.io.Source

/**
 * 최신 버전의 Avro Schema 조회
 */
object AvroSchemaRegistry {
    val conf: Config = Conf.getConf()

    def getServerSchema() = {
        getSchema("da_ssp_server")
    }

    def getClientSchema() = {
        getSchema("da_ssp_client")
    }

    private def getSchema(schemaName: String) = {
        val schemaFromRegistry = Source
            .fromURL(s"${conf.getString("avro.schema.registry")}/subjects/$schemaName/versions/latest/schema")
            .mkString
        val structType = SchemaConverters
            .toSqlType(new Schema.Parser().parse(schemaFromRegistry))
            .dataType.asInstanceOf[StructType]

        structType
    }

    /*def getSchema(): StructType = {
        val fields = Array[StructField](
            // dimensions
            StructField("date", DataTypes.StringType, false, Metadata.empty),
            StructField("publisherId", DataTypes.StringType, false, Metadata.empty),
            StructField("serviceId", DataTypes.StringType, false, Metadata.empty),
            StructField("adUnitId", DataTypes.StringType, false, Metadata.empty),
            StructField("adProviderId", DataTypes.StringType, false, Metadata.empty),
            StructField("age", DataTypes.IntegerType, false, Metadata.empty),
            StructField("gender", DataTypes.StringType, false, Metadata.empty),

            // metrics
            StructField("adProviderRequests", DataTypes.LongType, false, Metadata.empty), // 광고공급자 호출수
            StructField("adProviderResponses", DataTypes.LongType, false, Metadata.empty), // 광고공급자 응답수
            StructField("adProviderResponseRate", DataTypes.DoubleType, false, Metadata.empty), // 광고공급자 응답률
            StructField("filledRequests", DataTypes.LongType, false, Metadata.empty), // 선출수
            StructField("adProviderWinRate", DataTypes.LongType, false, Metadata.empty), // 선출기여도
            StructField("matchedRate", DataTypes.LongType, false, Metadata.empty), // 응답대비 선출률
            StructField("impressions", DataTypes.LongType, false, Metadata.empty), // 노출수
            StructField("measurableRate", DataTypes.LongType, false, Metadata.empty), // 선출대비 노출률
            StructField("krwBidPriceSum", DataTypes.DoubleType, false, Metadata.empty), // Mille 추정수익
            StructField("estimatedRevenue", DataTypes.DoubleType, false, Metadata.empty), // 추정수익
            StructField("ecpm", DataTypes.DoubleType, false, Metadata.empty) // eCPM
        )

        val schema = new StructType(fields)

        schema
    }*/
}
