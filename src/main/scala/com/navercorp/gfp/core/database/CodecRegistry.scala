package com.navercorp.gfp.core.database

import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros

import com.navercorp.gfp.biz.abuse.AdpostAbuseReportConfig
import com.navercorp.gfp.biz.discrepancy.{DiscrepancyReportConfig, DiscrepancyReportConfigMetric}
import com.navercorp.gfp.biz.revenuesharing.RevenueSharingGfpStatsSchedule
import com.navercorp.gfp.biz.revenuesharing3.{<PERSON>p<PERSON><PERSON>, RevenueSharingReport, SilvergreyDetail}
import com.navercorp.gfp.biz.schedule.{Ext, PERF, RS, Schedule}
import com.navercorp.gfp.core.summaryhistory.{Spark, SummaryHistory, SummaryHistoryDetail}
import com.navercorp.gfp.meta.adprovider.{AdProvider, EstimatedReportType, ReportApi}
import com.navercorp.gfp.meta.adproviderinfo.AdProviderInfo
import com.navercorp.gfp.meta.customreport.{CustomReport, CustomReportFilter}
import com.navercorp.gfp.meta.exchangerate.ExchangeRateAverages
import com.navercorp.gfp.meta.gfpfeerate.GfpFeeRate
import com.navercorp.gfp.meta.publisher.Publisher
import com.navercorp.gfp.meta.summarytargetcountry.SummaryTargetCountry

object CodecRegistry {
	// https://github.com/mongodb/mongo-scala-driver/blob/master/docs/reference/content/bson/macros.md
	val codecRegistry = fromRegistries(
		fromProviders(
			// 메타
			Macros.createCodecProvider[Publisher](),
			Macros.createCodecProvider[ReportApi](),
			Macros.createCodecProvider[EstimatedReportType](),
			Macros.createCodecProvider[AdProvider](),
			Macros.createCodecProvider[AdProviderInfo](),

			Macros.createCodecProvider[ExchangeRateAverages](),
			Macros.createCodecProvider[GfpFeeRate](),

			// AP 불일치 리포트
			Macros.createCodecProvider[DiscrepancyReportConfig](),
			Macros.createCodecProvider[DiscrepancyReportConfigMetric](),

			// 기본
			Macros.createCodecProvider[Spark](),
			Macros.createCodecProvider[SummaryHistory](),
			Macros.createCodecProvider[SummaryHistoryDetail](),
			Macros.createCodecProvider[SummaryTargetCountry](),

			// 리포트 스케줄
			Macros.createCodecProvider[RS](),
			Macros.createCodecProvider[PERF](),
			Macros.createCodecProvider[Ext](),
			Macros.createCodecProvider[Schedule](),

			// 수익쉐어 리포트
			Macros.createCodecProvider[RevenueSharingGfpStatsSchedule](),

			// 수익쉐어 리포트 3
			Macros.createCodecProvider[ApKey](),
			Macros.createCodecProvider[Schedule](),
			Macros.createCodecProvider[SilvergreyDetail](),
			Macros.createCodecProvider[RevenueSharingReport](),

			// 어뷰징 리포트
			Macros.createCodecProvider[AdpostAbuseReportConfig](),

			// 커스텀 리포트
			Macros.createCodecProvider[CustomReportFilter](),
			Macros.createCodecProvider[CustomReport](),
		), DEFAULT_CODEC_REGISTRY
	)
}
