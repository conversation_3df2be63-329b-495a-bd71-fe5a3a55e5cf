package com.navercorp.gfp.core.database

trait DatabaseInitializer {
    var uri: Option[String] = None

    //    *******************************************************************************************************************************************************
    //    ***************************************************************************************************************************

    /**
     * DB 초기화
     * - args[0]이 mongodb connection uri scheme일 경우
     *      - args[0]의 값을 Database.decryptedDatabaseUri로 설정
     *      - DB 인스턴스 생성
     *      - args[1:N] 을 반환함
     *
     * @param args
        >> test
        "--dataDbUri--*******************************************************************************************************************************************************"
        "--cmsDbUri--********************************************************************************************************************************************"
        "64c33145b3d34e8b8dbae9ca"
        "20230501"

        >> stage
        "--dataDbUri--***************************************************************************************************************************************************"
        "--cmsDbUri--*************************************************************************************************************"
        "64cb57f38ede8e39623f9f49"
        "20230701"

        >> real
        "--dataDbUri--***************************************************************************************************************************"
        "--cmsDbUri--**************************************************************************************************************************"
        "64cb57f38ede8e39623f9f49"
        "20230701"
     * @return
     */
    def initDB(args: Array[String]) = {
//        // Data MongoDB 인스턴스 생성
//        var uri = getUri("--dataDbUri--", args)
//        Database.init(uri.get)
//
//        // CMS MongoDB 인스턴스 생성
//        uri = getUri("--cmsDbUri--", args)
//        CmsDatabase.init(uri.get)
    }

    private def getUri(key: String, args: Array[String]): Option[String] = {
        val arg = args.find(x => x.startsWith(key))
        if (arg.isDefined) {
            val a = arg.get
            val uri = a.slice(key.length, a.length)
            println(s".......................... key:${key} uri:$uri")
            Option(uri)
        } else {
            println(s".......................... key:${key} uri:None")
            None
        }
    }
}
