package com.navercorp.gfp.meta.adprovider

import org.bson.types.ObjectId

case class ReportApi(rkUse: Option[Int], `type`: Option[String])

case class EstimatedReportType(banner: Option[String] = Some("FILL"), video: Option[String] = Some("IMP"), native: Option[String] = Some("IMP"))

case class AdProvider(_id: ObjectId,
					  adProviderType: Option[String],
					  currency: Option[String],
					  timezone: Option[String],
					  reportApi: Option[ReportApi],
					  estimatedReportType: Option[EstimatedReportType],
					  //					  distributeTypeForImp: String, distributeTypeForRvn: String
					 )
