package com.navercorp.gfp.meta.summarytargetcountry

import com.mongodb.client.{FindIterable, MongoCollection}
import com.navercorp.gfp.core.database.Database

class SummaryTargetCountryDao {
	def getCountries: FindIterable[SummaryTargetCountry] = {
		val collection: MongoCollection[SummaryTargetCountry] = Database.getDatabase
			.getCollection("SummaryTargetCountry", classOf[SummaryTargetCountry])

		collection.find()
	}
}
