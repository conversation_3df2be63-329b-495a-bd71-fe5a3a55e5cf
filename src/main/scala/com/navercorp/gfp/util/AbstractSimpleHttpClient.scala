package com.navercorp.gfp.util

import org.apache.http.impl.client.{CloseableHttpClient, HttpClients}

abstract class AbstractSimpleHttpClient[T](httpClient: => CloseableHttpClient) {

  def exceptionHandler(e: Throwable): T

  def withHttpClient(fn: CloseableHttpClient => T): T = {
    try {
      fn(httpClient)
    } catch {
      case e: Throwable => exceptionHandler(e)
    } finally {
      if (httpClient != null) {
        httpClient.close()
      }
    }
  }
}

object DefaultCloseableHttpClient extends (() => CloseableHttpClient) {
  def apply(): CloseableHttpClient = {
    HttpClients.createDefault
  }
}

object SimpleHttpClientStatus {
  val STATUS_SUCCESS = 200
  val STATUS_ERROR = 500
}

case class SimpleHttpClient() extends AbstractSimpleHttpClient[Int](DefaultCloseableHttpClient()) {
  override def exceptionHandler(e: Throwable): Int = {
    e.printStackTrace()
    500
  }
}
