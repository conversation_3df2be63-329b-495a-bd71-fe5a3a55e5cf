'use strict';

import mongoose from 'mongoose';

let Country = {};

let createSchemaAndModel = (conn) => {
    let scheme = mongoose.Schema({
		code : { type: String, trim: true },
		name : { type: String, trim: true },
	}, {
		// _id: false,
		id: false,
		versionKey: false,
	});

    scheme.set('toJSON',{ getters : true , virtuals : true });

	Country = conn.model('Country', scheme, 'Countries');

    return scheme;
};

export default createSchemaAndModel;

export { Country };
