'use strict';

import mongoose from 'mongoose';

let Job = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema({
		datetime: String,
		type: String,
		retryCnt: Number,
		running: Number,
		manual: Number,
		createdAt: Date,
		modifiedAt: Date
	},{	versionKey: false });

	Job = conn.model('Job', scheme, 'Jobs');

	return scheme;
};

export default createSchemaAndModel;

export { Job };
