'use strict';

import mongoose from 'mongoose';

let MonitoringProfitRate = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema({
		date: String,
		adProvider_id: { type: mongoose.Schema.Types.ObjectId, required: true },
		publisher_id: { type: mongoose.Schema.Types.ObjectId, required: true },

		krwSales: Number,
		krwProfit: Number,
		krwProfitRate: Number,
		krwProfitRateDeviation: Number,

		usdSales: Number,
		usdProfit: Number,
		usdProfitRate: Number,
		usdProfitRateDeviation: Number,

		createdAt: {
			type: Date,
			default: Date.now
		},
		expiredAt: {
			type: Date,
			default: Date.now
		}
	}, { versionKey: false });

	MonitoringProfitRate = conn.model('MonitoringProfitRate', scheme, 'MonitoringProfitRate');

	return scheme;
};

export default createSchemaAndModel;

export { MonitoringProfitRate };
