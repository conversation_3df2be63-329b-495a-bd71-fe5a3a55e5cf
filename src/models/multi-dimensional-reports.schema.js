'use strict';

import mongoose from 'mongoose';

let MultiDimensionalReport = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema({
		cmsType: String,

		publisher_id: {
			_id: false,
			type: mongoose.Schema.Types.ObjectId,
			ref: 'Publisher'
		},
		publisherName: String,

		reportType: String,

		// 이름
		name: {
			type: String,
			trim: true,
			required:true,
			maxlength:[50, 'name is longer than the maximum allowed length (50)'],
		},

		// 설명
		description: {
			type: String,
			trim: true,
			maxlength:[50, 'description is longer than the maximum allowed length (50)'],
		},

		language: String,

		// 광고공급자 기준 타임존 사용 여부
		adProviderTimezoneUse: String,

		// 타임존
		timezone: {
			type: String,
			trim: true,
		},

		// 데이터 시작일
		beginYmd: {
			type: String,
			trim: true,
		},

		// 데이터 종료일
		endYmd: {
			type: String,
			trim: true,
		},

		conditionForUi: {
			type: {
				filters: [{
					type: {
						filterGroupName: String,

						name: String,
						operationType: String,
						operation: String,

						keyGroup_id: String,
						keyGroupName: String,
						key: String,

						values: [String],
					}
				}],
				generalDimensions: [{
					type: {
						name: String,
						label: String
					}
				}],
				keyDimensions: [{
					type: {
						name: String,
						label: String
					}
				}],
				metrics: [{
					type: {
						name: String,
						label: String
					}
				}]
			}
		},

		conditionForSpark: {
			type: {
				filters: {
					type: [{
						_id: false,
						name: String,
						operation: String,
						values: [String],
						filters: []
					}]
				},
				dimensions: [String],
				metrics: [String],
				keys: [String]
			}
		},

		histories: {
			type: [{
				begunAt: { type: Date },
				endedAt: { type: Date },
				fileName: { type: String },
				filePath: { type: String },
				state: {
					type: String,
					enum: [
						'STANDBY',		// 리포트 생성 대기
						'START',		// 리포트 시작
						'ONGOING',		// 리포트 생성 중
						'COMPLETE',		// 리포트 생성 완료
						'FAILURE'		// 리포트 생성 실패
					]
				},
				error: String,
				sparkling: {
					type: {
						_id: false,
						sparkAppState: {
							type: String,
							enum: [
								'STANDBY',		// spark app 실행을 위한 대기 상태
								'ONGOING',		// RevenueSharingAggregator.java에서 read()~save()까지의 상태
								'COMPLETE',		// spark app 종료
								'FAILURE'		// spark app 내부 로직 실패
							]
						},
						// 스파크
						sparkSubmitState: {
							type: String,
							enum: [
								'STANDBY',		// spark-submit을 하기 위한 대기 상태
								'ACCEPTED',		// spark-submit 후 cluster에 spark app을 submit한 상태
								'RUNNING',		// cluster에서 spark app이 돌아가고 있음
								'FINISHED'		// spark-submit 종료
							]
						},
						trackingUrl: String,
						error: { type: String },
						begunAt: { type: Date },
						endedAt: { type: Date },
						filePath: { type: String },
						outputPath: { type: String },
						query: { type: String },
						dataPresence: Number
					}
				}
			}]
		},

		creator_id: {
			_id: false,
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User'
		},
		creatorName : String,
		creatorEmail : String,
		createdAt: {
			type: Date,
			default: Date.now
		},

		modifiedAt: {
			type: Date,
			default: Date.now
		}
	},{	versionKey: false });

	MultiDimensionalReport = conn.model('MultiDimensionalReport', scheme, 'MultiDimensionalReports');

	return scheme;
};

export default createSchemaAndModel;

export { MultiDimensionalReport };
