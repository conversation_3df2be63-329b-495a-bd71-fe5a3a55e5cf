'use strict';

import mongoose from 'mongoose';

let Place = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema({
		// Place 명
		name: {
			type: String,
			trim: true,
			required: [true, 'place name is required']
		},

		// Place 설명
		description	: {
			type: String,
			trim: true,
			required: [true, 'place description is required']
		},

		// OS : IOS, AOS, WEB
		channelType: {
			type: String,
			uppercase: true,
			trim: true,
			required: [true, 'place channel type is required']
		},

		// 지원상품(광고형식) : BANNER, NATIVE, VAST
		creativeType: {
			type: String,
			uppercase: true,
			trim: true,
			required: [true, 'place creative type is required']
		},

		adProviderInfo_id: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'AdProviderInfo'
		},

		placeInfos: [],

		// 등록
		creator_id: String,
		createdAt: {
			type: Date,
			default: Date.now
		},

		// 수정
		modifier_id: String,
		modifiedAt: {
			type: Date,
			default: Date.now
		},

		status: {
			type: String, uppercase: true, required:true, enum:['ON', 'OFF'],
			default: 'ON'
		},

		placeKey: {
			type: String
		}
	}, {versionKey: false});

	Place = conn.model('Place', scheme, 'AdProviderPlaces');

	return scheme;
};

export default createSchemaAndModel

export { Place };
