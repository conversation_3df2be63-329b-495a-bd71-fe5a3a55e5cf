'use strict';


import logger from '../utils/logger.util';

import * as adProviderSummaryScheduleController from '../controllers/ad-provider-summary-schedule.controller';

module.exports = router => {
	logger.info('[ad-provider-summary-schedule.router] 호출됨');

	// 일배치 - 매일 자정
	// Airflow/Spark - 광고공급자 집계 스케쥴 초기화
	router.get('/batch/day/adprovider/summary/schedule', adProviderSummaryScheduleController.processAdProviderSummarySchedule);

	// 수동
	// 광고공급자 집계 스케쥴 상태를 WAIT 으로 변경 (By Id)
	router.get('/batch/day/adprovider/summary/schedule/state/wait', adProviderSummaryScheduleController.updateAdProviderScheduleStateWaitById);
};
