'use strict';

import * as countrycode from 'country-code-lookup';
import {Country} from "../models/country.schema";

/**
 * country-code-lookup 라이브러리를 이용해 국가 정보 초기화해서 Country 컬렉션에 넣기
 * @param ctx
 * @returns {Promise<void>}
 */
module.exports.initCountries = async (ctx) => {
	/*
	{
		"continent": "Asia",
		"region": "East Asia",
		"country": "South Korea",
		"capital": "Seoul",
		"fips": "KS",
		"iso2": "KR",
		"iso3": "KOR",
		"isoNo": "410",
		"internet": "KR"
	},
	 */
	await Promise.all(countrycode.countries.map(async item => {
		await Country.findOneAndUpdate(
			{code: item.iso2},
			{'$set': {code: item.iso2, name: item.country}},
			{upsert: true});
	}));
};
