'use strict';

import moment from 'moment';
import path from 'path';

import config from '../../config/config';

import * as logger from '../../utils/logger.util';

import { generateReportFile, getDiscrepancyReport, sendReport } from './discrepancy-report-api.service';

const LOGGER = 'discrepancy_report_api';

/*
	NATIVO 불일치 리포트 정보
	- 디멘전
		- date
	- 메트릭
		- sspEstimatedImpressions
		- sspEstimatedRevenue
		- sspEstimatedCpm (discrepancy-report-config 엔 정의되지 않으나 이곳에서 추가하여 집계)
*/

/**
 * [PROCESS 3:: TASK 2] processDiscrepancyReportApi : NATIVO 불일치 리포트 API 연동
 * 	SUBTASK 1. _getDiscrepancyReport : 리포트 집계
 * 	SUBTASK 2. _generateReportFile : 불일치 리포트 파일 생성하기
 * 	SUBTASK 3. _sendReport : 리포트 메일 전송
 *
 * @RequestMapping(value='/batch/discrepancy/report/api/NATIVO')
 *
 * @param {Object} { _id, ymd, adProvider_ids, publisher_ids, startDate, endDate }
 * @param {Object} { finalDimensions, metrics, reportInfos: {file, mail} }
 * @return {String} filePath
 */
module.exports.processDiscrepancyReportApi = async ({
														_id,
														ymd,
														adProvider_ids,
														publisher_ids,
														startDate,
														endDate
													}, { finalDimensions, metrics, reportInfos: { file, mail } }) => {
	logger.debug(LOGGER, `[nativo.service :: processDiscrepancyReportApi] ( schedule_id= ${_id} )`);

	// SUBTASK 0. 메트릭 및 디멘전 세팅 (헤더)
	const finalMetrics = metrics.map(met => met.name);
	finalMetrics.push('sspEstimatedCpm');
	const headerMappingList = [
		{ id: 'date', title: 'date' },
		{ id: 'sspEstimatedImpressions', title: 'sspEstimatedImpressions' },
		{ id: 'sspEstimatedRevenue', title: 'sspEstimatedRevenue' },
		{ id: 'sspEstimatedCpm', title: 'sspEstimatedCpm' }
	];

	// SUBTASK 1. getDiscrepancyReport : DiscrepancyDaily 데이터로부터 리포트 집계
	// [ { date: "20240728", sspEstimatedImpressions: 31, sspEstimatedRevenue: 9.30, sspEstimatedCpm: } ]
	const reportData = await getDiscrepancyReport({
		_id, adProvider_ids, publisher_ids, dimensions: finalDimensions, metrics: finalMetrics, startDate, endDate
	});

	// SUBTASK 2. generateReportFile : 불일치 리포트 파일 생성하기
	const baseDir = path.join(config.owfs_root, file.path, moment(ymd).format('YYYY'), moment(ymd).format('MM'));
	const filePath = await generateReportFile({
		_id, startDate, endDate, baseDir, fileNameFormat: file.name, pwd: file.pwd, reportData
	}, headerMappingList);

	// SUBTASK 3. sendReport : 리포트 메일 전송
	await sendReport({ _id, filePath, addresses: mail.addresses, subject: mail.subject });

	return filePath;
};
