'use strict';


import fs from 'fs';
import _ from 'lodash';
import csv from 'csv-parser';
import mongoose from 'mongoose';
import { Readable } from 'stream';

import config from '../../config/config';
import cLogger from '../../common/logger';
import mongooseForChild from '../../common/mongooseForChildProcess';

import * as logger from '../../utils/logger.util';

import { BusinessError } from '../../common/error';

import { ExtendedKeyValue } from '../../models/extended-key-values.schema';

const ObjectId = mongoose.Types.ObjectId;

const KVEXT_LOGGER = 'kvext';


/**
 * @param {Object} chunkInfo { chunkNumber, startLine, endLine }
 * @param {Object} job { _id, publisher_id, key }
 * @param {String} filePath
 */
process.on('message', async ({ chunkInfo, job, filePath }) => {
	let database = null;

	try{
		// 로거 설정
		cLogger(config);


		// DB 연결 시작
		database = await mongooseForChild(config).catch(err => {
			throw new BusinessError({ message: `[Kvext FullSync Child Process] DB 연결 에러` }, { err, detail: JSON.stringify(err, null, 2)});
		});
		logger.debug(KVEXT_LOGGER, `[Kvext FullSync Child Process] chunkNumber #${chunkInfo.chunkNumber} 디비 연결중 ..`);


		// ChunkInfo 처리하기
		const dataCount = await processChunkInfo({ chunkInfo, job, filePath });


		// DB 연결 종료
		logger.debug(KVEXT_LOGGER, `[Kvext FullSync Child Process] chunkNumber #${chunkInfo.chunkNumber} 디비 종료중 ..`);
		await database.db.close();


		// 부모 프로세스에 처리 결과 전달
		process.send({ state: 'COMPLETE', dataCount });
	} catch (err) {
		logger.error(`[Kvext FullSync Child Process] Error :: \n`, err);

		if(!_.isNil(database)) await database.db.close();

		process.send({ state: 'FAILED', err });
	}
});


/**
 * processChunkInfo : ChunkInfo 처리하기
 * 
 * @param {Object} metaInfo { chunkInfo, job, filePath }
 */
const processChunkInfo = async ({ chunkInfo, job, filePath }) => {
	// [PROCESS 1] fullSync 파일에서 chunkData 추출, 가공 및 DB 저장
	const dataCount = await _processChunkData({ job, filePath }, chunkInfo);

	return dataCount;
};


/**
 * [Promise]
 * [PROCESS 1] _processChunkData : fullSync 파일에서 chunkData 추출 및 가공
 * 
 * @param {Object} metaInfo { job: { _id, publisher_id, key }, filePath }
 * @param {Object} chunkInfo { chunkNumber, startLine, endLine }
 * @return dataCount
 */
const _processChunkData = ({ job: { publisher_id, key }, filePath }, { chunkNumber, startLine, endLine }) => {
	logger.debug(KVEXT_LOGGER, `[fullsync-worker-child :: _processChunkData] chunkNumber #${chunkNumber} 추출 및 가공 처리 시작`);

	return new Promise((resolve, reject) => {

		/* DB 저장 */
		const BULK_SIZE = config.kvext_fullsync_bulk_size || 20000;

		const dbStream = new Readable({ objectMode: true });
		dbStream._read = () => {};

		let dataCount = 0;
		let dataList = new Array();

		dbStream.on('data', async data => {
			try {
				dataCount++;

				dataList.push(data);

				if (dataCount % BULK_SIZE === 0) {
					dbStream.pause();

					await _insertExtendedKeyValues(dataList);
					dataList = new Array();

					dbStream.resume();
				}
			} catch (err) {
				reject({ message: `[fullsync-worker-child :: _processChunkData] chunkNumber #${chunkNumber} dbStream data 에러`, err });

				closeStreams();
			}
		}).on('end', async () => {
			try {
				if (dataCount % BULK_SIZE !== 0) {
					await _insertExtendedKeyValues(dataList);
				}

				resolve(dataCount);

				dbStream.destroy();

				logger.debug(KVEXT_LOGGER, `[fullsync-worker-child :: _processChunkData] chunkNumber #${chunkNumber} dbStream end 완료`);
			} catch (err) {
				reject({ message: `[fullsync-worker-child :: _processChunkData] chunkNumber #${chunkNumber} dbStream end 에러`, err });

				closeStreams();
			}
		}).on('error', err => {
			reject({ message: `[fullsync-worker-child :: _processChunkData] chunkNumber #${chunkNumber} dbStream 에러`, err });

			closeStreams();
		});


		/* csv 읽기 */
		const fileStream = fs.createReadStream(filePath);
		const csvStream = csv(); // seperator 디폴트가 콤마
		let lineCount = 0; 

		const onData = (data) => {
			try {
				lineCount++;

				if(startLine <= lineCount && lineCount <= endLine) {
					// chunk 만들기
					const chunk = _makeChunk({ publisher_id, key }, data);

					// chunk가 없다면, 데이터 처리하지 않는다. 
					if (_.isNil(chunk)) {
						return;
					}

					// dbStream에 chunk 전달
					dbStream.push(chunk);
				}

				if (endLine < lineCount) {
					csvStream.removeListener('data', onData);
					csvStream.end();
				}
			} catch(err) {
				reject({ message: `[fullsync-worker-child :: _processChunkData] chunkNumber #${chunkNumber} csvStream data 에러`, err });

				closeStreams();
			}
		};

		csvStream.on('data', onData);

		csvStream.on('end', async () => {
			try {
				logger.debug(KVEXT_LOGGER, `[fullsync-worker-child :: _processChunkData] chunkNumber #${chunkNumber} csvStream end 완료`);

				dbStream.push(null);
				csvStream.removeListener('data', onData);
				fileStream.close();
			} catch (err) {
				reject({ message: `[fullsync-worker-child :: _processChunkData] chunkNumber #${chunkNumber} csvStream end 에러`, err });

				closeStreams();
			}
		});

		csvStream.on('error', err => {
			reject({ message: `[fullsync-worker-child :: _processChunkData] chunkNumber #${chunkNumber} csvStream 에러`, err });

			closeStreams();
		});

		fileStream.on('error', err => {
			reject({ message: `[fullsync-worker-child :: _processChunkData] chunkNumber #${chunkNumber} fileStream 에러`, err });

			closeStreams();
		});

		fileStream.pipe(csvStream);

		// 모든 stream 종료 처리
		const closeStreams = () => {
			if (dbStream) dbStream.destroy();
			if (csvStream) {
				csvStream.removeListener('data', onData);
				csvStream.end();
			}
			if (fileStream) fileStream.close();
		};
	});
};


/**
 * _makeChunk : Chunk 가공하기
 * 
 * @param {Object} metaInfo { publisher_id, key }
 * @param {Object} data
 * { // 인플루언서 Example
		bid, // Key 정보
		spaceid, adMediation, influencerCtg, calp, // 확장 정보
		...
 * }
 * @return {Object} chunk
 * {
		publisher_id: '5be0f4c7ad288d002bf54dc2',
		key: 'bid',
		value: 'bid_430',
		items: [
			{
				key: 'spaceid',
				value: 'spaceid_1'
			},
			{
				key: 'adMediation',
				value: 'g'
			},
			{
				key: 'influencerCtg',
				value: '여행'
			},
			{
				key: 'calp',
				value: 'travel'
			}
		]
 * }
*/
const _makeChunk = ({ publisher_id, key }, data) => {
	// data가 없는 경우, 제외
	// data[key]가 없는 경우, 즉 확장키 필드가 없는 경우, 제외
	if (_.isNil(data) || _.isEmpty(data) || _.isEmpty(data[key])) {
		return;
	}

	let chunk = {
		publisher_id: ObjectId(publisher_id), 
		key, 
		value: data[key],
	};

	let items = new Array();
	_.forOwn(data, (v, k) => {
		// ExtensionKey는 제외하고, 확장 정보만 추가한다. 
		if (!_.isEqual(key, k)) {
			items.push({
				key:k,
				value:v,
			});
		}
	});

	chunk.items = items;

	return chunk;
};


/**
 * _insertExtendedKeyValues : 가공한 chunkData를 ExtendedKeyValues DB에 저장
 * 
 * @param {Array} chunkData [{ publisher_id, key, value, items }]
 */
 const _insertExtendedKeyValues = async chunkData => {
	const today = new Date();

	let keyValues = chunkData.map(({ publisher_id, key, value, items }) => {
		return {
			publisher_id, key, value, items,
			valid: 1,
			modifiedAt:today, createdAt:today
		};
	});

	await ExtendedKeyValue.insertMany(keyValues);
};
