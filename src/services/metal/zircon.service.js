'use strict';

import path from 'path';
import config from "../../config/config";
import * as logger from "../../utils/logger.util";
import moment from "moment";
import _ from "lodash";
import { DataEnvironments } from "../../models/data/data-environments.schema";
import * as c3HdfsApi from "../../c3/hdfs-api"
import * as c3HdfsCli from "../../c3/hdfs-cli"
import * as mailer from "../../utils/mail.util";

/**
 * Zircon B GFP 최근 컴팩션 일자 갱신
 * @returns {Promise<void>}
 */
module.exports.refreshRecentZirconBGfpYmd = async () => {
	// 마지막 컴팩션 일자
	const env1 = await DataEnvironments.findOne({ 'name': 'zircon-b-gfp-recent-compaction-ymd' });
	const lastZirconBGfpDate = moment(env1.value, 'YYYYMMDD');
	let recentZirconBGfpYmd = lastZirconBGfpDate.format('YYYY/MM/DD'); // while loop에서 사용할 변수
	const lastZirconBGfpYmd = recentZirconBGfpYmd; // 로그 찍기 용 변수
	logger.debug(`\nZircon B GFP 최근 컴팩션 일자 업데이트.. lastZirconBGfpYmd:${lastZirconBGfpYmd}`);

	let current = lastZirconBGfpDate.add(1, 'days'); // 마지막 컴팩션 일자 + 1 일부터 조사
	const now = moment().hour(0).minute(0).second(0).millisecond(0)

	while (current.isBefore(now)) { // D-1까지 검사
		const currentYmd = current.format('YYYY/MM/DD');

		const filePath = path.join(config.zircon_b.hdfs.root_dir, config.zircon_b.hdfs.gfp.root_dir, config.zircon_b.hdfs.gfp.warehouse, currentYmd, '_COMPACTION_SUCCESS');
		const isExist = await c3HdfsApi.exists(filePath);
		logger.debug(`Zircon B GFP 최종 컴팩션 일자 검사중. filePath=${filePath} isExist=${isExist}`);

		// 해당 날짜에 _COMPACTION_SUCCESS가 있으면 최근 컴팩션 일자 갱신
		if (isExist) {
			if (process.env.NODE_ENV === 'local' || process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'test') {
				// 테스트인 경우는 중간에 이가 빠지는 경우가 있어 순차 컴팩션가 아니더라도
				// 마지막으로 쌓은 시간대를 최근 컴팩션 일자로 함
				recentZirconBGfpYmd = currentYmd;
			} else {
				// 중간에 이가 빠지지 않는 경우만 설정
				if (moment(recentZirconBGfpYmd, 'YYYY/MM/DD').add(1, 'days').isSame(moment(currentYmd, 'YYYY/MM/DD'))) {
					recentZirconBGfpYmd = currentYmd;
				}
			}
			logger.debug(`Zircon B GFP 최종 컴팩션 일자 검사중. ${lastZirconBGfpYmd} 이후 ${currentYmd} 존재함`);
		} else {
			logger.debug(`Zircon B GFP 최종 컴팩션 일자 검사중. ${lastZirconBGfpYmd} 이후 ${currentYmd} 존재하지 않음`);
		}

		current = current.add(1, 'days');
	}

	// 최근 컴팩션 일자 반영
	await _updateRecentZirconBGfpYmd(recentZirconBGfpYmd)
};

const _updateRecentZirconBGfpYmd = async (recentZirconBGfpYmd) => {
	logger.debug('Zircon B GFP 최종 컴팩션 일자 확정.', recentZirconBGfpYmd);
	await DataEnvironments.updateOne(
		{ 'name': 'zircon-b-gfp-recent-compaction-ymd' },
		{
			'$set':
				{
					'value': recentZirconBGfpYmd.replace(/\//g, ''),
					'modifiedAt': moment()
				}
		}
	);
};

/**
 * - Zircon B 최근 컴팩션 일자 갱신
 * - D-1, D-2에 대해서는 하루 4번 갱신하므로 최근 컴팩션 일자를 확정하기 어렵다.
 * - 따라서 Environment.name = 'zircon-b-recent-compaction-ymd'에 해당하는 날짜 이후부터 D-3까지의 날짜 중
 *   날짜 오름차순으로 날짜별 경로 ({ymd}/_publisherId=xx/_adProviderId=xx) 아래
 *     _COMPACTION_SUCCESS가 모두 존재한다면 최근 컴팩션 일자 확정
 *     _adProviderId 경로는 있는데 _COMPACTION_SUCCESS 파일이 없으면 확정되지 않음.
 * @returns {Promise<void>}
 */
module.exports.refreshRecentZirconBYmd = async () => {
	// 마지막 컴팩션 일자
	const env1 = await DataEnvironments.findOne({ 'name': 'zircon-b-recent-compaction-ymd' });
	const lastZirconBDate = moment(env1.value, 'YYYYMMDD');
	let recentZirconBYmd = lastZirconBDate.format('YYYY/MM/DD'); // while loop에서 사용할 변수
	const lastZirconBYmd = recentZirconBYmd; // 로그 찍기 용 변수
	logger.debug(`Zircon B 최근 컴팩션 일자 업데이트.. lastZirconBYmd:${lastZirconBYmd}`);

	let current = lastZirconBDate.add(1, 'days'); // 마지막 컴팩션 일자 + 1 일부터 조사

	// D-2
	const d2 = moment().hour(0).minute(0).second(0).millisecond(0).subtract(2, 'days')
	logger.debug(`Zircon B 최종 컴팩션 일자 검사중. D-2=${d2.format('YYYY/MM/DD')} 보다 작은 날짜로 검사..`);

	while (current.isBefore(d2)) {  // D-2보다 작은 날짜로 검사. 즉 D-3까지 검사
		const currentYmd = current.format('YYYY/MM/DD');
		const isExist = await _all_compaction_success_files_exist(currentYmd)

		// 해당 날짜에 _COMPACTION_SUCCESS가 있으면 최근 컴팩션 일자 갱신
		if (isExist) {
			if (process.env.NODE_ENV === 'local' || process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'test') {
				// 테스트인 경우는 중간에 이가 빠지는 경우가 있어 순차 컴팩션가 아니더라도
				// 마지막으로 쌓은 시간대를 최근 컴팩션 일자로 함
				recentZirconBYmd = currentYmd;
			} else {
				// 중간에 이가 빠지지 않는 경우만 설정
				if (moment(recentZirconBYmd, 'YYYY/MM/DD').add(1, 'days').isSame(moment(currentYmd, 'YYYY/MM/DD'))) {
					recentZirconBYmd = currentYmd;
				}
			}
			logger.debug(`Zircon B 최종 컴팩션 일자 검사중. ${lastZirconBYmd} 이후 ${currentYmd} 존재함`);

			// _COMPACTION_SUCCESS 파일 생성
			const compactionSuccessFile = path.join(config.zircon_b.hdfs.root_dir, config.zircon_b.hdfs.warehouse, currentYmd, '_COMPACTION_SUCCESS');
			logger.debug(`Zircon B ${currentYmd} _COMPACTION_SUCCESS 파일(${compactionSuccessFile}) 생성..`);
			await c3HdfsCli.touch(compactionSuccessFile);
		} else {
			logger.debug(`Zircon B 최종 컴팩션 일자 검사중. ${lastZirconBYmd} 이후 ${currentYmd} 존재하지 않음`);
		}

		current = current.add(1, 'days');
	}

	// 최근 컴팩션 일자 반영
	await _updateRecentZirconBYmd(recentZirconBYmd)
};

const _all_compaction_success_files_exist = async (currentYmd) => {
	let sucFileCnt = 0;

	// ymdPath = "/user/gfp-data/zircon/b/warehouse/2024/05/06"
	const ymdPath = path.join(config.zircon_b.hdfs.root_dir, config.zircon_b.hdfs.warehouse, currentYmd);
	logger.debug(`Zircon B _all_compaction_success_files_exist() 여부 확인중.. ymdPath: ${ymdPath}`)

	// 날짜 경로 아래 PUB 경로
	if (await c3HdfsApi.exists(ymdPath)) {
		const hourDirs = await c3HdfsApi.readDir(ymdPath);
		for (const hourDir of hourDirs) {
			// logger.debug(`hourDir: ${hourDir.path}`)
			const pubDirs = await c3HdfsApi.readDir(hourDir.path);
			// pubDir = "/user/gfp-data/zircon/b/warehouse/2024/05/06/_publisherId=xxx"
			for (const pubDir of pubDirs) {
				// PUB 경로 아래 AP경로
				if (pubDir.pathSuffix.startsWith('_publisherId')) {
					// logger.debug(`PUB: ${pubDir.path}`)
					const apDirs = await c3HdfsApi.readDir(pubDir.path);
					// apDir = "/user/gfp-data/zircon/b/warehouse/2024/05/06/_publisherId=xxx/_adProviderId=xxx"
					for (const apDir of apDirs) {
						if (apDir.pathSuffix.startsWith('_adProviderId')) {
							// logger.debug(`\t\tAP: ${apDir.path}`)
							// successFile = "/user/gfp-data/zircon/b/warehouse/2024/05/06/_publisherId=xxx/_adProviderId=xxx/_COMPACTION_SUCCESS"
							const successFile = path.join(apDir.path, "_COMPACTION_SUCCESS");
							// logger.debug(`\t\t_all_compaction_success_files_exist() successFile=${successFile}`);
							const isExist = await c3HdfsApi.exists(successFile);
							if (isExist) {
								sucFileCnt++;
							} else {
								logger.debug(`Zircon B 최종 컴팩션 일자 검사중. ${successFile} 파일 없음`);
								return false;
							}
						}
					}
				}
			}
		}
	} else {
		return false;
	}

	logger.debug(`Zircon B 최종 컴팩션 일자 검사중. ${currentYmd} _COMPACTION_SUCCESS 파일 모두 존재. sucFileCnt=${sucFileCnt}`);
	return true;
}

const _updateRecentZirconBYmd = async (recentZirconBYmd) => {
	logger.debug('Zircon B 최종 컴팩션 일자 확정.', recentZirconBYmd);
	await DataEnvironments.updateOne(
		{ 'name': 'zircon-b-recent-compaction-ymd' },
		{
			'$set':
				{
					'value': recentZirconBYmd.replace(/\//g, ''),
					'modifiedAt': moment()
				}
		}
	);
};

/**
 * 보관 기간이 지난 Zircon B, Zircon B GFP 삭제
 * @returns {Promise<void>}
 */
module.exports.deleteExpiredZirconB = async () => {
	// 지르콘 보관기간 조회. 누베스 백업 설정에 있음.
	/*
	{
		...
		"name" : "backup-nubes-config",
		"value" : {
			"dataInfo" : {
				...
				"zircon_b" : {
					"basePath" : "/user/gfp-data/zircon/b",
					"referDocName" : "zircon-b-recent-compaction-ymd",
					"backupOffset" : "-7M",
					"deleteOffset" : "-25M"
				},
				"zircon_r" : {
					"basePath" : "/user/gfp-data/zircon/r",
					"referDocName" : "zircon-r-recent-compaction-ymd",
					"backupOffset" : "-3M",
					"deleteOffset" : "-13M"
				}
			},
			...
		}
	 }
	 */
	const doc = await DataEnvironments.findOne({ name: 'backup-nubes-config' }, { value: 1 });
	const backupOffset = doc.value.dataInfo.zircon_b.backupOffset ? doc.value.dataInfo.zircon_b.backupOffset : '-7M';
	logger.debug(`Zircon B backupOffset=${backupOffset}`);

	const ymdToDelete = calculateTargetDt(backupOffset)
	logger.debug(`Zircon B ymdToDelete=${ymdToDelete}`)

	// ZBGFP 삭제
	let zbGfpPath = path.join(config.zircon_b.hdfs.root_dir, config.zircon_b.hdfs.gfp.root_dir, config.zircon_b.hdfs.gfp.intermediate, ymdToDelete); // /user/gfp-data/zircon/b/gfp/intermeidate/2024/09/01
	logger.debug(`보관 기간이 지난 Zircon B GFP 삭제 (${zbGfpPath}) ..`)
	await c3HdfsApi.delete(zbGfpPath, true)
	logger.debug(`보관 기간이 지난 Zircon B GFP 삭제 (${zbGfpPath}) 완료`)

	zbGfpPath = path.join(config.zircon_b.hdfs.root_dir, config.zircon_b.hdfs.gfp.root_dir, config.zircon_b.hdfs.gfp.compaction, ymdToDelete); // /user/gfp-data/zircon/b/gfp/compaction/2024/09/01
	logger.debug(`보관 기간이 지난 Zircon B GFP 삭제 (${zbGfpPath}) ..`)
	await c3HdfsApi.delete(zbGfpPath, true)
	logger.debug(`보관 기간이 지난 Zircon B GFP 삭제 (${zbGfpPath}) 완료`)

	zbGfpPath = path.join(config.zircon_b.hdfs.root_dir, config.zircon_b.hdfs.gfp.root_dir, config.zircon_b.hdfs.gfp.warehouse, ymdToDelete); // /user/gfp-data/zircon/b/gfp/warehouse/2024/09/01
	logger.debug(`보관 기간이 지난 Zircon B GFP 삭제 (${zbGfpPath}) ..`)
	await c3HdfsApi.delete(zbGfpPath, true)
	logger.debug(`보관 기간이 지난 Zircon B GFP 삭제 (${zbGfpPath}) 완료`)

	// ZB 삭제
	let zbPath = path.join(config.zircon_b.hdfs.root_dir, config.zircon_b.hdfs.intermediate, ymdToDelete); // /user/gfp-data/zircon/b/intermeidate/2024/09/01
	logger.debug(`보관 기간이 지난 Zircon B 삭제 (${zbPath}) ..`)
	await c3HdfsApi.delete(zbPath, true)
	logger.debug(`보관 기간이 지난 Zircon B 삭제 (${zbPath}) 완료`)

	zbPath = path.join(config.zircon_b.hdfs.root_dir, config.zircon_b.hdfs.compaction, ymdToDelete); // /user/gfp-data/zircon/b/compaction/2024/09/01
	logger.debug(`보관 기간이 지난 Zircon B 삭제 (${zbPath}) ..`)
	await c3HdfsApi.delete(zbPath, true)
	logger.debug(`보관 기간이 지난 Zircon B 삭제 (${zbPath}) 완료`)

	/* zb warehouse 경로 하위 날짜 디렉토리 삭제는 누베스 백업 공통 모듈에서 수행하므로 본 로직에서는 스킵 */
};

const calculateTargetDt = (backupOffset) => {
	const unitParams = ['years', 'months', 'days', 'hours'];

	const pattern = /((-?\d+)Y)?((-?\d+)M)?((-?\d+)D)?((-?\d+)H)?/;
	const backupMatch = backupOffset.match(pattern);

	// backupMatch 결과에서 undefined 혹은 null 값에 대한 처리를 추가합니다.
	const backupDtCalcInfo = unitParams.reduce((obj, unit, idx) => {
		let value = parseInt(backupMatch[idx * 2 + 1]);
		if (!isNaN(value)) {
			obj[unit] = value;
		} else {
			obj[unit] = null;
		}
		return obj;
	}, {});

	// logger.debug(`backupDtCalcInfo=${JSON.stringify(backupDtCalcInfo, null, 2)}`);

	let backupDt = moment();
	backupDt = backupDt.add(backupDtCalcInfo.years || 0, 'years');
	backupDt = backupDt.add(backupDtCalcInfo.months || 0, 'months');
	backupDt = backupDt.add(backupDtCalcInfo.days || 0, 'days');
	backupDt = backupDt.add(backupDtCalcInfo.hours || 0, 'hours');

	return backupDt.format('YYYY/MM/DD');
};


/**
 * Zircon R GFP 최근 컴팩션 일자 갱신
 * @returns {Promise<void>}
 */
module.exports.refreshRecentZirconRGfpYmd = async () => {
	// 마지막 컴팩션 일자
	const env1 = await DataEnvironments.findOne({ 'name': 'zircon-r-gfp-recent-compaction-ymd' });
	const lastZirconRGfpDate = moment(env1.value, 'YYYYMMDD');
	let recentZirconRGfpYmd = lastZirconRGfpDate.format('YYYY/MM/DD'); // while loop에서 사용할 변수
	const lastZirconRGfpYmd = recentZirconRGfpYmd; // 로그 찍기 용 변수
	logger.debug(`\nZircon R GFP 최근 컴팩션 일자 업데이트.. lastZirconRGfpYmd:${lastZirconRGfpYmd}`);

	let current = lastZirconRGfpDate.add(1, 'days'); // 마지막 컴팩션 일자 + 1 일부터 조사
	const now = moment().hour(0).minute(0).second(0).millisecond(0)

	while (current.isBefore(now)) { // D-1까지 검사
		const currentYmd = current.format('YYYY/MM/DD');

		const filePath = path.join(config.zircon_b.hdfs.root_dir, config.zircon_b.hdfs.gfp.root_dir, config.zircon_b.hdfs.gfp.warehouse, currentYmd, '_COMPACTION_SUCCESS');
		const isExist = await c3HdfsApi.exists(filePath);
		logger.debug(`Zircon R GFP 최종 컴팩션 일자 검사중. filePath=${filePath} isExist=${isExist}`);

		// 해당 날짜에 _COMPACTION_SUCCESS가 있으면 최근 컴팩션 일자 갱신
		if (isExist) {
			if (process.env.NODE_ENV === 'local' || process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'test') {
				// 테스트인 경우는 중간에 이가 빠지는 경우가 있어 순차 컴팩션가 아니더라도
				// 마지막으로 쌓은 시간대를 최근 컴팩션 일자로 함
				recentZirconRGfpYmd = currentYmd;
			} else {
				// 중간에 이가 빠지지 않는 경우만 설정
				if (moment(recentZirconRGfpYmd, 'YYYY/MM/DD').add(1, 'days').isSame(moment(currentYmd, 'YYYY/MM/DD'))) {
					recentZirconRGfpYmd = currentYmd;
				}
			}
			logger.debug(`Zircon R GFP 최종 컴팩션 일자 검사중. ${lastZirconRGfpYmd} 이후 ${currentYmd} 존재함`);
		} else {
			logger.debug(`Zircon R GFP 최종 컴팩션 일자 검사중. ${lastZirconRGfpYmd} 이후 ${currentYmd} 존재하지 않음`);
		}

		current = current.add(1, 'days');
	}

	// 최근 컴팩션 일자 반영
	await _updateRecentZirconRGfpYmd(recentZirconRGfpYmd)
};

const _updateRecentZirconRGfpYmd = async (recentZirconRGfpYmd) => {
	logger.debug('Zircon R GFP 최종 컴팩션 일자 확정.', recentZirconRGfpYmd);
	await DataEnvironments.updateOne(
		{ 'name': 'zircon-r-gfp-recent-compaction-ymd' },
		{
			'$set':
				{
					'value': recentZirconRGfpYmd.replace(/\//g, ''),
					'modifiedAt': moment()
				}
		}
	);
};

/**
 * 보관 기간이 지난 Zircon R GFP 삭제
 * @returns {Promise<void>}
 */
module.exports.deleteExpiredZirconRGfp = async () => {
	// 지르콘 보관기간 조회. 누베스 백업 설정에 있음.
	/*
	// Environment.name = 'backup-nubes-config'
	{
		...
		"name" : "backup-nubes-config",
		"value" : {
			"dataInfo" : {
				...
				"zircon_b" : {
					"basePath" : "/user/gfp-data/zircon/b",
					"referDocName" : "zircon-b-recent-compaction-ymd",
					"backupOffset" : "-7M",
					"deleteOffset" : "-25M"
				},
				"zircon_r_gfp" : {
					"basePath" : "/user/gfp-data/zircon/r/gfp",
					"referDocName" : "zircon-r-gfp-recent-compaction-ymd",
					"backupOffset" : "-13M",
					"deleteOffset" : "-25M"
				}
			},
			...
		}
	}

	// common.js
 	zircon_r_gfp: {
 	    ...
		backup_restore: {
			sub_path: ['/warehouse'],
			dt_path: '/YYYY/MM/DD'
		}
	},
	 */
	const doc = await DataEnvironments.findOne({ name: 'backup-nubes-config' }, { value: 1 });
	const backupOffset = doc.value.dataInfo.zircon_r_gfp.backupOffset ? doc.value.dataInfo.zircon_r_gfp.backupOffset : '-7M';
	logger.debug(`Zircon R backupOffset=${backupOffset}`);

	const ymdToDelete = calculateTargetDt(backupOffset)
	logger.debug(`Zircon R ymdToDelete=${ymdToDelete}`)

	// ZRGFP 삭제 - intermediate
	let zrGfpPath = path.join(config.zircon_r_gfp.hdfs.root_dir, config.zircon_r_gfp.hdfs.root_dir, config.zircon_r_gfp.hdfs.intermediate, ymdToDelete); // /user/gfp-data/zircon/r/gfp/intermeidate/2024/09/01
	logger.debug(`보관 기간이 지난 Zircon R GFP 삭제 (${zrGfpPath}) ..`)
	await c3HdfsApi.delete(zrGfpPath, true)
	logger.debug(`보관 기간이 지난 Zircon R GFP 삭제 (${zrGfpPath}) 완료`)

	// ZRGFP 삭제 - compaction
	zrGfpPath = path.join(config.zircon_r_gfp.hdfs.root_dir, config.zircon_r_gfp.hdfs.root_dir, config.zircon_r_gfp.hdfs.compaction, ymdToDelete); // /user/gfp-data/zircon/r/gfp/compaction/2024/09/01
	logger.debug(`보관 기간이 지난 Zircon R GFP 삭제 (${zrGfpPath}) ..`)
	await c3HdfsApi.delete(zrGfpPath, true)
	logger.debug(`보관 기간이 지난 Zircon R GFP 삭제 (${zrGfpPath}) 완료`)

	/* zrgfp warehouse 경로 하위 날짜 디렉토리 삭제는 누베스 백업 공통 모듈에서 수행하므로 본 로직에서는 스킵 */

	// zrGfpPath = path.join(config.zircon_r_gfp.hdfs.root_dir, config.zircon_r_gfp.hdfs.root_dir, config.zircon_r_gfp.hdfs.warehouse, ymdToDelete); // /user/gfp-data/zircon/r/gfp/warehouse/2024/09/01
	// logger.debug(`보관 기간이 지난 Zircon R GFP 삭제 (${zrGfpPath}) ..`)
	// await c3HdfsApi.delete(zrGfpPath, true)
	// logger.debug(`보관 기간이 지난 Zircon R GFP 삭제 (${zrGfpPath}) 완료`)

};

/**
 * Zircon R GFP 모니터링
 * @returns {Promise<void>}
 */
module.exports.monitorZirconRGfp = async () => {
	// ZRGFP 지연여부 확인
	await _checkZrgfp();
}

// ZRGFP지연되는지 확인
const _checkZrgfp = async () => {
	const { isDelayed, zrgfpTimeout, recentZrgfpCompactionAt } = await _isDelayedZrgfp();
	if (isDelayed) {
		const subject = 'Zircon R GFP가 지연되고 있습니다.';

		let html = `Zircon R GFP가 지연되고 있습니다.<br/><br/>`;
		html += `대상 일자: ${moment().format('YYYY-MM-DD')}<br/>`;
		// html += `최근 적재 일자: ${moment(recentZrgfpYmd, 'YYYYMMDD').format('YYYY-MM-DD')}<br/>`;
		html += `마지막 적재 일시: ${moment(recentZrgfpCompactionAt).format('YYYY-MM-DD HH:mm:ss')}<br/>`;
		html += `허용된 지연 시간: 대상 일자 + 1일 0시 기준 ${zrgfpTimeout}시간<br/>`;

		logger.debug(`zircon.service._checkZrgfp() subject = ${subject}`);
		logger.debug(`zircon.service._checkZrgfp() html = ${html}`);

		await _sendMail(subject, html);
	}
};

const _isDelayedZrgfp = module.exports.isDelayedZrgfp = async (targetYmd = '') => {
	// ZRGFP 지연 허용 시간(디폴트 8시간)
	// 허용일시: targetYmd + 1일의 0시부터 타임아웃 시간까지
	const env2 = await DataEnvironments.findOne({ name: 'zircon-r-gfp-compaction-max-tolerable-delay-hours' }, { value: 1 });
	const zrgfpTimeout = (env2 && env2.value) ? parseInt(env2.value) : 8;

	// 최근 ZRGFP 컴팩션 일자
	const env3 = await DataEnvironments.findOne({ name: 'zircon-r-gfp-recent-compaction-ymd' }, { value: 1 });
	let recentZrgfpYmd = (env3 && env3.value) ? env3.value : '';

	// 대상이 되는 ZRGFP 일자
	const targetDt = targetYmd == '' ? moment() : moment(targetYmd, 'YYYYMMDD');

	// targetYmd의 ZRGFP는 (targetYmd + 1일)의 0시에 zrgfpTimeout 시간이 더해진 시간까지 나와야 함
	const tolerableDt = targetDt.add(1, 'days').startOf('day').add(zrgfpTimeout, 'hours');
	const ymdOfDMinusOne = targetDt.subtract(1, 'days').format('YYYYMMDD');

	const now = moment();

	// 대상이 되는 ZRGFP보다 최근 적재일자가 이전이고 현재 시간이 허용 시간을 넘었는지 확인
	const isDelayed = tolerableDt < now && recentZrgfpYmd < ymdOfDMinusOne;
	logger.debug(`zircon.service._checkZrgfp() :: 
	targetYmd=${targetYmd}
	recentZrgfpYmd=${recentZrgfpYmd} 
	tolerableDateTime=${tolerableDt.format('YYYY-MM-DD HH:mm:ss')}  (targetYmd 0시 기준 zrgfpTimeout=${zrgfpTimeout}(hours)) 
	now=${now.format('YYYY-MM-DD HH:mm:ss')}
	isDelayed=${isDelayed}`);

	return {
		isDelayed,
		zrgfpTimeout,
		recentZrgfpYmd,
		recentZrgfpCompactionAt: env3.modifiedAt
	};
};

const _sendMail = async (subject, html) => {
	const env = await DataEnvironments.findOne({ 'name': 'revenue-sharing-report-monitor-alarm-receivers' }).select('value');
	if (_.isNil(env) || _.isNil(env.value) || _.isEmpty(env.value)) {
		return;
	}
	const to = env.value.join(';');
	logger.debug(`zircon.service._sendMail() to=${to} subject=${subject}`);

	await mailer.sendMail({ to, subject, html });
};
