'use strict';

import * as _ from 'lodash';
import * as logger from '../../../utils/logger.util';
import mongoose from 'mongoose';
import { getDatabase } from "../../../common/mongoose";

import { SummaryAdProviderStatsDaily } from '../../../models/summary-ad-provider-stats-daily.schema';
import { SummaryAdProviderStatsDailySimple } from '../../../models/summary-ad-provider-stats-daily-simple.schema';

import * as summaryJobService from './ad-provider-stats-summary-job.service';
import * as nonRkService from './ad-provider-stats-summary-non-rk.service';
import * as rkService from './ad-provider-stats-summary-rk.service';

let ObjectId = mongoose.Types.ObjectId;

const { Readable } = require('stream');

export const SummaryType = {
	AdProvider: 'daily-simple',
	PlaceKey: 'daily',
};

export const SummaryCollection = {
	AdProvider: 'SummaryAdProviderStatsDailySimple',
	PlaceKey: 'SummaryAdProviderStatsDaily',
};

const TRANS_EXEC_CNT = 5000;

/**
 * Job Schedule 을 기반으로 연동 리포트 Summary Data 생성
 *
 */
module.exports.generateDailySummary = async (jobs) => {
	logger.debug('[ad-provider-stats-summary.service] generateDailySummaryBlock() 호출됨');

	for (let i=0; i<jobs.length; i++) {

		let job = jobs[i];

		logger.info(`\n\n★★★ ---------- JOB Start (${i+1}/${jobs.length} ---------- \n${JSON.stringify(job)}`);

		// 1. Ad Provider별 Summary 생성
		let executionStartFirst = _.now();

		const apInput = new Readable({ objectMode: true });
		apInput._read = () => {};

		// delete
		await _removeSummaryDataBulk(job, SummaryCollection.AdProvider);

		const createApSummary = new Promise(async (resolve, reject) => {
			// query
			let docs = await _fetchSummaryData(job, SummaryType.AdProvider);  //빈날짜 메꾸기 로직 때문에 query stream 처리는 우선 보류하고 insert만 처리
			docs.map(doc => apInput.push(doc));
			apInput.push(null);

			// insert
			_insertSummaryData(job, apInput, SummaryCollection.AdProvider, resolve);
		});
		let apCount = await createApSummary;

		const resultAP = {executionTime: _.now() - executionStartFirst, resultCount: apCount};

		logger.info('\n   >>> 1. AdProvider별 Summary생성 완료 ----', resultAP);


		// 2. Place Key별 Summary 생성
		let executionStart = _.now();

		// delete
		await _removeSummaryDataBulk(job, SummaryCollection.PlaceKey);

		const createPkSummary = new Promise(async (resolve, reject) => {
			// query
			let stream = await _fetchSummaryDataStream(job);

			// insert
			_insertSummaryData(job, stream, SummaryCollection.PlaceKey, resolve);
		});
		let pkCount = await createPkSummary;

		const resultPK = {executionTime: _.now() - executionStart, resultCount: pkCount};

		logger.info('\n   >>> 2. PlaceKey별 Summary생성 완료 ----', resultPK);


		//-------------------------------------------------------------------------
		// 3. 처리 결과 업데이트
		//-------------------------------------------------------------------------
		await summaryJobService.updateJob(
			job,

			_.now() - executionStartFirst,
			resultAP.resultCount + resultPK.resultCount,

			resultAP.executionTime,
			resultAP.resultCount,

			resultPK.executionTime,
			resultPK.resultCount,
		);

		logger.info(`---------- JOB End (${i+1}/${jobs.length}) ----------`);
	}
};


/**
 * 기존 Summary Data 삭제
 *
 * @deprecated since 2019
 */
const _removeSummaryDataBulk = (job, collection) => {
	logger.debug('[ad-provider-stats-summary.service] _removeSummaryDataBulk() 호출됨');

	const params = {
		ymd: { $gte: job.startDate, $lte: job.endDate },
		adProvider_id: ObjectId(job.adProviderId)
	};
	if (! job.rkUse) {
		params.publisher_id = ObjectId(job.publisherId);
	}

	return new Promise(async (resolve, reject) => {
		let bulk = getDatabase().db.model(collection).collection.initializeUnorderedBulkOp();
		bulk.find(params).delete();
		const results = await bulk.execute();

		logger.info('      1.삭제(BulkOp) ===', results.result.nRemoved);
		resolve(results);
	});
};



/**
 * 연동 리포트 원본으로부터 Summary Data 추출
 *
 */
const _fetchSummaryData = (job, summaryType, countries=[]) => {
	logger.debug('[ad-provider-stats-summary.service] _fetchSummaryData() 호출됨');

	return new Promise(async (resolve, reject) => {
		let docs = [];

		if (summaryType === SummaryType.AdProvider) {
			docs = (job.rkUse) ?
				await rkService.fetchDataAdProvider(job)
				:
				await nonRkService.fetchDataAdProvider(job);

		}/* else if (summaryType === SummaryType.PlaceKey) {
			docs = (job.rkUse) ?
				await rkService.fetchDataPlaceKey(job, countries)
				:
				await nonRkService.fetchDataPlaceKey(job, countries);

		} */

		logger.info('      2.조회 ===', docs.length);
		resolve(docs);
	});
};

const _fetchSummaryDataStream = async (job) => {
	logger.debug('[ad-provider-stats-summary.service] _fetchSummaryDataStream() 호출됨');

	let stream = (job.rkUse) ?
		await rkService.fetchDataStreamPlaceKey(job)
		:
		await nonRkService.fetchDataStreamPlaceKey(job);

	logger.info('      2.조회stream === ok');
	return stream;
};


/**
 * Stream 데이터를 DB 인입
 *
 */
const _insertSummaryData = (job, stream, collection, callback) => {
	logger.debug('[ad-provider-stats-summary.service] _insertSummaryData() 호출됨');

	let count = 0;

	let bulk = getDatabase().db.model(collection).collection.initializeUnorderedBulkOp();

	stream.on('data', function (doc) {
		//console.log('count=', count);
		count++;

		if (job.rkUse && collection === SummaryCollection.PlaceKey) {
			doc.adProviderPlace_ids = _.orderBy(doc.adProviderPlace_ids);
			doc.adUnitIds = _.orderBy(doc.adUnitIds);
		}
		bulk.insert(doc);

		if (count % TRANS_EXEC_CNT === 0) {
			stream.pause();
			bulk.execute((e, result) => {
				if (e) throw new Error(e.message);

				bulk = getDatabase().db.model(collection).collection.initializeUnorderedBulkOp();
				stream.resume();
			});
		}

	}).on('error', function (e) {
		throw new Error(e.message);

	}).on('end', function (e) {
		logger.info('      3.인입 count=== ', count);

		if (count % TRANS_EXEC_CNT !== 0) {
			bulk.execute((e, result) => {
				if (e) throw new Error(e.message);
			});
		}

		callback(count);
	});
};
