'use strict';

import _ from 'lodash';
import child_process from 'child_process';
import csv from 'csv';
import fs from 'fs';
import moment from 'moment-timezone';
import mongoose from 'mongoose';
import os from 'os';
import path from 'path';
import slash from 'slash';
import { PassThrough } from 'stream';
import util from 'util';
import zlib from 'zlib';
import Decimal from 'decimal.js';
import { AdProvider } from "../../../models/ad-providers.schema";

import { AdUnit } from '../../../models/ad-unit.schema';
import { AdProviderInfo } from '../../../models/ad-provider-infos.schema';
import { AdProviderRkStat } from '../../../models/ad-provider-rk-stats.schema';
import { AdProviderNonRkStat } from '../../../models/ad-provider-non-rk-stats.schema';
import { BusinessError } from '../../../common/error';
import config from '../../../config/config';
import commCd from '@ssp/ssp-common-code';
import cLogger from '../../../common/logger';
import { Environments } from "../../../models/environments.schema";
import extendedLogger from '../../../utils/logger-ext.util';
import initDatabase, { getDatabase } from '../../../common/mongooseForChildProcess';
import { Place } from '../../../models/places.schema';
import { Publisher } from '../../../models/publishers.schema';
import { MappingHistory } from '../../../models/mapping-history.schema';
import SBE from '../../../common/error-code';
import { SummaryRevenueSharingSchedule } from '../../../models/summary-revenue-sharing-schedules.schema';
import { SummaryRevenueSharingReport } from '../../../models/summary-revenue-sharing-reports.schema';
import * as fileUtil from '../../../utils/file.util';
import * as NubesClient from '../../../nubes/clients'
import csvstream from "csv-stream";
import * as c3HdfsCli from '../../../c3/hdfs-cli';
import * as c3HdfsApi from '../../../c3/hdfs-api';
import * as csvService from "./revenue-sharing-report-csv.service";

let ObjectId = mongoose.Types.ObjectId;
const cd = commCd.codeEnc('KO');
const log = extendedLogger('rs'); // 확장로거

/*
	CSV의 헤더 포맷

		- 엑셀에서 열 때 첫 줄에 한글이 있으면 데이터가 많다고 열리지 않음.
		- 각 줄의 첫글자에 공백을 넣어서 해결.
		- 셀 값 자체에 콤마(,)가 들어가는 경우를 위해 큰따옴표(")로 감싼다.
 */
let headerTemplate = '\ufeff'; // UTF-8 BOM (Byte Order Mark)
headerTemplate += 'Publisher,%s\\n';
headerTemplate += 'ReportType,Revenue Share\\n';
headerTemplate += 'Completed at,%s\\n';
headerTemplate += 'Period,%s ~ %s\\n';
headerTemplate += 'Date,%s\\n';
headerTemplate += 'Key Group Name,%s\\n';
headerTemplate += 'Creator,%s\\n\\n';
headerTemplate += 'Report Type,Date,AdUnit,AdProvider Name,AdProvider Timezone,AdProvider Currency,AdProvider Place Key,';
headerTemplate += 'USD Net Revenue,USD Revenue,KRW Net Revenue,KRW Revenue,AdProvider Impressions,AdProvider Clicks,';

// 	CSV의 ROW 포맷
const rowFormatWithGfpEstimatedRevenue = '%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n';

const STAT_TYPE_ADP_RK = 'AdProvider RK';
const STAT_TYPE_ADP_NONRK = 'AdProvider NonRK';
const STAT_TYPE_NAM = 'NAM';

//	메모리 적재 사이즈를 줄이기 위해 pageSize 단위로 끊어서 DB에서 가져옴
const PAGE_SIZE = 50000;

//	임시 컬렉션의 모델(스케줄 별로 생성됨)
let TempRs;
let tempCollectionName;

// 플레이스키에 #{ }가 포함되지 않을 때만 쓰기 위한 정규표현식
const RegExToExceptRowWithAdProviderPlaceKey = /#{(.*)}/;

// 상대경로를 절대경로로
let owfsDownloadAbsoluteRoot = config.owfs_root;
if (!_.isNil(owfsDownloadAbsoluteRoot) && !owfsDownloadAbsoluteRoot.startsWith(path.sep) && process.env.NODE_ENV != 'local') {
	owfsDownloadAbsoluteRoot = '/home1/irteam/deploy/' + owfsDownloadAbsoluteRoot;
}

let baseRunId = '';
let runId = '';
let AP_IDS_FOR_NAVER_DIRECT = [];
const AdProviderCache = new Map();

/**
 * child process 작업 수행
 */
process.on('message', async (schInfo) => {
	let database;

	try {
		cLogger(config);

		database = await initDatabase(config).catch(error => log.error(`[G-RVN-SHAR] revenue-sharing-report-csv-nam-child.service.process.on() :: ${error.stack}`));

		// AP 정보 캐싱(writeNonRK에서 사용)
		await _initAdProviderCache();

		// 리포트 작성
		await _writeReport(schInfo);

		await database.db.close();

		process.send({ op: 'complete', msg: `완료` });
	} catch (error) {
		await database.db.close();

		log.error(`[G-RVN-SHAR] revenue-sharing-report-csv-nam-child.service.process.on() :: ${error.stack}`);

		process.send({ op: 'fail', msg: error.message });
	}
});

/**
 * AP 정보 캐싱
 * @returns {Promise<void>}
 * @private
 */
const _initAdProviderCache = async () => {
	const docs = await AdProvider.find({}).select('_id name timezone currency');
	docs.forEach(doc => AdProviderCache.set(doc._id.toString(), doc));
};

/**
 * 리포트 작성
 * @param schInfo  : 차일드 프로세스 ID
 * @returns {Promise.<void>}
 */
const _writeReport = async (schInfo) => {
	const schedule = schInfo.schedule;

	let isExistFailedReport = false;

	try {
		// 번들에 묶인 리포트들의 기본 정보 조회
		const preparedReports = await SummaryRevenueSharingReport
			.find({ 'bundleId': schInfo.bundleId })
			.sort({ 'beginYmd': 1 });

		// [Data] PlaceKey가 존재하지 않는 Place 대응 (https://jira.navercorp.com/browse/GFP-677) - MpppingHistory에 없는 플레이스 처리
		// 이 매체가 'NAVER_DIRECT'를 사용한다면 해당 AP IDS 구함
		AP_IDS_FOR_NAVER_DIRECT = await csvService.getNaverDirectAdProviderIds(schedule.publisher_id);
		log.debug(`[G-RVN-SHAR] publisher_id=${schedule.publisher_id} 'NAVER_DIRECT' AP를 사용한다면 해당 AP IDS = ${AP_IDS_FOR_NAVER_DIRECT.join(',')}`);


		// 리포트 파일 별 작성
		for (const report of preparedReports) {
			try {
				await _writeIntoFile(schInfo.txId, schInfo.idx, schedule, schInfo.bundleId, report.beginYmd, report.endYmd, report.dateForFileName, report._id);
			} catch (error) {
				log.error(`[G-RVN-SHAR] revenue-sharing-report-csv-nam-child.service.writeReport() :: ${error.stack}`);
				await _updateReport(report._id, cd.RevenueSharingReportProgress.FAILURE.code, error);
				isExistFailedReport = true;
			}
		}
	} catch (error) {
		log.error(`[G-RVN-SHAR] revenue-sharing-report-csv-nam-child.service.writeReport() :: ${error.stack}`);
	}
};

/**
 * 시작날짜 ~ 종료날짜 까지를 한 파일로 만든다.
 *
 * @param childProcIdx    : 차일드 프로세스 ID
 * @param schedule        : 스케줄 정보
 * @param beginYmd        : 리포트 시작일자
 * @param endYmd          : 리포트 종료일자
 * @param dateForFileName : 파일명에서 사용될 날짜. yyyymm or yyyymmdd. 이 함수의 caller에서 결정해서 줌.
 * @param reportId        : 리포트 ID
 * @returns {Promise.<void>}
 */
const _writeIntoFile = async (txId, childProcIdx, schedule, bundleId, beginYmd, endYmd, dateForFileName, reportId) => {
	const starttime = moment();

	const beginDotYmd = moment(beginYmd, 'YYYYMMDD').format('YYYY.MM.DD');
	const endDotYmd = moment(endYmd, 'YYYYMMDD').format('YYYY.MM.DD');
	const beginDate = moment(beginYmd, 'YYYYMMDD');
	const endDate = moment(endYmd, 'YYYYMMDD');

	baseRunId = `[G-RVN-SHAR] v2.0 childProc[${childProcIdx}]  TXID:${txId}  PUB:${schedule.publisherName}(${schedule.publisher_id})  SCH:${schedule.name}(${schedule._id})  KG:${schedule.keyGroup.name}(${schedule.keyGroup_id})  BUNDLE:${bundleId}  REPORT:${reportId}`;

	/**
	 * 파일 준비
	 * @type {{ filePathWithoutRoot, file, workingFile, fileName }}
	 */
	let fileInfo = await _prepareFile(schedule.keyGroup_id, schedule.interval, dateForFileName);
	let workingFile = fileInfo.workingFile;

	/**
	 * 리포트 시작
	 */
	await _startReport(reportId, fileInfo.file);
	log.debug(`${baseRunId} start`);


	/**
	 * 키그룹에 해당하는 AdUnit ID 목록 조회
	 */
	const adUnitIds = await _getAdUnitIdsByKeyGroupId(schedule.keyGroup_id);
	log.debug(`${baseRunId}\n처리해야 할 광고유닛=\n${JSON.stringify(adUnitIds, null, 2)}`);

	/**
	 * 임시 컬렉션 모델 생성
	 * @type {string}
	 */
	tempCollectionName = `TempRs${reportId}`;
	TempRs = _createTempRsModel(tempCollectionName);

	/**
	 * 파일 열기
	 */
	if (fs.existsSync(workingFile)) { // 작업할 파일이 이미 있다면(.temp) 삭제하고
		fs.unlinkSync(workingFile);
	}
	const fda = fs.openSync(workingFile, 'a'); // append mode로 파일 생성

	try {
		let currDate = moment(beginDate);

		if (schedule.statsType.AdProvider === 1) {
			/**
			 * 리포트 작성 RK
			 */
			while (currDate.isSameOrBefore(endDate)) {
				// 한 파일 내 날짜를 월단위 통으로 돌릴 것인가.. 아님 일자로 돌릴 것인가..
				let date;
				if (schedule.interval == 'MONTHLY') {
					date = currDate.format('YYYYMM');
				} else {
					date = currDate.format('YYYYMMDD');
				}

				// RK 통계(플레이스ID 별)
				await _writeRk(schedule, date, fda, reportId, adUnitIds, beginYmd, endYmd);

				if (schedule.interval == 'MONTHLY') {
					currDate = moment(currDate.add(1, 'months'));
				} else {
					currDate = moment(currDate.add(1, 'days'));
				}
			}


			/**
			 * 리포트 작성 NonRK
			 */
			currDate = moment(beginDate);
			while (currDate.isSameOrBefore(endDate)) {
				// 한 파일 내 날짜를 월단위 통으로 돌릴 것인가.. 아님 일자로 돌릴 것인가..
				let date;
				if (schedule.interval == 'MONTHLY') {
					date = currDate.format('YYYYMM');
				} else {
					date = currDate.format('YYYYMMDD');
				}

				// NonRK 통계(플레이스키 별)
				await _writeNonRk(schedule, date, fda, reportId, adUnitIds, beginYmd, endYmd);

				if (schedule.interval == 'MONTHLY') {
					currDate = moment(currDate.add(1, 'months'));
				} else {
					currDate = moment(currDate.add(1, 'days'));
				}
			}
		}

		if (schedule.statsType.GFP === 1) {
			/**
			 * 리포트 작성 GFP
			 */
			currDate = moment(beginDate);
			while (currDate.isSameOrBefore(endDate)) {
				// 한 파일 내 날짜를 월단위 통으로 돌릴 것인가.. 아님 일자로 돌릴 것인가..
				let date;
				if (schedule.interval == 'MONTHLY') {
					date = currDate.format('YYYYMM');
				} else {
					date = currDate.format('YYYYMMDD');
				}

				await _writeGfp(schedule, date, fileInfo.file, fda, reportId, adUnitIds, beginYmd, endYmd);


				if (schedule.interval == 'MONTHLY') {
					currDate = moment(currDate.add(1, 'months'));
				} else {
					currDate = moment(currDate.add(1, 'days'));
				}
			}
		}

		/**
		 * 파일 닫기
		 * 닫지 않으면 OS의 fd가 고갈됨 (https://github.com/nodejs/help/issues/10)
		 */
		if (fda !== undefined) fs.closeSync(fda);

		/**
		 * 헤더 쓰기
		 */
		_appendHeader(schedule, beginDotYmd, endDotYmd, workingFile);

		/**
		 * 파일 명 변경
		 */
		_renameFileIfNeed(workingFile, fileInfo.file);

		/**
		 * DB건수와 파일건수 비교 결과 파일 떨굼
		 */
		await _writeCountAtValidationFile(schedule, reportId, fileInfo.file);


		/**
		 * NUBES에 업로드
		 */
		await _uploadToNubes(fileInfo.file);

		/**
		 * 로컬 파일을 OWFS로 복사
		 */
		const twoFile = await _copyToOwfs(fileInfo.file);

		// local과 owfs간 파일 건수가 불일치한다는 오류가 종종 발생하는데 실제로는 일치함. owfs에 파일이 싱크는데 시간을 줌.
		await _sleep(3000);

		/**
		 * 로컬 vs OWFS 파일 비교
		 */
		await _validateOwfs(twoFile);

		/**
		 * 리포트 완료
		 */
		await _completeReport(reportId);
	} catch (err) {
		log.error(`${err.stack}`);

		if (fda !== undefined) fs.closeSync(fda); // 파일 닫기
		throw err;
	}

	/**
	 * 임시 컬렉션 삭제
	 */
	try {
		const coll = await getDatabase().db.collections[tempCollectionName]
		if (coll) {
			await coll.drop()
		}
	} catch (err) {
	}

	/**
	 * 소요시간 계산
	 */
	const elapsedTime = moment() - starttime;
	log.debug(`<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<  ${fileInfo.file} 리포트 작성 완료. (msec): ${elapsedTime} (sec): ${elapsedTime / 1000}  >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>`);
};

/**
 * CSV 파일의 제일 첫 줄에 헤더 붙이기
 * @param schedule
 * @param workingFile
 */
const _appendHeader = (schedule, beginDotYmd, endDotYmd, workingFile) => {
	let completedYmdt = moment().format('YYYY.MM.DD HH:mm:ss');

	// KeyGroup에 묶인 key 헤더 설정
	let keysTitle = '';
	if (!_.isNil(schedule.keyGroup.keys) && !_.isEmpty(schedule.keyGroup.keys) && schedule.keyGroup.keys.length > 0) {
		keysTitle = schedule.keyGroup.keys.reduce((acc, cur) => {
			return acc + ',' + cur
		});
	}

	// 헤더 포매팅
	let headerFormat = headerTemplate;
	if (schedule.estimatedRevenuePrint === 1) {
		headerFormat += 'Estimated Revenue,';
	}
	headerFormat += 'Filled Requests,Impressions,Viewable Impressions,Clicks,%s\n';
	let header = util.format(headerFormat,
		schedule.publisherName, completedYmdt, beginDotYmd, endDotYmd, schedule.interval, schedule.keyGroup.name, schedule.creatorName, keysTitle);

	// 제일 첫 줄에 헤더 붙이기
	if (os.platform() == 'win32') {
		// 로컬 개발 환경 용. 파일 사이즈가 클 경우 메모리 이슈가 생길 수 있으므로 주의.
		var data = fs.readFileSync(workingFile);
		var fd = fs.openSync(workingFile, 'w+');
		var buffer = Buffer.from(header.replace(/\\n/g, '\r\n'));
		fs.writeSync(fd, buffer, 0, buffer.length, 0);
		fs.writeSync(fd, data, 0, data.length, buffer.length);
		fs.closeSync(fd);
	} else { // linux
		const stdoutForLocal = child_process.execSync(`sed -i '1 i\\${header}' ${workingFile}`);
		if (_.isNil(stdoutForLocal)) {
			throw Error(`헤더 붙이는 중 에러 발생. ${stdoutForLocal}. workingFile:${workingFile}`);
		}
	}
};

/**
 * 임시파일명을 정식파일명으로 변경
 * @param src
 * @param dest
 */
const _renameFileIfNeed = (src, dest) => {
	if (src.endsWith('.temp')) {
		fs.renameSync(src, dest);
	}
};

/**
 * CSV 파일 준비하기
 *
 * config.report.revenue_sharing.local_root	= /home1/irteam/local_download
 * config.report.path						= /report
 * config.report.revenue_sharing.path		= /revenuesharing
 * filePathWithoutRoot						= /report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc
 * file(최종 파일)							= /home1/irteam/local_download/report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc/5f92bbce4f7489002e6401dc_20220403.csv
 * workingFile(작업중인 파일)				= /home1/irteam/local_download/report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc/5f92bbce4f7489002e6401dc_20220403.csv,
 * 		.csv or .temp
 * fileName									= 5f92bbce4f7489002e6401dc_20220403.csv
 *
 * @param keyGroupId
 * @param interval
 * @param dateForFileName : yyyyymm or yyyymmdd
 * @returns {{filePathWithoutRoot: string, file: (string|*), fileName: string}}
 */
const _prepareFile = async (keyGroupId, interval, dateForFileName) => {
	/*
	 * 파일 경로와 이름 만들기
	 */

	// 수익쉐어리포트루트 : "/home1/irteam/local_download/report/revenuesharing"
	const revenueSharingPath = slash(path.join(config.report.revenue_sharing.local_root, config.report.path, config.report.revenue_sharing.path));

	// 디렉토리 구조: {revenueSharingPath}/{년}/{월}/{keyGroupId}
	const ym = dateForFileName.substring(0, 6);
	let targetDate = moment(ym, 'YYYYMM');
	const howManyMonths = targetDate.diff(moment(), 'months'); // 몇달 전 디렉토리를 만들어야 하는지. 현재가 2월이고 생성할 달이 1월이면 1 - 2  => -1 (1달 전 디렉토리)
	// "/home1/irteam/local_download/report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc"
	const filePath = slash(path.join(fileUtil.getFilePathByMonthly(revenueSharingPath, howManyMonths), keyGroupId));

	// 없으면 만들고
	if (!fs.existsSync(filePath)) {
		await fileUtil.mkdirIfNotExist(filePath);
	}

	// 리포트루트 떼고 DB에 저장할 경로(다운로드 시 필요한 경로임): "/report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc"
	const filePathWithoutRoot = filePath.substring(config.report.revenue_sharing.local_root.length, filePath.length);


	// 파일 이름
	let fileName = keyGroupId + '_' + dateForFileName;
	if (interval == 'MONTHLY') {
		fileName += '_summary'; // 한달치를 한 줄로
	}
	fileName += '.csv'; // 월 또는 일자만

	// 쓰기를 위한 파일 경로(이름포함)
	// "/home1/irteam/local_download/report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc/5f92bbce4f7489002e6401dc_20220403.csv"
	let file = slash(path.join(filePath, fileName));


	/*
	 파일이 이미 존재하면 백업디렉토리로 복사
	 새 파일은 .csv 대신 .temp를 붙인다.
	 workingFile =
	 		"/home1/irteam/local_download/report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc/5f92bbce4f7489002e6401dc_20220403.csv"
	 			or
	 		"/home1/irteam/local_download/report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc/5f92bbce4f7489002e6401dc_20220403.temp"
	 */
	let workingFile;
	if (fs.existsSync(file)) {
		// 기존 파일 백업 디렉토리로 복사
		/*
			path.parse('/home/<USER>/dir/file.txt');
			Returns:{
				root: '/',
			  dir: '/home/<USER>/dir',
			  base: 'file.txt',
			  ext: '.txt',
			  name: 'file'
			}
		 */
		const parsedFile = path.parse(file);
		let backup = path.join(parsedFile.dir, 'backup'); // /home/<USER>/dir/backup

		// backup 디렉토리가 없으면 만들고 for local
		if (!fs.existsSync(backup)) {
			await fileUtil.mkdirIfNotExist(backup);
		}

		// backup 디렉토리가 없으면 만들고 for owfs
		const owfsBackupDir = _.replace(backup, config.report.revenue_sharing.local_root, owfsDownloadAbsoluteRoot);
		if (!fs.existsSync(owfsBackupDir)) {
			await fileUtil.mkdirIfNotExist(owfsBackupDir);
		}

		// 백업 디렉토리로 복사 for local
		const fileStats = fs.statSync(file);
		backup = path.join(backup, parsedFile.base + '.' + moment(fileStats.mtime).format('YYYYMMDD_HHmmss')); // /backup/5bf3709e8399e7cabac24a62_20181218.csv.20181218_115326
		fs.copyFileSync(file, backup);

		// 백업 디렉토리로 복사 for owfs
		console.log(`config.report.revenue_sharing.local_root=${config.report.revenue_sharing.local_root}`);
		console.log(`owfsDownloadAbsoluteRoot=${owfsDownloadAbsoluteRoot}`);
		const owfsBackupFile = _.replace(backup, config.report.revenue_sharing.local_root, owfsDownloadAbsoluteRoot);

		log.debug(`[G-RVN-SHAR] 원본파일백업
			localBackupFile: ${backup}
			owfsBackupFile: ${owfsBackupFile}`);

		fs.copyFileSync(backup, owfsBackupFile);

		// 임시 파일 생성 (.temp)
		workingFile = path.join(parsedFile.dir, parsedFile.name + '.temp'); // '/download/report/revenuesharing/2019/1/5bf3709e8399e7cabac24a62/5bf3709e8399e7cabac24a62_20181218.temp'
	} else {
		// 처음 만들어지는 것이므로 .csv 본래 확장자로 생성
		workingFile = file; // '/download/report/revenuesharing/2019/1/5bf3709e8399e7cabac24a62/5bf3709e8399e7cabac24a62_20181218.csv'
	}

	/*
		# DB에 저장될 경로 - CMS 다운로드 시 사용할 경로 (SummaryRevenueSharingReports.filePath)
		filePathWithoutRoot	= /report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc

		# 스토리지 전체 경로
		file				= /home1/irteam/local_download/report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc/5f92bbce4f7489002e6401dc_20220403.csv

		# 스토리지 전제 경로 - 실제 작업할 파일의 전체 경로
		workingFile			= /home1/irteam/local_download/report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc/5f92bbce4f7489002e6401dc_20220403.csv

		# DB에 저장될 파일명 (SummaryRevenueSharingReports.fileName)
		fileName			= 5f92bbce4f7489002e6401dc_20220403.csv
	 */
	return { filePathWithoutRoot, file, workingFile, fileName };
};

/**
 * publisher
 *    "name" : "리포트연동테스트용1",
 *     "_id" : ObjectId("5bf5159db77f83001f8a4605"),
 *
 * RK 지원하는 통계 쓰기
 * @param schedule
 * @param date    yyyymmdd or yyyymm
 * @param fd
 * @param reportId
 * @param adUnitIds
 * @param beginYmd
 * @param endYmd
 * @returns {Promise.<void>}
 */
const _writeRk = async (schedule, date, fd, reportId, adUnitIds, beginYmd, endYmd) => {
	await _updateReport(reportId, commCd.code.RevenueSharingReportProgress.ONGOING_RK.code);

	let cntInDb = 0, cntInFile = 0, dummyCnt = 0;

	const session = await TempRs.db.startSession({
		'causalConsistency': true,
		'readConcern': 'majority',
		'writeConcern': 'majority'
	});

	// $group 파이프라인의 _id.ymd 조건 설정
	let groupDate;
	if (date.length == 6) { // 월별 조회
		groupDate = { '$substr': ['$ymd', 0, 6] };
	} else { // 일별 조회
		groupDate = date;
	}

	try {
		// AdUnitID 별
		for (const adUnitId of adUnitIds) {
			const extPlaces = await _getExtPlacesByAdUnitId(schedule.publisher_id, STAT_TYPE_ADP_RK, adUnitId, beginYmd, endYmd);

			// 플레이스별
			for (const extPlace of extPlaces) {
				const starttime = moment();

				if (extPlace.adp.rkUse == 0) {
					continue;
				}

				runId = `${baseRunId}  ${date}  adUnitId:${adUnitId}   AP:${extPlace.adp._id}   PLACE:${extPlace.name}(${extPlace._id})  `;
				log.debug(`${runId} 1. RK ...`);

				// 퍼블리셔 설정
				extPlace.publisher_id = schedule.publisher_id;

				const basePipeline = [
					{
						'$match': {
							"ymd": { '$regex': date + '.*' },
							"publisher_id": ObjectId(schedule.publisher_id),
							'adUnitId': adUnitId,
							'adProviderPlace_id': ObjectId(extPlace._id)
						}
					},
					{
						'$group': {
							'_id': {
								'ymd': groupDate,
								'publisher_id': '$publisher_id',
								'adUnitId': '$adUnitId',
								'adProvider_id': '$adProvider_id',
								'rsKeyValue': '$rsKeyValue',
								'adProviderPlaceKey': '$adProviderPlaceKey',
							},
							'imp': { '$sum': '$imp' },
							'clk': { '$sum': '$clk' },
							'netRevenueUSD': { '$sum': '$netRevenueUSD' },
							'gfpNetRevenueUSD': { '$sum': '$gfpNetRevenueUSD' },
							'netRevenueKRW': { '$sum': '$netRevenueKRW' },
							'gfpNetRevenueKRW': { '$sum': '$gfpNetRevenueKRW' },
						}
					},
					{
						'$sort': { '_id.ymd': 1, '_id.adUnitId': 1, '_id.adProvider_id': 1, '_id.adProviderPlaceKey': 1 }
					},
					{
						'$project': {
							'_id': 0,
							'ymd': '$_id.ymd',
							'publisher_id': '$_id.publisher_id',
							'adUnitId': '$_id.adUnitId',
							'adProvider_id': '$_id.adProvider_id',
							'rsKeyValue': '$_id.rsKeyValue',
							'adProviderPlaceKey': '$_id.adProviderPlaceKey',
							'imp': { '$sum': '$imp' },
							'clk': { '$sum': '$clk' },
							'netRevenueUSD': { '$sum': '$netRevenueUSD' },
							'gfpNetRevenueUSD': { '$sum': '$gfpNetRevenueUSD' },
							'netRevenueKRW': { '$sum': '$netRevenueKRW' },
							'gfpNetRevenueKRW': { '$sum': '$gfpNetRevenueKRW' },
						}
					},
					{
						'$out': tempCollectionName
					}
				];
				await AdProviderRkStat.aggregate(basePipeline)
									  .allowDiskUse(true)
									  .readConcern("majority")
									  .session(session);


				// 총 건수 구하기
				const count = await TempRs.countDocuments().session(session);
				if (count < 1) {
					const adProviderPlaceKey = extPlace.adProviderPlaceKey;
					const valueOfKeys = schedule.keyGroup.keys.map((key) => '-').join(',');

					if (!RegExToExceptRowWithAdProviderPlaceKey.test(adProviderPlaceKey)) { // #{ }가 포함되지 않을 때만 쓰기

						const row = util.format(rowFormatWithGfpEstimatedRevenue,
							STAT_TYPE_ADP_RK,
							date,
							_wrapWordWithDoubleQuotation(adUnitId), 			// AdUnit 명
							_wrapWordWithDoubleQuotation(extPlace.adp.name),	// AdProvider 명
							extPlace.adp.timezone,								// AdProvider 타임존
							extPlace.adp.currency,								// AdProvider 기준통화
							_wrapWordWithDoubleQuotation(adProviderPlaceKey),	// AdProvider 플레이스연동키

							'-',												// USD Net Revenue
							'-',												// USD Revenue

							'-', 												// KRW Net Revenue
							'-', 												// KRW Revenue

							'-',												// AdProvider 노출
							'-',												// AdProvider 클릭

							'-', 												// GFP Filled Request Estimated Revenue

							'-', 												// GFP Filled Request
							'-', 												// GFP 노출
							'-', 												// GFP 유효노출
							'-', 												// GFP 클릭

							valueOfKeys 										// rsKeyValue
						);

						try {
							fs.appendFileSync(fd, row, 'utf8');
						} catch (err) {
							throw err;
						}

						dummyCnt++;
					}

					continue;
				} else {
					cntInDb += count;
				}


				// 페이징 구성
				let begin = 1, end = 1, pageIndex = 1;
				let lastId = '';

				while (true) {
					// 페이징 조회
					let stats;
					if (lastId == '') {
						stats = await TempRs.find().sort({ _id: 1 }).session(session).readConcern('majority').limit(PAGE_SIZE);
					} else {
						stats = await TempRs.find({ _id: { '$gt': lastId } }).sort({ _id: 1 }).session(session).readConcern('majority').limit(PAGE_SIZE);
					}

					if (_.isNil(stats) || _.isEmpty(stats)) {
						break;
					}

					end = begin + stats.length - 1;
					lastId = stats[stats.length - 1]._id;


					// 파일에 쓸 문자열 만들기
					const rows = await Promise.all(stats.map(async (model) => {
						// mongoose model에서 document 추출
						const stat = model._doc;

						/*
							// stat

							{
								"_id" : {
									"ymd" : "20181102",
									"publisher_id" : "5b3f2e7a73001509b777c20e",
									"adUnitId" : "dev_adunit_98",
									"adProvider_id" : ObjectId("5b42d1f670aa7d2b005377c2"),
									"adProviderPlaceKey" : "[{\"placeId2\":\"실프프로바이더2-PlaceID2\"},{\"size2\":\"실프프로바이더2-Size2\"},{\"category2\":\"실프프로바이더2-Category2\"}]"
								},
								"imp" : 4974045,
								"clk" : 509121,
								"revenueUSD" : 505290,
								"revenueKRW" : 50485300,
								"count" : 10000.0
							}
						 */
						const valueOfKeys = schedule.keyGroup.keys.map((key) => {
							return (_.isNil(stat.rsKeyValue) || _.isNil(stat.rsKeyValue[key]) || stat.rsKeyValue[key] === '') ? '-' : stat.rsKeyValue[key];
						}).join(',');

						const row = util.format(rowFormatWithGfpEstimatedRevenue,
							STAT_TYPE_ADP_RK,
							stat.ymd,
							_wrapWordWithDoubleQuotation(stat.adUnitId), 					// AdUnit 명
							_wrapWordWithDoubleQuotation(extPlace.adp.name), 				// AdProvider 명
							extPlace.adp.timezone, 											// AdProvider 타임존
							extPlace.adp.currency, 											// AdProvider 기준통화
							_wrapWordWithDoubleQuotation(stat.adProviderPlaceKey),			// AdProvider 플레이스연동키

							_.isNil(stat.gfpNetRevenueUSD) ? '-' : stat.gfpNetRevenueUSD,	// USD Net Revenue
							_.isNil(stat.netRevenueUSD) ? '-' : stat.netRevenueUSD,			// USD Revenue

							_.isNil(stat.gfpNetRevenueKRW) ? '-' : stat.gfpNetRevenueKRW,	// KRW Net Revenue
							_.isNil(stat.netRevenueKRW) ? '-' : stat.netRevenueKRW, 		// KRW Revenue

							stat.imp, 														// AdProvider 노출
							stat.clk, 														// AdProvider 클릭

							'-', 															// GFP Filled Request Estimated Revenue

							'-', 															// GFP Filled Request
							'-', 															// GFP 노출
							'-', 															// GFP 유효노출
							'-', 															// GFP 클릭

							valueOfKeys 													// rsKeyValue
						);

						return row;
					}));

					if (rows.length > 0) {
						cntInFile += rows.length;
					}

					const bulkRow = rows.reduce((accu, cur) => {return accu + cur}, '');

					// 파일에 쓰기
					if (bulkRow.length > 0) {
						try {
							fs.appendFileSync(fd, bulkRow, 'utf8');
						} catch (err) {
							throw err;
						}
					}

					begin = end + 1;
					pageIndex++;
				} // end of paging

				if (count != end) {
					throw new BusinessError({
						code: SBE.Common.Fail,
						message: `${runId} 1. RK 쓰기 실패. DB에서조회된개수(${count}) != 루프동안조회된개수(${end})`
					})
				}

				// 소요시간 기록
				const elapsedTime = moment() - starttime;
				log.debug(`${runId} 1. RK 소요시간(msec): ${elapsedTime} (sec): ${elapsedTime / 1000}`);
			}
		} // end of adUnitId

		await _historyCount('rk', reportId, cntInDb, cntInFile);

		log.debug(`${baseRunId}  ${date}  1. RK cntInDb: ${cntInDb} cntInFile:${cntInFile} dummyCnt:${dummyCnt}`);

		await session.endSession();
	} catch (err) {
		log.error(`${baseRunId}  ${date}  1. RK 에러 발생으로 DB endSession. ${err}`);
		await session.endSession();

		throw err;
	}
};

/**
 * NonRK 통계 쓰기
 *
 * @param schedule
 * @param date
 * @param fd
 * @param reportId
 * @param adUnitIds
 * @param beginYmd
 * @param endYmd
 * @returns {Promise.<number>}
 */
const _writeNonRk = async (schedule, date, fd, reportId, adUnitIds, beginYmd, endYmd) => {
	await _updateReport(reportId, commCd.code.RevenueSharingReportProgress.ONGOING_NONRK.code);

	let cntInDb = 0, cntInFile = 0, dummyCnt = 0;

	const session = await TempRs.db.startSession({
		'causalConsistency': true,
		'readConcern': 'majority',
		'writeConcern': 'majority'
	});

	// $group 파이프라인의 _id.ymd 조건 설정
	let groupDate;
	if (date.length == 6) { // 월별 조회
		groupDate = { '$substr': ['$ymd', 0, 6] };
	} else { // 일별 조회
		groupDate = date;
	}

	// adUnitIds에 따른 플레이스 목록 만들기
	let extPlaces = [];
	for (let adUnitId of adUnitIds) {
		const places = await _getExtPlacesByAdUnitId(schedule.publisher_id, STAT_TYPE_ADP_NONRK, adUnitId, beginYmd, endYmd);
		extPlaces = extPlaces.concat(places);
	}

	/*
		[ adProviderPlaceKey 중복을 제거하는 이유 ]

		NonRK AP는 adProviderPlace_id를 모르기 때문에 adProviderPlaceKey 기준으로 리포트를 연동해 온다.

		헌데 같은 이름의 adProviderPlaceKey는 서로 다른 AP의 여러 adProviderPlace_id에 매핑될 수 있다.

		수익쉐어 리포트에서는 adProviderPlace_id를 기술하지 않고 adProviderPlaceKey만 기술한다.
		또한 존재하지 않는 adProviderPlaceKey에 대한 '-' 처리가 있어야 하기 때문에 미리 adProviderPlaceKey의 존재를 알아야 한다.


		해서 adProviderPlaceKey 별로 루프를 돈다.

		플레이스키 기준으로 필터링하고
				'$match': {
					"ymd": { '$regex': date + '.*' },
					"publisher_id": ObjectId(schedule.publisher_id),
					'adProviderPlaceKey': extPlace.adProviderPlaceKey
				}

		adProvider_id, adProviderPlaceKey로 그룹핑하여 이름이 같은 adProviderPlaceKey라도 서로 다른 AP 통계를 얻어낸다.
			'$group': {
				'_id': {
					'ymd': groupDate,
					'publisher_id': '$publisher_id',
					'adProvider_id': '$adProvider_id',
					'adProviderPlaceKey': '$adProviderPlaceKey',
					'rsKeyValue': '$rsKeyValue',
				}
			},

		이 과정에서 이미 AP 별로 찢어지기 때문에 adProviderPlaceKey의 중복을 미리 제거해야 같은 (adProvider_id + adProviderPlaceKey)가 여러번 나오지 않는다.
	 */
	const uniqueExtPlaces = _.uniqBy(extPlaces, 'adProviderPlaceKey');

	try {
		// 플레이스별 통계 조회
		for (let extPlace of uniqueExtPlaces) {
			const starttime = moment();

			if (extPlace.adp.rkUse == 1) {
				continue;
			}

			runId = `${baseRunId}  ${date}  AP:${extPlace.adp._id}   PLACE:${extPlace.name}(${extPlace.adProviderPlaceKey})  `;
			log.debug(`${runId} 2. NonRK ...`);

			// 퍼블리셔 설정
			extPlace.publisher_id = schedule.publisher_id;

			const basePipeline = [
				{
					'$match': {
						"ymd": { '$regex': date + '.*' },
						"publisher_id": ObjectId(schedule.publisher_id),
						'adProviderPlaceKey': extPlace.adProviderPlaceKey
					}
				},
				{
					'$group': {
						'_id': {
							'ymd': groupDate,
							'publisher_id': '$publisher_id',
							'adProvider_id': '$adProvider_id',
							'adProviderPlaceKey': '$adProviderPlaceKey',
							'rsKeyValue': '$rsKeyValue',
						},
						'imp': { '$sum': '$imp' },
						'clk': { '$sum': '$clk' },
						'netRevenueUSD': { '$sum': '$netRevenueUSD' },
						'gfpNetRevenueUSD': { '$sum': '$gfpNetRevenueUSD' },
						'netRevenueKRW': { '$sum': '$netRevenueKRW' },
						'gfpNetRevenueKRW': { '$sum': '$gfpNetRevenueKRW' },
					}
				},
				{
					'$sort': { '_id.ymd': 1, '_id.adProvider_id': 1, '_id.adProviderPlaceKey': 1 }
				},
				{
					'$project': {
						'_id': 0,
						'ymd': '$_id.ymd',
						'publisher_id': '$_id.publisher_id',
						'adProvider_id': '$_id.adProvider_id',
						'adProviderPlaceKey': '$_id.adProviderPlaceKey',
						'rsKeyValue': '$_id.rsKeyValue',
						'imp': { '$sum': '$imp' },
						'clk': { '$sum': '$clk' },
						'netRevenueUSD': { '$sum': '$netRevenueUSD' },
						'gfpNetRevenueUSD': { '$sum': '$gfpNetRevenueUSD' },
						'netRevenueKRW': { '$sum': '$netRevenueKRW' },
						'gfpNetRevenueKRW': { '$sum': '$gfpNetRevenueKRW' },
					}
				},
				{
					'$out': tempCollectionName
				}
			];
			await AdProviderNonRkStat.aggregate(basePipeline)
									 .allowDiskUse(true)
									 .readConcern("majority")
									 .session(session);


			// Non RK의 key&value 만들기
			const emptyValueOfKeys = schedule.keyGroup.keys.map(() => {
				return '-'
			}).join(',');

			// 총 건수 구하기
			const count = await TempRs.countDocuments().session(session);
			if (count < 1) {

				if (!RegExToExceptRowWithAdProviderPlaceKey.test(extPlace.adProviderPlaceKey)) { // #{ }가 포함되지 않을 때만 쓰기
					const row = util.format(rowFormatWithGfpEstimatedRevenue,
						STAT_TYPE_ADP_NONRK,
						date,
						'-', 														// AdUnit 명
						_wrapWordWithDoubleQuotation(extPlace.adp.name), 			// AdProvider 명
						extPlace.adp.timezone, 										// AdProvider 타임존
						extPlace.adp.currency, 										// AdProvider 기준통화
						_wrapWordWithDoubleQuotation(extPlace.adProviderPlaceKey),	// AdProvider 플레이스연동키

						'-',														// USD Net Revenue
						'-',														// USD Revenue

						'-',														// KRW Net Revenue
						'-',														// KRW Revenue

						'-',														// AdProvider 노출
						'-',														// AdProvider 클릭

						'-', 														// GFP Filled Request Estimated Revenue

						'-', 														// GFP Filled Request
						'-', 														// GFP 노출
						'-', 														// GFP 유효노출
						'-', 														// GFP 클릭

						emptyValueOfKeys // rsKeyValue
					);

					try {
						fs.appendFileSync(fd, row, 'utf8');
					} catch (err) {
						throw err;
					}

					dummyCnt++;
				}

				continue;
			} else {
				cntInDb += count;
			}

			// 페이징 구성
			let begin = 1, end = 1, pageIndex = 1;
			let lastId = '';

			while (true) {
				// 페이징 조회
				let stats;
				if (lastId == '') {
					stats = await TempRs.find().sort({ _id: 1 }).session(session).readConcern('majority').limit(PAGE_SIZE);
				} else {
					stats = await TempRs.find({ _id: { '$gt': lastId } }).sort({ _id: 1 }).session(session).readConcern('majority').limit(PAGE_SIZE);
				}

				if (_.isNil(stats) || _.isEmpty(stats)) {
					break;
				}

				end = begin + stats.length - 1;
				lastId = stats[stats.length - 1]._id;


				// 파일에 쓸 문자열 만들기
				const rows = await Promise.all(stats.map(async (model) => {
					// mongoose model에서 document 추출
					const stat = model._doc;

					/*
						"_id" : {
							"ymd" : "20181102",
							"publisher_id" : ObjectId("5b3f2e7a73001509b777c20e"),
							"adProvider_id" : ObjectId("5b3f2e7a73001509b777c20c"),
							"adProviderPlaceKey" : "channelAlias:m.cafe"
						},
						"imp" : 120,
						"clk" : 12,
						"revenueUSD" : 12,
						"revenueKRW" : 14400,
						"count" : 3.0
					 */

					const valueOfKeys = schedule.keyGroup.keys.map((key) => {
						return (_.isNil(stat.rsKeyValue) || _.isNil(stat.rsKeyValue[key]) || stat.rsKeyValue[key] === '') ? '-' : stat.rsKeyValue[key];
					}).join(',');

					// [DATA] SNOW 매체 리포트 검증 문의 (https://jira.navercorp.com/browse/GFP-1021) 2025.03.18
					// 	- 같은 플레이스키를 쓰는 서로 다른 플레이스의 AP 정보가 각각 남지 않고 하나로 남는 현상에 대한 수정
					//	- 원인: const uniqueExtPlaces = _.uniqBy(extPlaces, 'adProviderPlaceKey'); 코드에 의해 플레이스키 단위로 extPlace가 하나로 줄어 들면서 생기는 현상
					//	- AdProviderCache를 미리 만들어 두고 _id로 조회하여 기술하도록 수정
					const ap = AdProviderCache.get(stat.adProvider_id.toString());

					const row = util.format(rowFormatWithGfpEstimatedRevenue,
						STAT_TYPE_ADP_NONRK,
						stat.ymd,
						'-', 															// AdUnit 명
						_wrapWordWithDoubleQuotation(ap.name), 							// AdProvider 명
						ap.timezone, 													// AdProvider 타임존
						ap.currency, 													// AdProvider 기준통화
						_wrapWordWithDoubleQuotation(stat.adProviderPlaceKey), 			// AdProvider 플레이스연동키

						_.isNil(stat.gfpNetRevenueUSD) ? '-' : stat.gfpNetRevenueUSD,	// USD Net Revenue
						_.isNil(stat.netRevenueUSD) ? '-' : stat.netRevenueUSD,			// USD Revenue

						_.isNil(stat.gfpNetRevenueKRW) ? '-' : stat.gfpNetRevenueKRW,	// KRW Net Revenue
						_.isNil(stat.netRevenueKRW) ? '-' : stat.netRevenueKRW,			// KRW Net Revenue

						stat.imp, 														// AdProvider 노출
						stat.clk, 														// AdProvider 클릭

						'-', 															// GFP Filled Request Estimated Revenue

						'-', 															// GFP Filled Request
						'-', 															// GFP 노출
						'-', 															// GFP 유효노출
						'-', 															// GFP 클릭

						valueOfKeys 													// rsKeyValue
					);

					return row;
				}));

				if (rows.length > 0) {
					cntInFile += rows.length;
				}

				const bulkRow = rows.reduce((accu, cur) => {return accu + cur}, '');

				// 파일에 쓰기
				if (bulkRow.length > 0) {
					try {
						fs.appendFileSync(fd, bulkRow, 'utf8');
					} catch (err) {
						throw err;
					}
				}

				begin = end + 1;
				pageIndex++;
			}

			if (count != end) {
				throw new BusinessError({
					code: SBE.Common.Fail,
					message: `${runId} 2. NonRK 쓰기실패. DB에서조회된개수(${count}) != 루프동안조회된개수(${end})`
				})
			}

			// 소요시간 기록
			const elapsedTime = moment() - starttime;
			log.debug(`${runId} 2. NonRK 소요시간(msec): ${elapsedTime} (sec): ${elapsedTime / 1000}`);
		} // end of extPlaces

		await _historyCount('nonRk', reportId, cntInDb, cntInFile);

		log.debug(`${baseRunId}   ${date}   2. NonRK cntInDb: ${cntInDb} cntInFile:${cntInFile} dummyCnt:${dummyCnt}`);

		await session.endSession();
	} catch (err) {
		log.debug(`${baseRunId}   ${date}   2. NonRK 에러 발생으로 DB endSession.`);
		await session.endSession();

		throw err;
	}
};

/**
 *
 * 리모트 하둡 전체 경로
 			/data/log/gfp/gfp-revenue-sharing-report-v2
			/daily		or	/monthly
			/20200610	or	/202006
			/_publisherId=5bd01269166015001fa8888d
			/_adUnitId=TEST_PCWEB_VIDEO_AD
			/_adProviderId=5d677456e48f98f38
			/_adProviderPlaceId=5dcb74403e0a34002d5e1d9d
			/part-00007-fcef3cf0-db25-461c-af17-9490377b46fb.c000.csv
 *
 * @param schedule
 * @param date
 * @param localFinalFullPath
 * 		.temp아니고 최종 .csv 파일의 전체 경로
 * 		'/home1/irteam/local_download/report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc/5f92bbce4f7489002e6401dc_20220403.csv'
 * @param fd
 * @param reportId
 * @param adUnitIds
 * @returns {Promise<void>}
 * @private
 */
const _writeGfp = async (schedule, date, localFinalFullPath, fd, reportId, adUnitIds, beginYmd, endYmd) => {
	await _updateReport(reportId, commCd.code.RevenueSharingReportProgress.ONGOING_GFP.code);

	let cntInFile = 0, dummyCnt = 0;

	// localFinalFullPath = '/home1/irteam/local_download/report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc/5f92bbce4f7489002e6401dc_20220403.csv'
	// tempDir            = '/home1/irteam/local_download/report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc/5f92bbce4f7489002e6401dc_20220403'
	const parsed = path.parse(localFinalFullPath);
	const tempDir = path.join(parsed.dir, parsed.name); // c3로부터 다운로드 받기 위한 임시 경로
	log.debug(`................... tempDir = ${tempDir}`);

	// adUnitId 별
	for (const adUnitId of adUnitIds) {
		// 리모트 하둡 광고유닛 경로
		const remoteAuDir = slash(path.join(
			config.report.revenue_sharing.hdfs.root_dir,	// '/user/gfp-data/data/log/gfp/gfp-revenue-sharing-report-v2'
			schedule.interval.toLowerCase(),				// '/monthly' or '/daily'
			date,											// '/yyyymmdd', '/yyyymm'
			'_publisherId=' + schedule.publisher_id,		// '/_publisherId={publisherId}'
			'_adUnitId=' + adUnitId,						// '/_adUnitId={_adUnitId}'
		));

		// 로컬 광고유닛 다운로드 경로
		// '/home1/irteam/local_download/report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc/5f92bbce4f7489002e6401dc_20220403/adUnitId={adUnitId}}'
		const localAuDir = path.join(tempDir, `_adUnitId=${adUnitId}`);

		log.debug(`................... remoteAuDir = ${remoteAuDir}`);
		log.debug(`...................  localAuDir = ${localAuDir}`);

		// C3로부터 adUnitId 단위로 다운로드
		if (await c3HdfsApi.exists(remoteAuDir)) {
			await _downloadFromC3(remoteAuDir, localAuDir);
		}

		// 플레이스별
		const extPlaces = await _getExtPlacesByAdUnitId(schedule.publisher_id, STAT_TYPE_NAM, adUnitId, beginYmd, endYmd);
		for (const extPlace of extPlaces) {
			const starttime = moment();

			const runId = `${baseRunId}   ${date}   adUnitId:${adUnitId}   AP:${extPlace.adp._id}   PLACE:${extPlace.name}(${extPlace._id})  `;
			log.debug(`${runId} 3. GFP ...`);

			// 퍼블리셔 설정
			extPlace.publisher_id = schedule.publisher_id;

			// 리모트 하둡 플레이스 경로
			const remotePlaceDir = slash(path.join(
				remoteAuDir,
				'_adProviderId=' + extPlace.adp._id,			// '/_adProviderId={adProviderId}'
				'_adProviderPlaceId=' + extPlace._id			// '/_adProviderPlaceId={_adProviderPlaceId}'
			));

			// 로컬 플레이스 경로
			const localPlaceDir = path.join(localAuDir,
				'_adProviderId=' + extPlace.adp._id,			// '/_adProviderId={adProviderId}'
				'_adProviderPlaceId=' + extPlace._id			// '/_adProviderPlaceId={_adProviderPlaceId}'
			);

			log.debug(`................... remotePlaceDir = ${remotePlaceDir}`);
			log.debug(`...................  localPlaceDir = ${localPlaceDir}`);

			// 로컬 플레이스 경로가 존재하는지
			if (!fs.existsSync(`${localPlaceDir}`)) { // 3. GFP  플레이스에 해당하는 로컬 파일 없음.
				log.debug('3. GFP  플레이스에 해당하는 로컬 파일 없음.');

				if (!RegExToExceptRowWithAdProviderPlaceKey.test(extPlace.adProviderPlaceKey)) { // #{ }가 포함되지 않을 때만 쓰기
					_writeDummyGfpStat(schedule.keyGroup.keys, schedule.estimatedRevenuePrint, date, fd, reportId, runId, adUnitId, extPlace);
					dummyCnt++;
					cntInFile++;
				}
			} else {
				const fileNameList = fs.readdirSync(localPlaceDir);
				log.debug(`로컬 플레이스 경로 하위에 있는 avro 파일 개수:${fileNameList.length}`);
				for (const fileName of fileNameList) { // 플레이스 하나에 여러개의 avro 파일이 있을 수 있음
					const localPathToRead = path.join(localPlaceDir, fileName)
					log.debug(`로컬 플레이스 경로 하위에 있는 avro 파일:${localPathToRead}`);
					const { statsLength } = await _processBizCloudFile(schedule, date, fd, reportId, runId, adUnitId, extPlace, localPathToRead);

					if (statsLength < 1) {
						if (!RegExToExceptRowWithAdProviderPlaceKey.test(extPlace.adProviderPlaceKey)) { // #{ }가 포함되지 않을 때만 쓰기
							_writeDummyGfpStat(schedule.keyGroup.keys, schedule.estimatedRevenuePrint, date, fd, reportId, runId, adUnitId, extPlace);
							dummyCnt++;
							cntInFile++;
							log.debug(`${runId} 3. GFP  플레이스에 해당하는 리모트 파일 안에 내용 없음.`);
						}
					} else {
						cntInFile += statsLength;
					}
				}
			}

			// 플레이스 처리시간 기록
			const elapsedTime = moment() - starttime;
			log.debug(`${runId} 3. GFP placeDir:${remotePlaceDir} 소요시간(msec): ${elapsedTime} (sec): ${elapsedTime / 1000}`);
		}
	}

	_rmTempDir(tempDir);

	await _historyCount('gfp', reportId, cntInFile - dummyCnt, cntInFile - dummyCnt);

	log.debug(`${baseRunId}   ${date}   3. GFP. cntInFile:${cntInFile} dummyCnt:${dummyCnt}`);
};

/**
 * C3로부터 파일 다운로드
 * @param remoteAuDir
 * 		"/data/log/gfp/gfp-revenue-sharing-report-v2/daily/20230701/_publisherId=5be280a6ea6ccd00196b47f1/_adUnitId=AOS_BAND_TOP_CHAT_N"
 * @param localAuDir
 * 		"/home1/irteam/local_download\report\revenuesharing2\2023\07\6458614e5fffe20024ba9479\6458614e5fffe20024ba9479_20230701\_adUnitId=AOS_BAND_TOP_CHAT_N"
 * @returns {Promise<void>}
 * @private
 */
const _downloadFromC3 = async (remoteAuDir, localAuDir) => {
	fileUtil.mkdirIfNotExist(localAuDir);
	await c3HdfsCli.download(remoteAuDir, localAuDir);
}

const _writeDummyGfpStat = (keys, estimatedRevenuePrint, date, fd, reportId, runId, adUnitId, extPlace) => {
	const stat = {
		"publisherId": extPlace.publisher_id,
		"placeKey": extPlace.adProviderPlaceKey,
		"rsKeyValue": "",
		"adProviderPlaceId": extPlace._id,

		"filledRequest": '-',
		"imp": '-',
		"viewableImp": '-',
		"clk": '-',

		"adUnitId": extPlace.adu.adUnitId,
		"adProviderId": extPlace.adp._id
	};

	if (estimatedRevenuePrint === 1) {
		stat['bidPriceForCreativeType'] = '-';
	}

	const { row } = _getRowForGfp(keys, estimatedRevenuePrint, date, adUnitId, extPlace, stat);

	// 파일에 쓰기
	try {
		fs.appendFileSync(fd, row, 'utf8');
	} catch (err) {
		throw err;
	}
};

/**
 * 1. 리모트 HDFS에 있는 avro 파일을 읽어
 * 2. 로컬에 저장하고
 * 3. 로컬파일을 읽어 csv에 쓴다.
 *
 * @param schedule
 * @param date
 * @param fd
 * @param reportId
 * @param runId
 * @param adUnitId
 * @param extPlace
 * @param localPathToRead
 * @returns {Promise<unknown>}
 * @private
 */
const _processBizCloudFile = (schedule, date, fd, reportId, runId, adUnitId, extPlace, localPathToRead) => {
	return new Promise((resolve, reject) => {
		let totalStatsLength = 0;

		// ------------ 로컬 파일 읽기 스트림
		const localReadStream = fs.createReadStream(localPathToRead)
								  .on('error', function onError(err) {
									  log.error(`${runId} 3. GFP  로컬파일 읽기 "error" event. ${localPathToRead} ${err}`);
									  reject(err);
								  });


		// ------------ csv로 읽기 스트림
		const parser = csvstream.createStream(
			{ escapeChar: '\\', enclosedChar: '"' }
		);
		parser
			.on('finish', () => {
				// log.debug(`${runId} 3. GFP  로컬파일읽기. "finish" 이벤트.`);
			})
			.on('end', () => {
				localReadStream.destroy();
			})
			.on('error', err => {
				log.error(`${runId} 3. GFP 로컬파일읽기. "error" 이벤트. ${err.stack}`);

				parser.end();
				localReadStream.destroy();

				reject(err);
			});


		// ------------ 데이터 변환 스트림
		const transformer = csv.transform(function (data) {
			const { row } = _getRowForGfp(schedule.keyGroup.keys, schedule.estimatedRevenuePrint, date, adUnitId, extPlace, data);

			totalStatsLength++;

			return row;
		});


		// ------------ 쓰기 스트림
		const writeStream = fs.createWriteStream('', { flags: 'a', autoClose: false, fd: fd, encoding: 'utf8' });
		writeStream
			// .on('end', () => { log.debug(`리포트 writeStream "end" 이벤트.`);})
			// .on('close', () => { log.debug(`리포트 writeStream "close" 이벤트.`);})
			.on('finish', () => {
				// log.debug(`리포트 writeStream "finish" 이벤트.`);
				resolve({ statsLength: totalStatsLength });
			});
		writeStream.on('error', err => {
			log.debug(`${runId}\n\twriteStream "error" 이벤트.`);
			log.error(`${runId} 3. GFP 로컬파일 쓰기 에러. readFile:${localPathToRead} "error" 이벤트. ${err.stack}`);

			localReadStream.destroy();

			reject(err);
		});


		// ------------ 스트림 연결
		if (localPathToRead.endsWith('.gz')) {
			localReadStream.pipe(zlib.createGunzip()).pipe(parser).pipe(transformer).pipe(writeStream);
		} else {
			localReadStream.pipe(parser).pipe(transformer).pipe(writeStream);
		}
	});
};

const _getRowForGfp = (keys, estimatedRevenuePrint, date, adUnitId, extPlace, stat) => {
	// 파일에 쓸 문자열 만들기
	const rsKeyValue = {};
	const tokens = _.split(stat.rsKeyValue, ',');
	tokens.forEach(token => {
		const kv = _.split(token, ':');
		rsKeyValue[kv[0]] = kv[1];
	});

	// 키그룹의 Key에 따른 값 설정
	const valueOfKeys = keys.map((key) => {
		return (_.isNil(rsKeyValue) || _.isNil(rsKeyValue[key]) || rsKeyValue[key] === '') ? '-' : rsKeyValue[key];
	}).join(',');

	// adProviderPlaceKey 설정
	let placeKey = stat.placeKey;
	if (_.isNil(placeKey)) {
		placeKey = ''; // placeKey가 null일 수 있음
	} else {
		placeKey = placeKey.replace(/"/g, '""');
	}

	const row = util.format(rowFormatWithGfpEstimatedRevenue,
		STAT_TYPE_NAM,
		date,												// 날짜
		_wrapWordWithDoubleQuotation(stat.adUnitId), 		// AdUnitId
		_wrapWordWithDoubleQuotation(extPlace.adp.name),	// AdProvider 명
		extPlace.adp.timezone, 								// AdProvider 타임존
		extPlace.adp.currency, 								// AdProvider 기준통화
		'"' + placeKey + '"', 								// AdProvider 플레이스연동키

		'-', 												// USD Net Revenue
		'-', 												// USD Revenue

		'-', 												// KRW Net Revenue
		'-', 												// KRW Revenue

		'-', 												// AdProvider 노출
		'-', 												// AdProvider 클릭

		/*
			- bidPriceXx는 RevenueSharingAggregator에서 scala.math.BigDecimal(precision=38, scale=18)로 처리했기 때문에 최대한 정확도를 높여서 합산된 값임
			- scala.math.BigDecimal(precision, scale)
				- precision : 전체 자리수(정수부 자리수 + 소수부 자리수)
				- scale: 소수점 몇 째자리 까지 표현할 수 있는지(소수점 이하 자리수)
			- bidPriceXx는 ecpm이므로 1000으로 나누어서 최종적으로는 double(소수점 최대 15자리)로 처리함
			- 참고
				- exponent: 소수를 표현하는 방법(지수 표기법)
					- 1.23E5  = 123000
					- 1.23E-5 = 0.0000123
		 */
		_.isNaN(parseFloat(stat.bidPriceForCreativeType)) ? '-' : new Decimal(stat.bidPriceForCreativeType) / 1000, 	// GFP CreativeType Estimated Revenue

		_.isNil(stat.filledRequest) ? '-' : stat.filledRequest, // GFP Filled Request	(eventId = 1)
		stat.imp, 												// GFP 노출				(eventId = 2 + 11)
		stat.viewableImp, 										// GFP 유효노출			(eventId = 2 + 12)
		stat.clk, 												// GFP 클릭				(eventId = 3)

		valueOfKeys 											// rsKeyValue
	);

	return { row };
};

const _rmTempDir = (tempDir) => {
	try {
		if (fs.existsSync(tempDir)) {
			if (os.platform() == 'win32') {
				child_process.execSync(`rmdir /s /q "${tempDir}"`);
			} else {
				child_process.execSync(`rm -rf ${tempDir}`, { encoding: 'utf8' });
			}
		}
	} catch (err) {
		log.error(err.stack);
		throw err;
	}
};

/**
 * 리포트 메타 데이터 생성
 * @param reortId
 * @param file : '/home1/irteam/local_download/report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc/5f92bbce4f7489002e6401dc_20220403.csv'
 * @returns {Promise.<void>}
 */
const _startReport = async (reportId, file) => {
	const nubesFile = _getFileForNubes(file); // '/revenuesharing/2022/04/5f92bbce4f7489002e6401dc/5f92bbce4f7489002e6401dc_20220403.csv'
	const result = path.parse(nubesFile);
	await SummaryRevenueSharingReport.findByIdAndUpdate(reportId, {
		'$set': {
			'filePath': result.dir,
			'fileName': result.base,
			'progress': commCd.code.RevenueSharingReportProgress.START.code,
			'begunAt': moment(),
			'modifiedAt': moment()
		}
	});
};

/**
 * 리포트 진행상태 업데이트 (사용자 용)
 * @param reportId
 * @param progress
 * @returns {Promise.<void>}
 */
const _updateReport = async (reportId, progress) => {
	const report = {
		'progress': progress,
		'modifiedAt': moment()
	};

	await SummaryRevenueSharingReport.findByIdAndUpdate(reportId, { '$set': report });
};

/**
 * 리포트 생성 완료
 * @param reportId
 * @returns {Promise.<void>}
 */
const _completeReport = async (reportId) => {
	// 리포트 완료일시 업데이트
	const report = {
		'progress': commCd.code.RevenueSharingReportProgress.COMPLETE.code,
		'completedAt': moment(),
		'modifiedAt': moment()
	};
	await SummaryRevenueSharingReport.findByIdAndUpdate(reportId, { '$set': report });

	// 스케줄에 최종 완료일시 업데이트
	const reportModel = await SummaryRevenueSharingReport.findById(reportId, { summaryRevenueSharingSchedule_id: 1 });
	await SummaryRevenueSharingSchedule.findByIdAndUpdate(reportModel.summaryRevenueSharingSchedule_id, { '$set': { 'lastCompletedAt': Date.now() } });
};


/**
 * 키그룹에 묶인 AdUnit ID 목록 조회
 * @param keyGroupId
 * @returns {Promise.<*>}
 */
const _getAdUnitIdsByKeyGroupId = module.exports.getAdUnitIdsByKeyGroupId = async (keyGroupId) => {
	const models = await AdUnit.find({ rsKeyGroup_id: keyGroupId }).select('-_id adUnitId');
	return models.map(model => model.adUnitId);
};

/**
 * AdUnit에 묶인 플레이스 목록 조회
 * @param keyGroupId
 * @returns {Promise.<*>}
 */
const _getExtPlacesByAdUnitId = module.exports.getExtPlacesByAdUnitId = async (publisher_id, statsType, adUnitId, beginYmd, endYmd) => {
	// (1) -- MappingHistory에 있는 광고유닛 & 플레이스 관계 조회
	let place_ids = await _getPlaceIdsThroughMappingHistory(adUnitId);
	// log.debug(`${baseRunId} _getExtPlacesByAdUnitId() :: adUnitId=${adUnitId} MappingHistory에서 가져온 플레이스 length=${place_ids.length} place_ids=${JSON.stringify(place_ids, null, 1)}`);


	// (2) -- Silvergrey 통계에서 광고유닛 & 플레이스 관계 조회
	// [Data] PlaceKey가 존재하지 않는 Place 대응 (https://jira.navercorp.com/browse/GFP-677) - MpppingHistory에 없는 플레이스 처리
	// 이 매체가 'NAVER_DIRECT'를 사용하고 RK, GFP 타입인 경우에만 실버그레이에서 조회
	if (AP_IDS_FOR_NAVER_DIRECT.length > 0 && (statsType == STAT_TYPE_ADP_RK || statsType == STAT_TYPE_NAM)) {
		const place_ids2 = await _getPlaceIdsThroughSilvergrey(publisher_id, adUnitId, beginYmd, endYmd);
		// log.debug(`${baseRunId} _getExtPlacesByAdUnitId() :: adUnitId=${adUnitId} 실버그레이에서 가져온 플레이스 length=${place_ids2.length} place_ids=${JSON.stringify(place_ids2, null, 1)}`);

		place_ids = place_ids.concat(place_ids2)
		place_ids = _.uniqBy(place_ids, '_id'); // _id 기준으로 중복 제거
	}


	// (3) -- adUnitI에 엮인 플레이스 확장 정보 조회
	const adUnit = await AdUnit.findOne({ "adUnitId": adUnitId }).select('_id name adUnitId revenueShareUse creativeTypes');
	const extPlaces = await _getPlaces(adUnit, place_ids);
	// log.debug(`${baseRunId} _getExtPlacesByAdUnitId() :: adUnitId=${adUnit.adUnitId} ap=${extPlaces.map(p => p.adp.name)} (MappingHistory + Silvergrey) length=${extPlaces.length} places=${JSON.stringify(extPlaces, null, 2)}`);


	return extPlaces;
};

const _getPlaceIdsThroughMappingHistory = async (adUnitId) => {
	// 매핑히스토리에서 AdUnit에 연결된 플레이스 정보 조회
	const hists = await MappingHistory.find({ 'adUnitId': adUnitId }).select('adProviderPlace_id');
	const place_ids = hists.map(hist => hist.adProviderPlace_id);
	return place_ids;
};

const _getPlaceIdsThroughSilvergrey = async (publisherId, adUnitId, beginYmd, endYmd) => {
	const pipeline = [
		{
			'$match': {
				'publisher_id': ObjectId(publisherId),
				'adProvider_id': { '$in': AP_IDS_FOR_NAVER_DIRECT.map(id => ObjectId(id)) },
				'adUnitId': adUnitId,
				'ymd': { '$gte': beginYmd, '$lte': endYmd }
			}
		},
		{
			'$project': {
				'adProviderPlace_id': 1,
			}
		},
		{
			'$group': {
				'_id': '$adProviderPlace_id'
			}
		}
	];

	// log.debug(`${baseRunId} _getPlaceIdsThroughSilvergrey() :: adUnitId=${adUnitId} statsType=${statsType} pipelineRk=${JSON.stringify(pipeline, null, 2)}`);
	const place_ids = await AdProviderRkStat.aggregate(pipeline);

	return place_ids.map(place => place._id);
};

const _getPlaces = async (adUnit, place_ids) => {
	const places = await Place.find({ _id: { '$in': place_ids } }).select('_id name adProviderInfo_id placeInfos placeKey')
							  .populate(
								  {
									  path: 'adProviderInfo_id',
									  select: '_id adProvider_id',
									  populate: {
										  path: 'adProvider_id',
										  select: '_id name timezone currency placeTemplates reportApi.rkUse',
									  }
								  }
							  );
	const extPlaces = places.map(place => _newExtPlace(adUnit, place));
	return extPlaces;
};

const _newExtPlace = (adUnit, place) => {
	return {
		'_id': place._id,
		'name': place.name,
		'placeInfos': place.placeInfos,
		'adProviderPlaceKey': place.placeKey,
		'adu': {
			'_id': adUnit._id,
			'name': adUnit.name,
			'adUnitId': adUnit.adUnitId,
		},
		'adp': {
			'_id': place.adProviderInfo_id.adProvider_id._id,
			'name': place.adProviderInfo_id.adProvider_id.name,
			'placeTemplates': place.adProviderInfo_id.adProvider_id.placeTemplates,
			'timezone': place.adProviderInfo_id.adProvider_id.timezone,
			'currency': place.adProviderInfo_id.adProvider_id.currency,

			// ※ mongoose의 2 레벨 select는 Ojbect가 아니라 mongoose document이므로 _doc을 통해 Object를 가져와야 함.
			'rkUse': place.adProviderInfo_id.adProvider_id._doc.reportApi.rkUse
		}
	};
};

/**
 * csv 파일에서 콤마를 포함한 단어를 한 셀에 넣기 위해 큰 따옴표를 묶음
 * @param word
 * @returns {string}
 */
const _wrapWordWithDoubleQuotation = (word) => {
	return '"' + word.replace(/"/g, '""') + '"';
};

/**
 * 임시 컬렉션 모델 생성
 * @param tempCollection
 */
const _createTempRsModel = (tempCollection) => {
	let scheme = new mongoose.Schema({
			'_id': {
				'ymd': String,
				'publisher_id': {
					type: mongoose.Schema.Types.ObjectId,
					ref: 'Publishers'
				},
				'adUnitId': String,
				'adUnitIds': [{ type: String }],
				'adProvider_id': {
					type: mongoose.Schema.Types.ObjectId,
					ref: 'AdProviders'
				},
				'rsKeyValue': String,
				'adProviderPlaceKey': String,
			},
			'imp': Number,
			'clk': Number,
			'netRevenueUSD': mongoose.Schema.Types.Decimal128,
			'gfpNetRevenueUSD': mongoose.Schema.Types.Decimal128,
			'netRevenueKRW': mongoose.Schema.Types.Decimal128,
			'gfpNetRevenueKRW': mongoose.Schema.Types.Decimal128
		},
		{
			writeConcern: {
				w: 'majority',
				j: true,
				wtimeout: 5000
			}
		}
	);

	return getDatabase().db.model(tempCollection, scheme, tempCollection);
};

/**
 * SummaryRevenueSharingReport에 작성이 끝난 리포트의 DB건수, 파일내건수 기록
 *
 * @param type
 * @param reportId
 * @param cntInDb
 * @param cntInFile
 * @returns {Promise.<void>}
 * @private
 */
const _historyCount = async (type, reportId, cntInDb, cntInFile) => {
	const set = {};
	set[type + 'CntInDb'] = cntInDb;
	set[type + 'CntInFile'] = cntInFile;
	set['modifiedAt'] = moment();

	const doc = {
		'$set': set
	};

	await SummaryRevenueSharingReport.findByIdAndUpdate(reportId,
		doc, { upsert: true });
};

/**
 * DB건수와 파일건수 비교 결과 파일(.cnt) 떨굼
 *
 * @param reportId
 * @returns {Promise.<void>}
 * @private
 */
const _writeCountAtValidationFile = async (schedule, reportId, csvFileFullPath) => {
	try {
		// 기준치 가져오기
		const env = await Environments.findOne({ 'name': 'revenue-sharing-count-limit-for-notification' }, { 'value': 1 });
		const cntCriteria = parseInt(env.value); // default 10

		// 건수 조회
		const report = await SummaryRevenueSharingReport.findOne(
			{ _id: reportId },
			{
				"rkCntInDb": 1,
				"rkCntInFile": 1,
				"nonRkCntInDb": 1,
				"nonRkCntInFile": 1,
				"gfpCntInDb": 1,
				"gfpCntInFile": 1,
				'filePath': 1,
				'fileName': 1,
			});

		// 기준값 이상인지
		let isValid = true;
		if (Math.abs(report.rkCntInDb - report.rkCntInFile) > cntCriteria) {
			isValid = false;
		}
		if (Math.abs(report.nonRkCntInDb - report.nonRkCntInFile) > cntCriteria) {
			isValid = false;
		}
		if (Math.abs(report.gfpCntInDb - report.gfpCntInFile) > cntCriteria) {
			isValid = false;
		}

		// .cnt 파일 열고
		const idx = csvFileFullPath.lastIndexOf('.');
		const validationFileName = csvFileFullPath.substring(0, idx) + '.cnt'; // .csv -> .cnt 파일 이름 설정
		const fda = fs.openSync(validationFileName, 'w'); // Open file for writing. The file is created (if it does not exist) or truncated (if it exists).

		// .cnt 파일에 정상여부 쓰고
		try {
			const word = isValid ? 'success' : 'failure';
			fs.appendFileSync(fda, word, 'utf8');
		} catch (err) {
			throw err;
		}

		// 파일 닫기
		if (fda !== undefined) {
			fs.closeSync(fda);
		}

		// works messenger로 알리기
		if (!isValid) {
			const publisher = await Publisher.findById(schedule.publisher_id);
			log.error(`[G-RVN-SHAR] '${schedule.name}(${schedule._id})' DB건수와 파일에 쓴 건수가 다름.
	publisher: ${publisher.name}(${schedule.publisher_id})
	rsKeyGroup: ${schedule.keyGroup.name}(${schedule.keyGroup_id})
	schedule: ${schedule.name}(${schedule._id})
	reportId: ${reportId}
	rkCnt:		InDb(${report.rkCntInDb})		InFile(${report.rkCntInFile})
	nonRkCnt:	InDb(${report.nonRkCntInDb})		InFile(${report.nonRkCntInFile})
	gfpCnt:	InDb(${report.gfpCntInDb})		InFile(${report.gfpCntInFile})`);
		}
	} catch (err) {
		log.error(`[G-RVN-SHAR] revenue-sharing-report-csv-nam-child.service._writeCountAtValidationFile() :: ${err.stack}`);
		throw err;
	}
};

/**
 * 로컬 파일을 OWFS로 복사
 * @param csvFileForLocal
 * @returns {Promise<void>}
 * @private
 */
const _copyToOwfs = async (csvFileForLocal) => {
	csvFileForLocal = csvFileForLocal.replace(/\\/g, path.sep);
	let csvFileForOwfs = _.replace(csvFileForLocal, slash(config.report.revenue_sharing.local_root), owfsDownloadAbsoluteRoot);

	// owfs에 디렉토리가 없으면 생성
	const owfsDir = csvFileForOwfs.substring(0, csvFileForOwfs.lastIndexOf(path.sep)); // 파일이름까지 포함되어 있으므로 파일 이름은 제거하고
	// log.debug('csvFileForOwfs:', csvFileForOwfs, 'owfsDir:', owfsDir);
	fileUtil.mkdirIfNotExist(owfsDir);

	// .csv 파일 복사
	try {
		// 	log.debug(`[G-RVN-SHAR] ------------- OWFS로 복사중(.csv)
		// 원본파일:${csvFileForLocal}
		// 목적파일:${csvFileForOwfs}`);
		await fs.copyFileSync(csvFileForLocal, csvFileForOwfs);
		// log.debug('[G-RVN-SHAR] ------------- OWFS로 복사 완료(.csv)');
	} catch (err) {
		throw err;
	}

	// .cnt 파일 복사
	const cntFileForLocal = _.replace(csvFileForLocal, '.csv', '.cnt');
	let cntFileForOwfs = _.replace(cntFileForLocal, slash(config.report.revenue_sharing.local_root), owfsDownloadAbsoluteRoot);
	try {
		// log.debug(`[G-RVN-SHAR] ------------- OWFS로 복사중(.cnt)
		// 원본파일:${cntFileForLocal}
		// 목적파일:${cntFileForOwfs}`);
		await fs.copyFileSync(cntFileForLocal, cntFileForOwfs);
		// log.debug('[G-RVN-SHAR] ------------- OWFS로 복사 완료(.cnt)');
	} catch (err) {
		throw err;
	}

	return {
		csvFileForLocal: csvFileForLocal,
		csvFileForOwfs: csvFileForOwfs
	}
};

const _validateOwfs = async ({ csvFileForLocal, csvFileForOwfs }) => {
	_validateFileSize({ csvFileForLocal, csvFileForOwfs });
};

/**
 * 파일 사이즈 비교
 * @param csvFileForLocal
 * @param csvFileForOwfs
 * @private
 */
const _validateFileSize = ({ csvFileForLocal, csvFileForOwfs }) => {
	// 로컬 파일 사이즈
	const statsForLocal = fs.statSync(csvFileForLocal);
	const fileSizeInBytesForLocal = statsForLocal["size"];

	// OWFS 파일 사이즈
	const statsForOwfs = fs.statSync(csvFileForOwfs);
	const fileSizeInBytesForOwfs = statsForOwfs["size"];

	// 비교
	const msgForSize = `local:${csvFileForLocal}(bytes:${fileSizeInBytesForLocal}) owfs:${csvFileForOwfs}(bytes:${fileSizeInBytesForOwfs})`;
	if (fileSizeInBytesForLocal != fileSizeInBytesForOwfs) {
		throw Error(`로컬 vs OWFS 파일 사이즈 불일치. ${msgForSize}`);
	} else {
		// log.debug(`로컬 vs OWFS 파일 사이즈 일치. ${msgForSize}`);
	}
};

/**
 * AdProvider의 시간을 GFP 시간으로 전환
 *
 * @param beginYmd	yyyymmdd
 * @param endYmd	yyyymmdd
 * @param adProviderTimezone
 * @returns {{since, until}}
 */
module.exports.convertToGfpTimezone = (beginYmd, endYmd, adProviderTimezone) => {
	const adpBeginTime = moment.tz(beginYmd, 'YYYYMMDD', adProviderTimezone);
	const adpEndTime = moment.tz(endYmd, 'YYYYMMDD', adProviderTimezone);

	const since = moment(adpBeginTime).tz('Asia/Seoul').format('YYYYMMDDHH');
	const until = moment(adpEndTime).tz('Asia/Seoul').format('YYYYMMDDHH');

	// console.log(`
	// 	adpTime:${adpBeginTime.format('YYYYMMDDHH')} ~ ${adpEndTime.format('YYYYMMDDHH')}
	// 	gfpTime:${since} ~ ${until}`);

	return {
		since: since,
		until: until
	}
};

/**
 * @param ms
 * @returns {Promise<unknown>}
 * @private
 */
const _sleep = (ms) => {
	return new Promise(resolve => {
		setTimeout(resolve, ms)
	})
};

/**
 * NUBES 업로드
 * @param filePathWithoutRoot
 * @param fileName
 * @returns {Promise<void>}
 * @private
 */
const _uploadToNubes = async (file) => {
	const batchSize = 2000;

	const monitor = {
		stream: new PassThrough({ objectMode: true, highWaterMark: batchSize }),
		start: null,
		latency: 0,
		rows: 0,
	};
	monitor.stream
		   .on('pipe', () => {
			   monitor.start = moment();
		   })
		   .on('data', () => {
			   if (monitor.rows === 0) {
				   monitor.latency = moment().diff(monitor.start);
			   }
			   monitor.rows++;
		   });

	const fileReadStream = fs.createReadStream(file)
							 .on('end', () => {
								 // log.debug(`${runId} ${fileName} 3. GFP  리모트파일읽기 "end" event.`);
							 })
							 .on('finish', async function onFinish() {
								 // log.debug(`${runId} ${fileName} 3. GFP  리모트파일읽기 "finish" event.`);
							 })
							 .on('error', function onError(err) {
								 log.error(`${runId} NUBES에 업로드할 로컬파일읽기 "error" event. ${file} ${err}`);
							 });

	const nubesStream = fileReadStream
		.pipe(monitor.stream)
	;

	/*
	filePathWithoutRoot	= /report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc
	file				= /home1/irteam/local_download/report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc/5f92bbce4f7489002e6401dc_20220403.csv
	workingFile			= /home1/irteam/local_download/report/revenuesharing/2022/04/5f92bbce4f7489002e6401dc/5f92bbce4f7489002e6401dc_20220403.csv
	fileName			= 5f92bbce4f7489002e6401dc_20220403.csv
	 */
	const fileForNubes = _getFileForNubes(file);
	const nubesClient = new NubesClient.Client(config.nubes.nam_api.gatewayAddress, config.nubes.nam_api.bucket);
	log.debug(`................ nubesFile:${fileForNubes}`);

	await nubesClient.upload(nubesStream, fileForNubes, { overwrite: true });
	log.debug(`save csv report: ${fileForNubes}: latency=${monitor.latency}, rows=${monitor.rows}`);
};

/**
 * NAM의 경우 /home1/irteam/local_download/report/revenuesharing'에서 '/home1/irteam/local_download/report'를 떼고 저장한다.
 *
 * @param file
 * @returns {string}
 * @private
 */
const _getFileForNubes = (file) => {
	const dirToDel = slash(path.join(config.report.revenue_sharing.local_root, config.report.path));
	const nubesFile = file.substring(dirToDel.length, file.length);
	return nubesFile;
};
