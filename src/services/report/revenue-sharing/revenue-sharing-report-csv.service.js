'use strict';

import _ from 'lodash';
import {fork} from 'child_process';
import mongoose from 'mongoose';
import moment from 'moment-timezone';
import os from 'os';
import parallelLimit from 'run-parallel-limit';
import path from 'path';

import {AdUnit} from "../../../models/ad-unit.schema";
import {AdProviderInfo} from "../../../models/ad-provider-infos.schema";
import {BatchReportJobSchedule} from "../../../models/batch-report-job-schedule.schema";
import {BusinessError} from '../../../common/error';
import commCd from '@ssp/ssp-common-code';
import extendedLogger from '../../../utils/logger-ext.util';
import * as mailer from '../../../utils/mail.util';
import {Publisher} from "../../../models/publishers.schema";
import SBE from '../../../common/error-code';
import {
	SummaryRevenueSharingGfpStatsSchedule
} from '../../../models/summary-revenue-sharing-gfp-stats-schedules.schema';
import {SummaryRevenueSharingSchedule} from '../../../models/summary-revenue-sharing-schedules.schema';
import {SummaryRevenueSharingReport} from "../../../models/summary-revenue-sharing-reports.schema";
import {RevenueSharingRetryForAdProviderStat} from '../../../models/revenue-sharing-retry-for-adprovider-stats.schema';
import {MappingHistory} from "../../../models/mapping-history.schema";
import {AdProvider} from "../../../models/ad-providers.schema";
import {Environments} from "../../../models/environments.schema";

import {DataEnvironments} from '../../../models/data/data-environments.schema';

let ObjectId = mongoose.Types.ObjectId;
const cd = commCd.codeEnc('KO');
const log = extendedLogger('rs'); // 확장 로거

let completeFlag = [];

const STAT_TYPE_GFP = 'GFP';
const STAT_TYPE_ADPROVIDER = 'AdProvider';

/**
 * 5분마다 실행
 * @returns {Promise.<void>}
 */
const run = module.exports.run = async (txId, scheduleId, userRequestdBeginYmd, userRequestedEndYmd, isCheckPeriod = true, isForce = false) => {
	// 스케줄 정보 조회
	let schInfos = await getScheduleInfo(txId, scheduleId, userRequestdBeginYmd, userRequestedEndYmd, isCheckPeriod);

	// 실행해야 할 스케줄 선정
	const schInfosToRun = await _getScheduleInfoToRun(schInfos, isForce);
	if (_.isNil(schInfosToRun) || _.isEmpty(schInfosToRun)) {
		log.debug(`[G-RVN-SHAR] TXID:${txId} 실행해야 할 스케줄 없음.`);
		return;
	} else {
		/*
		{
			"schedule": {
				"_id": "619f2f2745d5451a7c80294d",
				"name": "리포트연동테스트용1_일별",
				"description": "",
				"period": "DAY",
				"interval": "DAILY",
				"status": "ON",
				"statsType": {
					"AdProvider": 1,
					"GFP": 1
				},
				"estimatedRevenuePrint": 0,
				"howManyDaysBefore": 2,
				"publisher_id": "5bf5159db77f83001f8a4605",
				"publisherName": "리포트연동테스트용1",
				"cmsType": "GFP",
				"creatorName": "김주연-MASTER",
				"lastCompletedAt": "2022-04-02T19:00:13.574Z",
				"keyGroup_id": "5c19add4f0c603002553e206",
				"keyGroup": {
					"types": [
						"REVENUE_SHARE"
					],
					"keys": [
						"keyA"
					],
					"_id": "5c19add4f0c603002553e206",
					"name": "주연rskg1",
					"description": "ㅇㄹㅇㄹ",
					"creatorName": "김주연-MASTER",
					"createdAt": "2018-12-19T02:32:52.884Z"
				}
			},
			"beginYmd": "20220402",
			"endYmd": "20220402",
			"date": "20220402",
			"timeSetting": {
				"beginYmd": "20220402",
				"endYmd": "20220402",
				"beginDotYmd": "2022.04.02",
				"endDotYmd": "2022.04.02",
				"beginDate": "2022-04-01T19:00:01.138Z",
				"endDate": "2022-04-01T19:00:01.138Z",
				"date": "20220402"
			},
			"txId": 0.28752059300364285,
			"idx": 0
		}
		 */
		// log.debug(`[G-RVN-SHAR] TXID:${txId} 선정된스케줄수:${schInfosToRun.length}  ${JSON.stringify(schInfosToRun, null, 2)}`);

		await _run_rs_v2(schInfosToRun); // v2 용 리포트 생성
	}

	return schInfosToRun;
};

/**
 * v2 용 리포트 생성
 * @param schInfosToRun
 * @returns {Promise<void>}
 * @private
 */
const _run_rs_v2 = async (schInfosToRun) => {
	await new Promise((resolve, reject) => {
		completeFlag = [];

		// 병령처리 작업 기술
		let tasks = schInfosToRun.map(schInfo => {
			completeFlag.push(0);

			return (async (callback) => {
				const {err, result} = await _makeChildProcess(schInfo, 2);
				callback(err, result); // 이 콜백 호출이 parallelLimit(병렬처리) 콜백으로 전달됨
			});
		});

		// CPU 개수 구하기
		let cpuNum = os.cpus().length - 1; // 부모 프로세스 빼기
		if (schInfosToRun.length < cpuNum) {
			cpuNum = schInfosToRun.length;
		}

		// 병렬처리
		parallelLimit(tasks, cpuNum, (err, results) => {
			if (err) {
				reject(err);
			} else {
				resolve();
			}
		});
	});
};

/**
 * AdProvider 리포트 재생성으로 수익쉐어 리포트 재생성
 * @returns {Promise.<void>}
 */
module.exports.reRunByAp = async (txId) => {
	// 재시도 대상 조회
	const list = await RevenueSharingRetryForAdProviderStat.aggregate([
		{
			'$match': {
				'overallState': {'$ne': 'COMPLETE'},
				'targets.$.state': {'$ne': 'COMPLETE'}
			}
		},
		{
			'$project': {
				'_id': '$_id',
				'targets': {
					'$filter': {
						'input': "$targets",
						'as': "target",
						'cond': {'$ne': ['$$target.state', 'COMPLETE']}
					}
				}
			}
		},
		{
			'$sort': {ymd: 1}
		}
	]);

	if (_.isNil(list) || _.isEmpty(list)) {
		return;
	}

	for (let idx = 0; idx < list.length; idx++) {
		let overallState = 'COMPLETE';

		const targets = list[idx].targets;

		if (_.isNil(targets) || _.isEmpty(targets)) {
			continue;
		}

		for (let targetIdx = 0; targetIdx < targets.length; targetIdx++) {
			let targetState = 'COMPLETE';

			// 1. 재시도 대상 퍼블리셔 정보
			const target = targets[targetIdx];
			log.debug(`[G-RVN-SHAR] revenue-sharing-report-csv.service.reRunByAp() :: 재생성(AP) - TXID:${txId} 타겟. ymd: ${target.ymd} publisher_id:${target.publisher_id}`);

			// 2. 대상 퍼블리셔의 스케줄 목록 조회
			const schedules = await getSchedules(null, target.publisher_id);

			if (_.isNil(schedules) || _.isEmpty(schedules)) {
				log.debug(`[G-RVN-SHAR] revenue-sharing-report-csv.service.reRunByAp() :: 재생성(AP) - TXID:${txId} 타겟. ymd: ${target.ymd} publisher_id:${target.publisher_id} 대상 스케줄 없음`);
			} else {
				// 3. 대상 퍼블리셔의 스케줄별 수익쉐어 리포트 재생성
				let logPrefix = '';
				for (let scheduleIdx = 0; scheduleIdx < schedules.length; scheduleIdx++) {
					const schedule = schedules[scheduleIdx];

					logPrefix = '[G-RVN-SHAR] revenue-sharing-report-csv.service.reRunByAp() :: 재생성(AP) -' +
						' TXID:' + txId +
						' PUB:' + schedule.publisherName + '(' + schedule.publisher_id + ')' +
						' SCH:' + schedule.name + '(' + schedule._id + ') period=' + schedule.period + ' interval=' + schedule.interval + ' howManyDaysBefore:' + schedule.howManyDaysBefore +
						' TARGET: ymd=' + target.ymd + ' publisher_id=' + target.publisher_id;

					// 적용 가능한 날짜인지 검사
					const reRun = await _isAppliableYmd(target.ymd, schedule);
					log.debug(`\n\n`);
					log.debug(`${logPrefix} 적용가능한날짜인지: ${reRun.isAppliable}`);

					if (reRun.isAppliable) {
						// 재생성할 날짜 결정
						const reRunYmdObj = _setReRunYmd(target.ymd, schedule);

						// 정상 배치에 의해 이미 돌고 있는지 검사
						const res = await _isAlreadyRun(reRunYmdObj.endYmd, schedule._id);
						if (res.isAlreadyRun) {
							log.debug(logPrefix + ' reportId:' + res.report._id + ' reportProgress:' + res.report.progress + ' 해당 일자 리포트가 이미 진행중이어서 스킵..');
						} else {
							logPrefix = logPrefix + ' ' + JSON.stringify(reRunYmdObj);
							log.debug(logPrefix + ' 재생성(AP) 중...');

							try {
								// 리포트 재생성
								const executedSchInfos = await run(txId, schedule._id, reRunYmdObj.beginYmd, reRunYmdObj.endYmd, false, true);
								log.debug(logPrefix + ' 재생성(AP) 완료');

								// 메일발송
								if (!_.isEmpty(executedSchInfos)) {
									await _announceReRun(STAT_TYPE_ADPROVIDER, target._id, target.ymd, target.publisher_id, target.adProvider_ids, schedule, reRunYmdObj);
								}
							} catch (e) {
								log.error(logPrefix + ' 재생성(AP) 실패. err:' + e.stack);
								overallState = targetState = 'FAILED';
								break; //대상 퍼블리셔의 리포트 재생성 중단
							}
						}
					} else {
						log.debug(logPrefix + ' 재생성(AP) 일자(' + target.ymd + ')가 스케줄 조건과 맞지 않아 스킵. 주기:' + schedule.period + ' 적용가능한일자:' + reRun.reRunnableYmd);
					}
				} // end of schedules
			}

			// 4. 대상 퍼블리셔의 특정 날짜 재생성 상태 업데이트
			await RevenueSharingRetryForAdProviderStat.findOneAndUpdate(
				{'targets._id': ObjectId(target._id)},
				{
					'$set': {
						'targets.$.state': targetState,
						'targets.$.modifiedAt': new Date(),
						'modifiedAt': new Date()
					}
				});
		} // end of targets

		// 5. AdProvider Stats 재시도 날짜의 종합 상태 업데이트
		await RevenueSharingRetryForAdProviderStat.findOneAndUpdate(
			{'_id': ObjectId(list[idx]._id)},
			{
				'$set': {'overallState': overallState, 'modifiedAt': new Date()}
			});
	} // end of list
};

/**
 * GFP 통계 재생성으로 인한 리포트 재생성
 * @param txId
 * @param date
 * @returns {Promise<*[]>}
 */
module.exports.reRunByGfp = async (txId, date) => {
	const skips = []; // 수익쉐어 리포트 생성이 이미 돌고 있어 스킵된 스케줄과 관련 정보

	// 스케줄 목록 조회
	const schList = await getSchedules(null, null);

	// GFP 통계를 포함하는 스케줄만으로 필터
	const schedules = schList.filter(sch => sch.statsType.GFP == 1);

	const schLogMsg = schedules.map(sch => {
		return `${sch.name}(${sch._id}) period=${sch.period} fileCreateCriteria=${sch.fileCreateCriteria} interval=${sch.interval} howManyDaysBefore=${sch.howManyDaysBefore}\n`;
	});
	log.debug(`[G-RVN-SHAR] revenue-sharing-report-csv.service.reRunByGfp() :: 재생성(GFP) - TXID:${txId} 재생성할 스케줄(${schedules.length}):\n${schLogMsg}`);

	// 스케줄별 수익쉐어 리포트 재생성
	let logPrefix = '';
	for (let scheduleIdx = 0; scheduleIdx < schedules.length; scheduleIdx++) {
		const schedule = schedules[scheduleIdx];

		logPrefix = '[G-RVN-SHAR] revenue-sharing-report-csv.service.reRunByGfp() :: 재생성(GFP) -' +
			' TXID:' + txId +
			' PUB:' + schedule.publisherName + '(' + schedule.publisher_id + ')' +
			' SCH:' + schedule.name + '(' + schedule._id + ') period=' + schedule.period + ' interval=' + schedule.interval + ' howManyDaysBefore:' + schedule.howManyDaysBefore +
			' date=' + date;

		// 적용 가능한 날짜인지 검사
		const reRun = await _isAppliableYmd(date, schedule);
		log.debug(`\n\n`);
		log.debug(`${logPrefix} 적용가능한날짜인지: ${reRun.isAppliable}`);

		if (reRun.isAppliable) {
			// 재생성할 날짜 결정
			const reRunYmdObj = _setReRunYmd(date, schedule);

			// 정상 배치에 의해 이미 돌고 있는지 검사
			const res = await _isAlreadyRun(reRunYmdObj.endYmd, schedule._id);

			if (res.isAlreadyRun) {
				log.debug(logPrefix + ' reportId:' + res.report._id + ' reportProgress:' + res.report.progress + ' 해당 일자 리포트가 이미 진행중이어서 스킵..');

				// 스킵된 정보
				skips.push({
					date,
					schedule: {
						_id: schedule._id,
						name: schedule.name
					},
					report: {
						_id: res.report._id,
						progress: res.report.progress
					}
				});

			} else {
				logPrefix = logPrefix + ' ' + JSON.stringify(reRunYmdObj);
				log.debug(logPrefix + ' 재생성(GFP) 중...');

				try {
					// 리포트 재생성
					const executedSchInfos = await run(txId, schedule._id, reRunYmdObj.beginYmd, reRunYmdObj.endYmd, false, true);
					log.debug(logPrefix + ' 재생성(GFP) 완료');

					// 메일발송
					if (!_.isEmpty(executedSchInfos)) {
						const gfpStatsSchedule = await SummaryRevenueSharingGfpStatsSchedule.findOne({'date': date});
						await _announceReRun(STAT_TYPE_GFP, gfpStatsSchedule._id, date, schedule.publisher_id, null, schedule, reRunYmdObj);
					}
				} catch (e) {
					log.error(logPrefix + ' 재생성(GFP) 실패. err:' + e.stack);
					break; //대상 퍼블리셔의 리포트 재생성 중단
				}
			}
		} else {
			log.debug(logPrefix + ' 재생성(GFP) 일자(' + date + ')가 스케줄 조건과 맞지 않아 스킵. 주기:' + schedule.period + ' 적용가능한일자:' + reRun.reRunnableYmd);
		}
	} // end of schedules

	return skips;
};

/**
 * 스케줄 목록 조회
 * @returns {Promise.<*>}
 */
const getSchedules = module.exports.getSchedules = async (scheduleId, publisherId) => {
	const matchQuery = {'status': 'ON'};

	if (!_.isNil(scheduleId)) {
		matchQuery._id = ObjectId(scheduleId);
	}

	if (!_.isNil(publisherId)) {
		matchQuery.publisher_id = ObjectId(publisherId);
	}

	const schedules = await SummaryRevenueSharingSchedule
		.aggregate([
			{
				'$match': matchQuery

			},
			{
				'$lookup': {
					from: 'Publishers',
					foreignField: '_id',
					localField: 'publisher_id',
					as: 'pub'
				}
			},
			{
				'$lookup': {
					from: 'Users',
					foreignField: '_id',
					localField: 'creator_id',
					as: 'user'
				}
			},
			{
				'$unwind': {
					'path': '$pub',
					'preserveNullAndEmptyArrays': false
				}
			},
			{
				'$unwind': {
					'path': '$user',
					'preserveNullAndEmptyArrays': false
				}
			},
			{
				'$match': {
					'pub.status': 'ON'
				}
			},
			{
				'$project': {
					'_id': '$_id',
					'name': '$name',
					'description': '$description',
					'period': '$period',
					'interval': '$interval',
					'fileCreateCriteria': '$fileCreateCriteria',
					'status': '$status',
					'statsType': '$statsType',
					'estimatedRevenuePrint': '$estimatedRevenuePrint',
					'howManyDaysBefore': '$howManyDaysBefore',
					'publisher_id': '$publisher_id',
					'publisherName': '$pub.name',
					'cmsType': '$pub.cmsType',
					'creatorName': '$user.name',
					'lastCompletedAt': '$lastCompletedAt',
					'keyGroup_id': '$keyGroup_id',
					'keyGroup': {
						'$arrayElemAt': [
							{
								'$filter': { // https://medium.com/techuniverse/mongodb-aggregation-example-extracting-a-specific-field-of-an-array-element-with-matching-d832eeef2cdf
									'input': "$pub.keyGroups",
									'as': "keyGroup",
									'cond': {'$eq': ["$$keyGroup._id", "$keyGroup_id"]}
								}
							}, 0
						]
					}
				}
			},
			{
				'$sort': {'period': 1}
			}
		])
		.exec();

	/*
		// schedules

		[
			{
				"_id": "5be90adc7c741214f08f6f12",
				"name": "NaverTV 수익쉐어",
				"description": "NaverTV 수익쉐어 설명입니다.",
				"period": "MONTH",
				"status": "ON",
				"publisher_id": "5b3f2e7a73001509b777c20e",
				"keyGroup_id": "5bd7e2da77bd856e48c1518c",
				"keyGroup": {
					"_id": "5bd7e2da77bd856e48c1518c",
					"name": "navertv kg",
					"description": "navertv key group... 지우지마셩.",
					"keys": [
						"country",
						"author"
					],
					"types": [
						"NORMAL",
						"REVENUE_SHARE"
					],
					"creatorName": "김주연",
					"createdAt": "2018-11-07T08:25:23.100Z"
				}
			}
		]
	*/
	if (_.isNil(schedules) || _.isEmpty(schedules) || schedules.length < 1) {
		return [];
	} else {
		for (const schedule of schedules) {
			schedule.keyGroup.keys = await _getSortedKeysByCreatedAt(schedule.publisher_id, schedule.keyGroup.keys);
		}
		return schedules;
	}
};

const _getSortedKeysByCreatedAt = async (publisher_id, keys) => {
	const pub = await Publisher.aggregate([
		{
			'$match': {
				_id: publisher_id,
				'freeformKeyValues.key': {'$in': keys}
			}
		},
		{
			'$sort': {
				'freeformKeyValues.createdAt': 1
			}
		},
		{
			'$project': {
				_id: 0,
				'keys': '$freeformKeyValues.key'
			}
		},
		{
			'$project': {
				'sortedKeys': {
					'$filter': {
						'input': "$keys",
						'as': "itemKey",
						'cond': {'$setIsSubset': [['$$itemKey'], keys]}
					}
				}

			}
		}
	]);

	return pub[0].sortedKeys;
};

/**
 * 스케줄에 실행해야 할 날짜와 차일드프로세스 번호까지 부여한 종합적인 스케줄 정보
 * @type {function(*=, *, *, *)}
 */
const getScheduleInfo = module.exports.getScheduleInfo = async (txId, scheduleId, userRequestedBeginYmd, userRequestedEndYmd, isCheckPeriod) => {
	if ((!_.isNil(userRequestedBeginYmd) && userRequestedBeginYmd.length !== 8) || (!_.isNil(userRequestedEndYmd) && userRequestedEndYmd.length !== 8)) {
		const msg = '[G-RVN-SHAR] revenue-sharing-report-csv.service.getScheduleInfo() :: userRequestedBeginYmd 또는 userRequestedEndYmd 파라미터가 yyyymmdd 형식이 아님.' +
			' sch:' + scheduleId + ' userRequestedBeginYmd:' + userRequestedBeginYmd + ' userRequestedEndYmd:' + userRequestedEndYmd +
			' isCheckPeriod:' + isCheckPeriod + ' txId:' + txId;
		log.error(msg);
		throw Error(msg);
	}

	const schInfos = [];

	// 스케줄 가져오기
	const schedules = await getSchedules(scheduleId);


	for (let i = 0; i < schedules.length; i++) {
		// 월 단위 경우 매월 2일이 아니면 스킵
		if (isCheckPeriod && !_isRunnablePeriod(schedules[i].period)) {
			continue;
		}

		const timeSetting = _setDate(schedules[i], userRequestedBeginYmd, userRequestedEndYmd);

		schInfos.push({
			schedule: schedules[i],
			beginYmd: timeSetting.beginYmd,
			endYmd: timeSetting.endYmd,
			date: timeSetting.date,
			timeSetting: timeSetting,
			txId: txId
		});
	}

	return schInfos;
};

/**
 * 스케줄 실행여부
 * @param period
 * @returns {boolean}
 */
const _isRunnablePeriod = (period) => {
	const today = moment();

	if (period == 'MONTH') {
		if (today.date() == 2) { // 매 월 2일에만 월단위 스케줄 실행
			return true;
		} else {
			return false;
		}
	} else {
		return true;
	}
};

/**
 * 진짜 실행해야 할 스케줄 구성
 *
 * 	1. AdProvider 리포트가 생성되었는지 확인
 * 	2. 이미 진행중인 스케줄은 스킵
 * 	3. 생성 완료된 스케줄도 스킵
 * @param schInfos
 * @returns {Promise.<Array>}
 * @private
 */
const _getScheduleInfoToRun = async (schInfos, isForce) => {
	const schInfosToRun = [];
	const skipSchInfos = [];

	let idx = 0;
	for (let i = 0; i < schInfos.length; i++) {
		/*
		schInfo = {
			"schedule": schedule,
			"bundleId": bundleId,
			"beginYmd": timeSetting.beginYmd,
			"endYmd": timeSetting.endYmd,
			"date": timeSetting.date,
			"timeSetting": {
				// yyyymmdd 형식(문자열)
				"beginYmd": beginYmd,
				"endYmd": endYmd,

				// yyyy.mm.dd 형식(문자열)
				"beginDotYmd": beginDotYmd,
				"endDotYmd": endDotYmd,

				// moment 객체
				"beginDate": beginDate,
				"endDate": endDate,

				// 일단위일 경우
				"date": date
			},
			"txId": txId
		}
		 */
		const schInfo = schInfos[i];

		// 월마다 30일치를 30개 파일로 생성하는 경우라도 마지막날 리포트가 있는지만 검사하면 됨.
		let reports = await _getReportsBySchedule(schInfo.schedule._id, schInfo.endYmd);

		if (reports && reports.length > 0) {
			schInfo.bundleId = reports[0].bundleId;
		}

		// 리포트 row가 없거나 강제 생성이면
		if (_.isNil(reports) || (!_.isNil(reports) && reports.length < 1) || isForce) {
			const bundleId = await _prepareReports(schInfo); // 'STANDBY' 상태로 리포트 준비
			schInfo.bundleId = bundleId;
		}

		// 비교하기 직전에 디비에서 리포트들의 번들 상태 조회
		const bundleState = await _getBundleState(schInfo);
		schInfo.bundleState = bundleState;

		const logPrefix = getLogPrefix(schInfo);

		if (bundleState === cd.RevenueSharingReportProgress.COMPLETE.code) { // 리포트 이미 완료
			log.debug(`[G-RVN-SHAR] ${logPrefix} 완료`);
		} else if (bundleState === cd.RevenueSharingReportProgress.STANDBY.code || // STANDBY or STANDBY_AP or STANDBY_GFP
			bundleState === cd.RevenueSharingReportProgress.STANDBY_AP.code ||
			bundleState === cd.RevenueSharingReportProgress.STANDBY_GFP.code) {

			// AP리포트 완료여부 조회
			const apReportsState = await getAdpReportsState(schInfo.schedule.publisher_id, schInfo.schedule.keyGroup_id, schInfo.endYmd, logPrefix);
			let isApComplete = (apReportsState.uncompleteAdps.length > 0) ? false : true;

			// GFP 리포트 완료여부 조회
			let date;
			if (schInfo.schedule.interval === 'MONTHLY') {
				date = schInfo.endYmd.substr(0, 6);
			} else {
				date = schInfo.endYmd;
			}
			let isGfpComplete = 0;
			if (schInfo.schedule.statsType.GFP === 0) { // GFP 통계를 포함하지 않는 스케줄은 GFP 리포트 상태를 완료로 고정
				isGfpComplete = 1;
			} else {
				isGfpComplete = await isGfpReportsComplete(date, logPrefix);
			}

			if (!isApComplete && !isGfpComplete) {		// AP, GFP 둘 다 대기중
				await _updateBundleState(schInfo.schedule._id, schInfo.timeSetting.beginYmd, schInfo.timeSetting.endYmd, cd.RevenueSharingReportProgress.STANDBY.code);
				log.debug(`[G-RVN-SHAR] ${logPrefix} AP & GFP 통계 모두 안 끝났음. bundleState = STANDBY`);

				// 스킵된 스케줄
				if (isForce) {
					let reason = `${logPrefix}<br/>`;
					reason += '	AP상태: <span style="color:crimson">' + getApReportsStatePhrase(apReportsState) + '</span><br/>';
					reason += '	GFP상태: <span style="color:crimson">미완료</span><br/>';
					schInfo['reason'] = reason;

					skipSchInfos.push(schInfo);
				}

			} else if (!isApComplete && isGfpComplete) {	// AP만 대기중
				await _updateBundleState(schInfo.schedule._id, schInfo.timeSetting.beginYmd, schInfo.timeSetting.endYmd, cd.RevenueSharingReportProgress.STANDBY_AP.code);
				log.debug(`[G-RVN-SHAR] ${logPrefix} AP 통계가 안 끝났음. bundleState = STANDBY_AP`);

				// 스킵된 스케줄
				if (isForce) {
					let reason = `${logPrefix}<br/>`;
					reason += '	AP상태: <span style="color:crimson">' + getApReportsStatePhrase(apReportsState) + '</span><br/>';
					reason += '	GFP상태: 완료<br/>';
					schInfo['reason'] = reason;

					skipSchInfos.push(schInfo);
				}

			} else if (isApComplete && !isGfpComplete) {	// GFP만 대기중
				await _updateBundleState(schInfo.schedule._id, schInfo.timeSetting.beginYmd, schInfo.timeSetting.endYmd, cd.RevenueSharingReportProgress.STANDBY_GFP.code);
				log.debug(`[G-RVN-SHAR] ${logPrefix} GFP 통계가 안 끝났음. bundleState = STANDBY_GFP`);

				// 스킵된 스케줄
				if (isForce) {
					let reason = `${logPrefix}<br/>`;
					reason += '	AP상태: ' + getApReportsStatePhrase(apReportsState) + '<br/>';
					reason += '	GFP상태: <span style="color:crimson">미완료</span><br/>';
					schInfo['reason'] = reason;

					skipSchInfos.push(schInfo);
				}

			} else if (isApComplete && isGfpComplete) {	// AP, GFP 통계 모두 끝났음.
				// 번들 상태 START로 업데이트 (https://oss.navercorp.com/da-ssp/bts/issues/606)
				// 자식 프로세스로 가는 동안 멀티요청으로(아주 짧은 시간 차이로) 인해 중복 처리되는 것을 방지하기 위해
				// 부모 프로세스에서 스케줄 선정하자마자 START로 놓음.
				await _updateBundleState(schInfo.schedule._id, schInfo.timeSetting.beginYmd, schInfo.timeSetting.endYmd, cd.RevenueSharingReportProgress.START.code);
				log.debug(`[G-RVN-SHAR] ${logPrefix} 리포트 상태를 START로 업데이트함`);

				schInfo.idx = idx++;

				// 스케줄 선정
				schInfosToRun.push(schInfo);
			}
		} else {
			// 그 밖의 것(START, FAILURE) 중 FAILURE는 알람으로 사람이 수동처리
			log.debug(`[G-RVN-SHAR] ${logPrefix} ${bundleState} 이미 처리 중`);
		}
	}

	// 스킵된 스케줄에 대해서는 이메일 알림
	if (!_.isEmpty(skipSchInfos)) {
		await _sendMailForSkippedSchedules(skipSchInfos);
	}

	return schInfosToRun;
};

/**
 * 처리하려는 리포트 날짜에 해당하는 번들 상태 조회
 *
 * @param schInfo
 * @private
 */
const _getBundleState = async (schInfo) => {
	// period = 'DAY' 인 경우 beginYmd, endYmd가 같지만
	// period = 'MONTH', fileCriteria='DAILY'인 경우 beginYmd는 달의 첫날, endYmd는 달의 마지막 날이다.
	const filter = {
		'summaryRevenueSharingSchedule_id': ObjectId(schInfo.schedule._id),
		'beginYmd': {'$gte': schInfo.timeSetting.beginYmd},
		'endYmd': {'$lte': schInfo.timeSetting.endYmd},
	};

	// log.debug(`............ getBundleState() :: fileter=${JSON.stringify(filter, null, 2)}`);

	let completeCnt = 0;
	let standbyCnt = 0;
	let standbyApCnt = 0;
	let standbyGfpCnt = 0;
	let failureCnt = 0;
	let otherCnt = 0;
	const reports = await SummaryRevenueSharingReport.find(filter).sort({'_id': -1});
	reports.forEach(rpt => {
		if (rpt.progress === cd.RevenueSharingReportProgress.COMPLETE.code) completeCnt++;
		else if (rpt.progress === cd.RevenueSharingReportProgress.STANDBY.code) standbyCnt++;
		else if (rpt.progress === cd.RevenueSharingReportProgress.STANDBY_AP.code) standbyApCnt++;
		else if (rpt.progress === cd.RevenueSharingReportProgress.STANDBY_GFP.code) standbyGfpCnt++;
		else if (rpt.progress === cd.RevenueSharingReportProgress.FAILURE.code) failureCnt++;
		else otherCnt++;
	});

	// n 개 리포트들의 종합 상태 설정
	const rptCnt = reports.length;
	if (rptCnt == completeCnt) { // 모두 COMPLETE이어야 COMPLETE
		return cd.RevenueSharingReportProgress.COMPLETE.code;
	} else if (rptCnt == standbyCnt) { // 모두 STANDBY이어야 STANDBY
		return cd.RevenueSharingReportProgress.STANDBY.code;
	} else if (rptCnt == standbyApCnt) { // 모두 STANDBY_AP이어야 STANDBY_AP
		return cd.RevenueSharingReportProgress.STANDBY_AP.code;
	} else if (rptCnt == standbyGfpCnt) { // 모두 STANDBY_GFP이어야 STANDBY_GFP
		return cd.RevenueSharingReportProgress.STANDBY_GFP.code;
	} else if (failureCnt > 0) { // 실패가 하나라도 있으면 실패
		return cd.RevenueSharingReportProgress.FAILURE.code;
	} else { // 나머지는 실행 중...
		return "START_OR_ONGOING";
	}
};

const getApReportsStatePhrase = module.exports.getApReportsStatePhrase = (apReportsState) => {
	let apReportStatePhrase = '';
	if (apReportsState.completeAdps) { // 완료된 AP
		apReportsState.completeAdps.forEach(adp => {
			apReportStatePhrase += `${adp.adProviderName}:${adp.reportApiType}(${adp.mongoState}) `;
		});
	}

	if (apReportsState.uncompleteAdps) { // 미 완료된 AP
		apReportsState.uncompleteAdps.forEach(adp => {
			apReportStatePhrase += `${adp.adProviderName}:${adp.reportApiType}(${adp.mongoState}) `;
		});
	}
	return apReportStatePhrase;
};

/**
 * 강제 생성 중 스킵된 스케줄 정보는 이메일로 알림
 * @param skippedSchInfos
 * @returns {Promise<void>}
 * @private
 */
const _sendMailForSkippedSchedules = async (skippedSchInfos) => {
	try {
		const env = await Environments.findOne({'name': 'revenue-sharing-report-monitor-receiver'}).select('value');
		const to = env.value.join(';');

		const subject = '수익쉐어 리포트 - 스킵된 스케줄 정보';

		let html = '';
		skippedSchInfos.forEach(schInfo => {
			const schedule = schInfo.schedule;

			html += '[ 매체 ]<br/>';
			html += schedule.publisherName + ' (' + schedule.publisher_id + ')<br/><br/>';

			html += '[ 스케줄 ]<br/>';
			html += '	스케줄명: ' + schedule.name + ' (' + schedule._id + ')<br/>';
			html += '	키그룹: ' + schedule.keyGroup.name + ' (' + schedule.keyGroup._id + ')<br/>';
			html += '	생성주기: ' + cd.RevenueSharingReportPeriod[schedule.period].name + '<br/>';
			html += '	집계단위: ' + cd.RevenueSharingReportInterval[schedule.interval].name + '<br/>';
			html += '	파일생성기준: ' + (_.isNil(schedule.fileCreateCriteria) ? '-' : cd.RevenueSharingReportFileCreateCriteria[schedule.fileCreateCriteria].name) + '<br/><br/>';

			html += '[ 날짜 ]<br/>';
			html += '	' + schInfo.beginYmd + ' ~ ' + schInfo.endYmd + '<br/><br/>';

			html += '[ 사유 ]<br/>';
			html += '	' + schInfo.reason + '<br/>';
			html += '<br/>--------------------------------------------------------------------------------<br/>';
		});

		mailer.sendMail({to, subject, html});
	} catch (error) {
		log.error(`[G-RVN-SHAR] revenue-sharing-report-csv.service._sendMailForSkippedSchedules() :: 수익쉐어 리포트 - 강제 생성 중 스킵된 스케줄 정보에 대한 메일 보내는 중 에러. ${error.stack}`);
	}
};

/**
 * 스케줄이 아닌 리포트의 번들 상태 업데이트
 * 한 달치 30개 파일로 쪼개지는 경우 일괄 같은 번들 상태로 업데이트됨
 *
 * @param schedule_id
 * @param beginYmd
 * @param endYmd
 * @param bundleState
 * @returns {Promise<void>}
 * @private
 */
const _updateBundleState = async (schedule_id, beginYmd, endYmd, bundleState) => {
	await SummaryRevenueSharingReport.updateMany(
		{
			'summaryRevenueSharingSchedule_id': ObjectId(schedule_id),
			'beginYmd': {'$gte': beginYmd},
			'endYmd': {'$lte': endYmd},
		},
		{'$set': {'progress': bundleState}}
	);
};

/**
 * 생성주기(period)와 집계단위(interval)에 따라 시작일 ~ 종료일까지, 어느 파일 이름으로 몇 개의 리포트를 만들지 결정된다.
 * 그 정보를 DB에 미리 생성해 놓는다.
 *
 * 30개의 파일을 만들어야 한다면 30개의 report를 준비
 *
 * @param schInfo
 * @returns {Promise.<Array>}
 * @private
 */
const _prepareReports = async (schInfo) => {
	const schedule = schInfo.schedule;
	const timeSetting = schInfo.timeSetting;

	let dateForFileName; // 파일명을 위한 날짜

	const bundleId = new ObjectId(); // 한 스케줄에 여러 리포트가 생성되는 경우 그들을 묶기 위한 ID

	/*
		생성 대기중 리포트가 중복으로 표시되는 현상 (https://oss.navercorp.com/da-ssp/bts/issues/1007)
		재시도 시 이력을 재생산하지 않고, 기존 리포트 document를 STANDBY 상태로 overwrite한다.
	 */

	if (schedule.period == 'DAY') { // 그 날치를 그 날 파일에
		dateForFileName = timeSetting.beginDate.format('YYYYMMDD');
		const alreadyExistReport_id = await _getReportIdIfAlreadyExistReport(schInfo.schedule._id, schInfo.beginYmd, schInfo.endYmd);
		if (alreadyExistReport_id) {
			await _overwriteReport(bundleId, alreadyExistReport_id);
		} else {
			await _createReport(schInfo.schedule._id, bundleId, schInfo.beginYmd, schInfo.endYmd, dateForFileName);
		}
	} else {
		// period = MONTH, interval = MONTHLY :: 한 달치를 한줄로 한 파일에
		if (schedule.interval == 'MONTHLY') {
			dateForFileName = timeSetting.beginDate.format('YYYYMM');
			const alreadyExistReport_id = await _getReportIdIfAlreadyExistReport(schInfo.schedule._id, schInfo.beginYmd, schInfo.endYmd);
			if (alreadyExistReport_id) {
				await _overwriteReport(bundleId, alreadyExistReport_id);
			} else {
				await _createReport(schInfo.schedule._id, bundleId, schInfo.beginYmd, schInfo.endYmd, dateForFileName);
			}
		} else {
			// period = MONTH, interval = DAILY, fileCreateCriteria = MONTHLY :: 한 파일에 30일치를 일별로 기술
			if (schedule.fileCreateCriteria == 'MONTHLY') {
				dateForFileName = timeSetting.beginDate.format('YYYYMM');

				const alreadyExistReport_id = await _getReportIdIfAlreadyExistReport(schInfo.schedule._id, schInfo.beginYmd, schInfo.endYmd);
				if (alreadyExistReport_id) {
					await _overwriteReport(bundleId, alreadyExistReport_id);
				} else {
					await _createReport(schInfo.schedule._id, bundleId, schInfo.beginYmd, schInfo.endYmd, dateForFileName);
				}
			} else {
				// period = MONTH, interval = DAILY, fileCreateCriteria = DAILY :: 30일치를 30개 파일로
				let currDate = moment(timeSetting.beginDate);
				while (currDate.isSameOrBefore(timeSetting.endDate)) {
					dateForFileName = currDate.format('YYYYMMDD');
					const alreadyExistReport_id = await _getReportIdIfAlreadyExistReport(schInfo.schedule._id, dateForFileName, dateForFileName);
					if (alreadyExistReport_id) {
						await _overwriteReport(bundleId, alreadyExistReport_id);
					} else {
						await _createReport(schInfo.schedule._id, bundleId, dateForFileName, dateForFileName, dateForFileName);
					}
					currDate = moment(currDate.add(1, 'd'));
				}
			}
		}
	}

	return bundleId;
};

/**
 * 해당 일자의 리포트 다큐먼트가 이미 있는지 확인
 * @param schedule_id
 * @param beginYmd
 * @param endYmd
 * @returns {Promise<null|*>}
 * @private
 */
const _getReportIdIfAlreadyExistReport = async (schedule_id, beginYmd, endYmd) => {
	const report = await SummaryRevenueSharingReport.findOne({
		'summaryRevenueSharingSchedule_id': schedule_id,
		'beginYmd': beginYmd,
		'endYmd': endYmd,
	}).sort({_id: -1});

	if (_.isNil(report)) {
		return null;
	} else {
		return report._id;
	}
};

/**
 * 특정 리포트 다큐먼트를 STANDBY 상태로 덮어쓰기
 * @param bundle_id
 * @param report_id
 * @returns {Promise<void>}
 * @private
 */
const _overwriteReport = async (bundle_id, report_id) => {
	await SummaryRevenueSharingReport.updateOne(
		{
			'_id': report_id
		},
		{
			'$set': {
				'bundleId': bundle_id,
				'msg': cd.RevenueSharingReportProgress.STANDBY.code, // 개발자용 모니터링
				'progress': cd.RevenueSharingReportProgress.STANDBY.code,
				'filePath': null,
				'fileName': null,
				'begunAt': null,
				'completedAt': null,
				'modifiedAt': moment()
			}
		});
};

/**
 * 이 스케줄이 만들어야 하는 대상 날짜 설정
 *
 * @param schedule      : 스케줄 정보
 * @param condBeginYmd  : 사용자가 지정한 시작 날짜
 * @param condEndYmd    : 사용자가 지정한 종료 날짜
 * @returns {{beginYmd: *, endYmd: *, beginDotYmd: *, endDotYmd: *, beginDate: *, endDate: *}}
 */
const _setDate = (schedule, userRequestedBeginYmd, userRequestedEndYmd) => {
	const today = moment();
	let beginDate, endDate;		// for loop
	let beginYmd, endYmd;		// for mongodb 및 드루이드 조회 용 날짜
	let beginDotYmd, endDotYmd; // csv 내에 표기할 날짜
	let date;

	if (schedule.period === 'DAY') { // 일 단위(디폴트 1일 또는 2일 전)
		beginDate = today.subtract(schedule.howManyDaysBefore, 'days'); // 며칠전 데이터를 파일로 써야 하는지
		endDate = beginDate;

		beginYmd = beginDate.format('YYYYMMDD');
		endYmd = beginYmd;

		beginDotYmd = beginDate.format('YYYY.MM.DD');
		endDotYmd = beginDotYmd;

		date = endDate.format('YYYYMMDD');
	} else { // 월 단위 (디폴트 지난 달)
		beginDate = moment(today.subtract(1, 'months').date(1)); // 지난 달 1일
		endDate = moment(today.endOf('month')); // 지난 달 말일

		beginYmd = beginDate.format('YYYYMM01'); // 지난 달 1일
		endYmd = endDate.format('YYYYMMDD'); // 지난 달 말일

		beginDotYmd = beginDate.format('YYYY.MM.01'); // 지난 달 1일
		endDotYmd = endDate.format('YYYY.MM.DD'); // 지난 달 말일

		date = endDate.format('YYYYMM');
	}

	// 파라미터로 들어온게 있다면 스케줄의 날짜 무시하고 그것으로 설정
	if (!_.isNil(userRequestedBeginYmd) && !_.isEmpty(userRequestedBeginYmd)) {
		beginYmd = userRequestedBeginYmd;
		beginDate = moment(beginYmd, 'YYYYMMDD');
		beginDotYmd = beginDate.format('YYYY.MM.DD');
	}
	if (!_.isNil(userRequestedEndYmd) && !_.isEmpty(userRequestedEndYmd)) {
		endYmd = userRequestedEndYmd;
		endDate = moment(endYmd, 'YYYYMMDD');
		endDotYmd = endDate.format('YYYY.MM.DD');

		if (schedule.period === 'DAY') { // 일 단위
			date = endDate.format('YYYYMMDD');
		} else {
			date = endDate.format('YYYYMM');
		}
	}

	return {
		// yyyymmdd 형식
		beginYmd,
		endYmd,

		// yyyy.mm.dd 형식
		beginDotYmd,
		endDotYmd,

		// Date 객체
		beginDate,
		endDate,

		date // 일단위일경우 yyyymmdd 문자열, 월단위일 경우 yyyymm 문자열
	}
};

/**
 * CPU 개수만큼 스케줄 실행
 *
 * @param schInfo
 * @returns {Promise.<void>}
 */
const _makeChildProcess = (schInfo, version) => {
	return new Promise((resolve, reject) => {
		try {
			const starttime = moment();
			let ellapsedTime;

			let msg;

			const schedule = schInfo.schedule;
			const idx = schInfo.idx;

			const runId = `[G-RVN-SHAR] TXID:${schInfo.txId}  PUB:${schedule.publisher_id}    SCH:${schedule.name}(${schedule._id})   `;

			// 자식 프로세스 띄움
			let option = {execArgv: ['--max-old-space-size=4096']};
			if (process.env.NODE_ENV === 'local') {
				option = {};
			}

			let childProc;
			if (schInfo.schedule.cmsType == 'GFP') {
				childProc = fork(path.join(process.cwd(), '/src/rsCsvApp.js'), option);
			} else if (schInfo.schedule.cmsType == 'NAM') {
				childProc = fork(path.join(process.cwd(), '/src/rsCsvNamApp.js'), option);
			}

			// 자식 프로세스로부터 받은 메시지
			childProc.on('message', (step) => {
				if (step.op === 'complete' || step.op === 'fail') {
					completeFlag[idx] = 1;

					childProc.disconnect();

					if (step.op === 'complete') { // complete
						ellapsedTime = moment() - starttime;

						msg = `${runId} childProc[${idx}] 리포트 완료.   소요시간(msec):${ellapsedTime}   (sec):${ellapsedTime / 1000}   리포트 작성 끝난 수:${completeFlag.filter(val => val).length}/${completeFlag.length}`;

						log.debug(msg);

						resolve({
							result: msg
						});
					} else { // fail
						resolve({
							err: new BusinessError({
								code: SBE.Common.Fail,
								message: `${runId} childProc[${idx}] 리포트 실패. ${step.msg}`
							})
						});
					}
				} else { // op == 'ing'
					log.debug(`${runId} childProc[${idx}] . ${step}`);
				}
			});

			// 자식 프로세스 에러 발생 시
			childProc.on('error', err => {
				log.error(`[G-RVN-SHAR] revenue-sharing-report-csv.service.makeChildProcess() :: childProc.on('error') :: ${err.stack}`);
				resolve({
					err: new BusinessError({
						code: SBE.Common.Fail,
						message: `${runId} childProc[${idx}] 리포트 실패 ${err.message}`
					})
				});
			});

			childProc.on('uncaughtException', err => {
				log.error(`[G-RVN-SHAR] revenue-sharing-report-csv.service.makeChildProcess() :: childProc.on('uncaughtException') :: ${err.stack}`);
				resolve({
					err: new BusinessError({
						code: SBE.Common.Fail,
						message: `${runId} childProc[${idx}] 리포트 실패 ${err.message}`
					})
				});
			});

			// 프로세스가 준비될 때까지 기다렸다가 실행
			setTimeout(() => {
				childProc.send(schInfo);
			}, 1000);
		} catch (error) {
			log.error('[G-RVN-SHAR] revenue-sharing-report-csv.service.makeChildProcess() :: ', error.stack);
			resolve({
				err: error
			});
		}
	});
};

/**
 * 적용 가능한 날짜인지
 *
 * @param date
 * @param schedule
 * @returns {{isAppliable, reRunnableYmd}}
 * @private
 */
const _isAppliableYmd = async (date, schedule) => {
	const result = {
		isAppliable: false,
		reRunnableYmd: '-', // 로깅할 때 쓰임
	};

	if (schedule.period == 'MONTH') { // 월 1회 생성
		const today = moment();
		const targetMonth = moment(date, 'YYYYMM01');
		const previousMonth = today.subtract(1, 'months').format('YYYYMM01');
		if (today.date() >= 2 && targetMonth.isSameOrBefore(previousMonth)) { // 재집계를 해야 할 달이 스케줄의 대상 달 이전일 때만 실행
			result.isAppliable = true;
		} else {
			result.isAppliable = false;
		}
		result.reRunnableYmd = previousMonth; // 재실행 가능한 달. 이 달을 포함한 이전 달만 가능
	} else {
		const theDaysBeforeDate = moment().subtract(schedule.howManyDaysBefore, 'days');

		if (date.length === 8) { // 매일 집계인 경우 재집계 날짜가 yyyymmdd로 들어왔을 때만 대상임
			const targetDate = moment(date, 'YYYYMMDD');
			if (targetDate.isSameOrBefore(theDaysBeforeDate)) {
				result.isAppliable = true;
			} else {
				result.isAppliable = false;
			}
		} else {
			// 매일 집계인데 yyyymm으로 들어오면 실행 불가
			result.isAppliable = false;
		}

		result.reRunnableYmd = theDaysBeforeDate.format('YYYYMMDD'); // 재실행 가능한 날짜. 이 날을 포함한 이전 날짜만 가능
	}

	return result;
};

/**
 * 정상 배치에 의해 이미 돌고 있는지 검사
 * @param date
 * @param schedule_id
 * @returns {Promise<boolean>}
 * @private
 */
const _isAlreadyRun = async (date, schedule_id) => {
	const report = await SummaryRevenueSharingReport
		.findOne({
			summaryRevenueSharingSchedule_id: ObjectId(schedule_id),
			endYmd: date
		})
		.sort({createdAt: -1})
		.limit(1);

	let isAlreadyRun = false;
	if (_.isNil(report)) {
		isAlreadyRun = false; // 스케줄을 껐다 킨 경우는 해당 리포트가 없을 수 있음.
	} else {
		if (report.progress === cd.RevenueSharingReportProgress.START.code ||
			report.progress === cd.RevenueSharingReportProgress.ONGOING_RK.code ||
			report.progress === cd.RevenueSharingReportProgress.ONGOING_NONRK.code ||
			report.progress === cd.RevenueSharingReportProgress.ONGOING_GFP.code
		) {
			isAlreadyRun = true;
		}
	}

	return {isAlreadyRun, report};
};

/**
 * 재실행할 시작~종료 일자
 *
 * @param date
 * @param schedule
 * @returns {{beginYmd: *, endYmd: *}}
 * @private
 */
const _setReRunYmd = (date, schedule) => {
	let beginYmd, endYmd;

	if (schedule.period == 'MONTH') { // 월 1회 생성
		if (schedule.interval == 'DAILY') {
			if (schedule.fileCreateCriteria == 'DAILY') {
				beginYmd = date;
				endYmd = date;
			} else {
				const reRunDate = moment(date, 'YYYYMMDD'); // date가 6자리여도 reRunDate는 8자리로 자동으로 만들어 줌. 202109로 들어오면 20210930로 만들어 줌
				beginYmd = reRunDate.format('YYYYMM01'); // 해당 달의 1일
				endYmd = reRunDate.endOf('month').format('YYYYMMDD'); // 해당 달의 말일
			}
		} else {
			const reRunDate = moment(date, 'YYYYMMDD');
			beginYmd = reRunDate.format('YYYYMM01'); // 해당 달의 1일
			endYmd = reRunDate.endOf('month').format('YYYYMMDD'); // 해당 달의 말일
		}
	} else {
		beginYmd = date;
		endYmd = date;
	}

	return {beginYmd, endYmd};
};

/**
 * 스케줄 재실행 완료 시 재실행 사실을 담당자에게 통지

 "value" : [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]

 *
 * @param target
 * @param schedule
 */
const _announceReRun = async (statType, target_id, targetDate, targetPublisher_id, targetAdProvider_ids, schedule, reRunYmdObj) => {
	try {
		// 해당 스케줄의 최신 리포트 조회
		const report = await SummaryRevenueSharingReport
			.findOne({
				summaryRevenueSharingSchedule_id: ObjectId(schedule._id),
				endYmd: reRunYmdObj.endYmd,
				progress: {'$in': ['completed', 'COMPLETED', 'COMPLETE']}
			})
			.sort({completedAt: -1})
			.limit(1);

		let subject = 'GFP 수익쉐어 리포트 재생성 알림';

		let html = '아래의 스케줄 및 통계타입에 대하여 GFP 수익쉐어 리포트가 재생성되었습니다.<br/><br/>';

		html += '[ 매체 ]<br/>';
		html += schedule.publisherName + ' (' + schedule.publisher_id + ')<br/><br/>';

		html += '[ 재집계 ]<br/>';
		html += '	통계타입: ' + statType + '<br/>';
		html += '	날짜: ' + targetDate + '<br/>';

		if (statType === STAT_TYPE_GFP) {
			html += '	SummaryRevenueSharingGfpStatsSchedule._id: ' + target_id + '<br/>';
			html += '	publisher_id: ' + (_.isNil(targetPublisher_id) ? '전체' : targetPublisher_id) + '<br/><br/>';
		} else {
			html += '	RevenueSharingRetryForAdProviderStats.target_id: ' + target_id + '<br/>';
			html += '	RevenueSharingRetryForAdProviderStats.target[].publisher_id: ' + (_.isNil(targetPublisher_id) ? '전체' : targetPublisher_id) + '<br/>';

			html += '	RevenueSharingRetryForAdProviderStats.target[].adProvider_ids: ';
			if (targetAdProvider_ids && targetAdProvider_ids.length > 0) {
				const adps = await AdProvider.find({_id: {'$in': targetAdProvider_ids}}).select('_id name');
				adps.forEach(adp => {
					html += adp.name + '(' + adp._id + ') ';
				});
			}
			html += '<br/><br/>';
		}

		html += '[ 스케줄 ]<br/>';
		html += '	스케줄명: ' + schedule.name + ' (' + schedule._id + ')<br/>';
		html += '	키그룹: ' + schedule.keyGroup.name + ' (' + schedule.keyGroup._id + ')<br/>';
		html += '	생성주기: ' + cd.RevenueSharingReportPeriod[schedule.period].name + '<br/>';
		html += '	집계단위: ' + cd.RevenueSharingReportInterval[schedule.interval].name + '<br/>';
		html += '	파일생성기준: ' + (_.isNil(schedule.fileCreateCriteria) ? '-' : cd.RevenueSharingReportFileCreateCriteria[schedule.fileCreateCriteria].name) + '<br/><br/>';

		if (_.isNil(report)) {
			html += '[ 리포트 ]<br/>';
			html += '	리포트 생성 조건을 만족하지 못해 리포트 생성 안됐음.<br/><br/>';
		} else {
			html += '[ 리포트 ]<br/>';
			html += '	_id: ' + report._id + '<br/>';
			html += '	파일: ' + report.filePath + '/' + report.fileName + '<br/>';
			html += '	날짜: ' + reRunYmdObj.beginYmd + ' ~ ' + reRunYmdObj.endYmd + '<br/><br/>';
		}

		log.debug(`[G-RVN-SHAR] revenue-sharing-report-csv.service._announceReRun() :: html:${html}`);

		// 수신인에 따른 메일 발송
		const receivers = await _getReAggregationAlarmReceivers(schedule._id, statType);
		if (!_.isNil(receivers)) {
			receivers.forEach(to => {
				mailer.sendMail({to, subject, html});
			});
		}
	} catch (err) {
		log.error(`[G-RVN-SHAR] revenue-sharing-report-csv.service._announceReRun() :: 수익쉐어 리포트 재생성 알림 메일 발송 실패. err: ${err.stack}`);
	}
};

/**
 * 스케줄에 해당하는 SummaryRevenueSharingReports document 생성
 * @param scheduleId
 * @param beginYmd
 * @param endYmd
 * @returns {Promise.<void>}
 * @private
 */
const _createReport = async (scheduleId, bundleId, beginYmd, endYmd, dateForFileName) => {
	const report = {
		'summaryRevenueSharingSchedule_id': ObjectId(scheduleId),
		'bundleId': ObjectId(bundleId),
		'beginYmd': beginYmd,
		'endYmd': endYmd,
		'dateForFileName': dateForFileName,
		'msg': cd.RevenueSharingReportProgress.STANDBY.code, // 개발자용 모니터링
		'progress': cd.RevenueSharingReportProgress.STANDBY.code,
		'modifiedAt': moment()
	};
	const res = await SummaryRevenueSharingReport.create(report);
	return res._id;
};

/**
 * 스케줄에 해당하는 최신 리포트 조회
 * @param scheduleId
 * @param ymd
 * @returns {Promise.<boolean>}
 * @private
 */
const _getReportBySchedule = module.exports.getReportBySchedule = async (scheduleId, endYmd) => {
	const filter = {
		'summaryRevenueSharingSchedule_id': ObjectId(scheduleId),
		'endYmd': endYmd
	};

	const report = await SummaryRevenueSharingReport
		.findOne(filter)
		.sort({'_id': -1});

	return report;
};

const _getReportsBySchedule = async (scheduleId, endYmd) => {
	const filter = {
		'summaryRevenueSharingSchedule_id': ObjectId(scheduleId),
		'endYmd': endYmd
	};

	const reports = await SummaryRevenueSharingReport
		.find(filter)
		.sort({'_id': -1});

	return reports;
};

/**
 * 로그를 찍기 위한 메시지 프리픽스
 * schInfo: {
 *     txId,
 *     schedule,
 *     beginYmd,
 *     endYmd,
 *     timeSetting
 * }
 * @type {function(*, *)}
 */
const getLogPrefix = module.exports.getLogPrefix = (schInfo) => {
	return 'TXID:' + schInfo.txId +
		' PUB:' + schInfo.schedule.publisherName + '(' + schInfo.schedule.publisher_id + ')' +
		' SCH:' + schInfo.schedule.name + '(' + schInfo.schedule._id + ')' +
		' KG:' + schInfo.schedule.keyGroup.name + '(' + schInfo.schedule.keyGroup_id + ')' +
		' DATE:' + schInfo.beginYmd + '~' + schInfo.endYmd +
		' BUNDLE:' + schInfo.bundleId + '(' + schInfo.bundleState + ')';
};

/**
 * 이 키그룹에 해당하는 AdProvider들의 리포트가 완료되었는지 검사.
 * 모두 완료되어야지만 true
 *
 * @param keyGroupId
 * @param ymd
 * @returns {Promise.<boolean>}
 * @private
 */
const getAdpReportsState = module.exports.getAdpReportsState = async (publisherId, keyGroupId, ymd, logPrefix) => {
	log.debug(`[G-RVN-SHAR] ${logPrefix} AP 연동 리포트 완료 여부 체크`);

	const adProviders = await AdUnit.aggregate([
		{
			'$match': {
				'rsKeyGroup_id': ObjectId(keyGroupId)
			}
		},

		// MappingHistory
		{
			'$lookup': {
				from: 'MappingHistory',
				foreignField: 'adUnitId',
				localField: 'adUnitId',
				as: 'hist'
			}
		},

		// AdProviderPlaces
		{
			'$lookup': {
				from: 'AdProviderPlaces',
				foreignField: '_id',
				localField: 'hist.adProviderPlace_id',
				as: 'place'
			}
		},

		// AdProviderInfos
		{
			'$lookup': {
				from: 'AdProviderInfos',
				foreignField: '_id',
				localField: 'place.adProviderInfo_id',
				as: 'info'
			}
		},

		// AdProviders
		{
			'$lookup': {
				from: 'AdProviders',
				foreignField: '_id',
				localField: 'info.adProvider_id',
				as: 'adp'
			}
		},

		{
			'$project': {
				'adProvider_id': '$adp._id',
			}
		},

		{ '$unwind': '$adProvider_id' },

		{
			'$group': {
				'_id': '$adProvider_id',
			}
		}
	]);

	const aProvider_ids_org = adProviders.map(ap => ap._id);

	// [Data] PlaceKey가 존재하지 않는 Place 대응 (https://jira.navercorp.com/browse/GFP-677) - MpppingHistory에 없는 플레이스 처리
	// [DATA] GFD 연동 리포트 타임존 변경 영향 검토 (https://jira.navercorp.com/browse/GFP-1008)
	const adProvider_ids = await _addNaverDirectApIdIfNeeded(aProvider_ids_org, publisherId);

	const aps = await AdProvider.find({_id: {'$in': adProvider_ids}}, {
		_id: 1,
		name: 1,
		adProviderType: 1,
		'reportApi.type': 1,
		'reportApi.period.end': 1
	});
	const completeAdps = [];
	const uncompleteAdps = [];
	let apSchedules = ''; // 로깅 용
	for (let i = 0; i < aps.length; i++) {
		const ap = aps[i]._doc;

		// AP리포트 연동 상태 조회
		const filter2 = {};
		filter2['publisher_id'] = ObjectId(publisherId);
		filter2['adProvider_id'] = ObjectId(ap._id);
		const adpInfo = await AdProviderInfo.findOne(filter2).select('reportApiStatus');

		// AP리포트 연동 상태 = ON
		if (adpInfo.reportApiStatus === 'ON') {
			// AP 스케줄 조회
			const filter = {'period.endDate': ymd};
			if (ap.adProviderType === 'OUTSIDE') {				// OUTSIDE AP일 때는 pub_id + ap_id로 조회
				filter['publisher_id'] = ObjectId(publisherId);
				filter['adProvider_id'] = ObjectId(ap._id);
			} else {											// IN_NAVER AP일 때는 reportApiType으로만 조회
				filter['reportApiType'] = ap.reportApi.type;
			}
			const adpSchedule = await BatchReportJobSchedule.findOne(filter, {
				'reportApiType': 1,
				'period.endDate': 1,
				'mongoState': 1
			});

			// 스케줄이 없으면
			if (_.isNil(adpSchedule)) {
				if (_.has(ap, 'reportApi.period.end')) {
					log.debug(`[G-RVN-SHAR] ${logPrefix} apId:${ap._id}(${ap.name}) infoId:${adpInfo._id} AdProviderInfos.reportApiStatus:${adpInfo.reportApiStatus} 인데 AP 스케줄이 없음`);

					/*
						[ 2024.02.27 ]
						- [DATA] 수익쉐어 리포트 빈 통으로 생성(https://oss.navercorp.com/da-ssp/bts/issues/2203)
						- 원인: 0시 근방에 AP스케줄들이 등록되는데 수익쉐어 리포트 생성이 AP 스케줄 생성 전에 도는 바람에 모두 빈통으로 생김.
						- 해결: 이에 이 키값그룹과 연결된 AP의 스케줄이 있는지를 확인하고 더불어 연동 정보가 기술되어 있는지를 확인함
						- 결국 AP리포트 연동 상태 = ON && 리포트 연동 정보(reportApi.period.end)가 있는데 스케줄이 없으면 아직 안 생긴 것

						[ 2024.07.16 ]
						- [DATA] 수익쉐어 리포트 - AP별 스케줄 생성 여부 보지 않고 오늘 자에 생성된 스케줄이 있는지를 보도록 변경(https://jira.navercorp.com/projects/GFP/issues/GFP-18?filter=allopenissues)
						- 현상1: "2024.02.27" 수정으로 인해 매체에 AP가 새로 연결되면 해당 AP의 스케줄이 등록되지 않아 오늘 만들어야 할 수익쉐어 리포트를 못만들게 됨.
						- 현상2: 과거 일자 재처리 시에도 새로 연결된 AP의 스케줄은 없기 때문에 못만듦
						- 해결
							- "2024.02.27" 문제를 해결하면서도 정상적으로 처리되어야 할 AP들을 포함시키기 위해
							- 오늘 등록된 AP 스케줄이 하나라도 있으면 이 키값그룹과 관련된 AP 스케줄은 모두 있다고 보고 리포트 생성함
					 */
					// 오늘 등록된 AP 스케줄이 하나라도 있는지
					const todayRegisteredAdpSchedule = await BatchReportJobSchedule.findOne({ymd: moment().format("YYYYMMDD")}, {'ymd': 1});
					if (todayRegisteredAdpSchedule) {
						// 하나라도 있다면 등록되어야 할 AP들의 스케줄은 있다고 보고 진행
						log.debug(`[G-RVN-SHAR] ${logPrefix} 오늘 등록된 AP 스케줄이 적어도 하나 있으므로 진행`);
					} else {
						// AP 스케줄이 아예 하나도 없으므로 수익쉐어 리포트 생성 불가
						// 연동상태=ON && AdProviders.reportApi.period.end도 없고(연동정보도 기술되어 있지 않고) && AP 스케줄이 아예 없으므로 생성 불가
						const notExistedReportApiScheduleResult = {
							"adProviderName": ap.name,
							"reportApiType": '-',
							"mongoState": '-',
							"howManyDaysBefore": ap.reportApi.period.end,
						};
						uncompleteAdps.push(notExistedReportApiScheduleResult);
						log.debug(`[G-RVN-SHAR] ${logPrefix} 오늘 등록된 AP 스케줄이 하나도 없으므로 생성 불가`);
					}
					apSchedules += '\n\t' + ap.name + ':-(-)';
				} else {
					// AP리포트 연동 상태 = ON이나 AP의 reportApi.period.end가 없는 경우도 있음. 이런 경우는 스킵
					// 이런 목록 조회는 코드 맨 아래 "MongoDB Query - (1)" 확인
					log.debug(`[G-RVN-SHAR] ${logPrefix} apId:${ap._id}(${ap.name}) infoId:${adpInfo._id} AdProviderInfos.reportApiStatus:${adpInfo.reportApiStatus} AdProviders.reportApi:${JSON.stringify(ap.reportApi)} AdProviders.reportApi.period.end가 설정되어 있지 않으므로 스킵`);
				}
			} else { // 스케줄이 있고
				const reportApiScheduleResult = {
					"adProviderName": ap.name,
					"reportApiType": adpSchedule.reportApiType,
					"mongoState": adpSchedule.mongoState,
					"howManyDaysBefore": ap.reportApi.period.end,
				};
				log.debug(`[G-RVN-SHAR] ${logPrefix} apId:${ap._id}(${ap.name}) infoId:${adpInfo._id} AdProviderInfos.reportApiStatus:${adpInfo.reportApiStatus} AP 스케줄이 있고 adpSchedule.mongoState=${adpSchedule.mongoState}`);

				// 완료
				if (adpSchedule.mongoState === 'COMPLETE') {
					completeAdps.push(reportApiScheduleResult);
				} else { // 완료 아님
					uncompleteAdps.push(reportApiScheduleResult);
				}
				apSchedules += '\n\t' + ap.name + ':' + adpSchedule.reportApiType + '(' + adpSchedule.mongoState + ')';
			}
		} else {
			// AdProviderInfos.reportApiStatus = 'OFF'인 경우 AP리포트연동 배치가 '비활성'되어 스케줄이 안 생기므로 스킵
			log.debug(`[G-RVN-SHAR] ${logPrefix} apId:${ap._id}(${ap.name}) infoId:${adpInfo._id} AdProviderInfos.reportApiStatus:${adpInfo.reportApiStatus} 인 경우 AP리포트연동 배치가 '비활성'되어 AP 스케줄이 안 생기므로 스킵`);
		}
	}
	log.debug(`[G-RVN-SHAR] ${logPrefix} ApSchedules:${apSchedules}`);

	return {completeAdps, uncompleteAdps};
};

/**
 * [Data] PlaceKey가 존재하지 않는 Place 대응 (https://jira.navercorp.com/browse/GFP-677) - MpppingHistory에 없는 플레이스 처리
 * 	- 이 매체와 엮인 AP 중 adProviderCd == 'NAVER_DIRECT' 인 AP IDS 구함
 * 	- 리턴 값이 배열이기는 하나, 실제로는 하나의 값만 리턴됨
 *
 * @param publisher_id
 * @returns {Promise<*>}
 * @private
 */
const _getNaverDirectAdProviderIds = module.exports.getNaverDirectAdProviderIds = async (publisher_id) => {
	const infos = await AdProviderInfo.aggregate([
		{
			'$match': {
				'publisher_id': ObjectId(publisher_id),
			}
		},
		{
			'$lookup': {
				from: 'AdProviders',
				foreignField: '_id',
				localField: 'adProvider_id',
				as: 'ap'
			}
		},
		{
			'$unwind': {
				'path': '$ap',
				'preserveNullAndEmptyArrays': false
			}
		},
		{
			'$match': {
				'$or': [
					{ 'ap.adProviderCd': 'NAVER_DIRECT' },
				]
			}
		},
		{
			'$project': {
				'_id': 0,
				'adProvider_id': 1
			}
		}
	]);

	return infos.map(info => info.adProvider_id);
};


/**
 * [Data] PlaceKey가 존재하지 않는 Place 대응 (https://jira.navercorp.com/browse/GFP-677) - MpppingHistory에 없는 플레이스 처리
 * 	- "NAVER Direct"(AdProviders.adProviderCd == 'NAVER_DIRECT') AP는 MappingHistory에 존재하지 않아 AP 도착 시간을 MappingHistory을 통해서 알 수 없으므로
 * 	- 이 매체에 "NAVER Direct"가 엮여 있다면 무조건 "AdProviders.reportApi.type" == "GFD" 리포트가 도착했는지 확인하도록 함.
 *
 * [DATA] GFD 연동 리포트 타임존 변경 영향 검토 (https://jira.navercorp.com/browse/GFP-1008)
 *
 * @param adProvider_ids
 * @param publisherId
 * @returns {Promise<*>}
 * @private
 */
const _addNaverDirectApIdIfNeeded = async (adProvider_ids, publisherId) => {
	adProvider_ids.forEach((adProvider_id) => {
		log.debug(`[G-RVN-SHAR] before AP 리포트가 도착했는지 확인하기 위한 adProvider_id: ${adProvider_id}`);
	});

	// NAVER_DIRECT AP id 조회
	const naverDirectAp_ids = await _getNaverDirectAdProviderIds(publisherId)
	log.debug(`[G-RVN-SHAR] 이 매체(${publisherId})에 NAVER_DIRECT(GFD) AP가 엮여 있다면 해당 AP id: ${naverDirectAp_ids}`);

	// adProvider_ids에 naverDirectAp_ids가 포함되어 있지 않다면 추가
	const isNaverDirectIncluded = naverDirectAp_ids.some(id => adProvider_ids.includes(id));
	if (!isNaverDirectIncluded) adProvider_ids.push(naverDirectAp_ids);

	adProvider_ids.forEach((adProvider_id) => {
		log.debug(`[G-RVN-SHAR] after AP 리포트가 도착했는지 확인하기 위한 adProvider_id: ${adProvider_id}`);
	});

	return adProvider_ids;
}

/**
 * GFP 통계 집계가 끝났는지 검사
 *
 * @param date
 * @returns {Promise<boolean>}
 * @private
 */
const isGfpReportsComplete = module.exports.isGfpReportsComplete = async (date, logPrefix) => {
	let isComplete = false;

	const gfpStatsSchedule = await SummaryRevenueSharingGfpStatsSchedule.findOne({'date': date}).select('sparkAppState');

	if (_.isNil(gfpStatsSchedule)) {
		isComplete = false; // GFP 통계 스케줄이 아직 안 만들어졌음
	} else {
		if (gfpStatsSchedule.sparkAppState === 'COMPLETE') {
			isComplete = true;
		} else {
			if (gfpStatsSchedule.sparkAppState === 'FAILURE') {
				log.error(`[G-RVN-SHAR] ${logPrefix} GFP통계상태(SummaryRevenueSharingGfpStatsSchedule.sparkAppAtate)=FAILURE 라서 수익쉐어 리포트 못 만들고 있음.`);
			}
			isComplete = false;
		}
	}

	return isComplete;
};

/**
 * 스케줄 x (GFP or AdProvider)에 따른 알림 수신자 이메일 주소 조회
 *
 * @param scheduleId
 * @param statsType
 * 			STAT_TYPE_GFP = "GFP"
 * 			STAT_TYPE_AP = "AP"
 * @returns {Promise<[StringConstructor | String]>}
 */
const _getReAggregationAlarmReceivers = async (scheduleId, statsType) => {
	const schedule = await SummaryRevenueSharingSchedule.findOne({'_id': ObjectId(scheduleId)});

	/*
		"reAggregationAlarmReceiver" : {
			"ap" : [
				"<EMAIL>"
			],
			"gfp" : [
				"<EMAIL>"
			]
		}
	 */
	let receivers = [];
	if (statsType === STAT_TYPE_GFP) {
		receivers = schedule.reAggregationAlarmReceiver.gfp;
	} else {
		receivers = schedule.reAggregationAlarmReceiver.ap;
	}

	return receivers
};


/*
-- MongoDB Query - (1)
-- AP 리포트 연동 상태(AdProviderInfos.reportApiStatus = 'ON')는 켜져있는데 AP의 reportApi가 없는 경우
db.AdProviderInfos.aggregate([
{
    $match: {
//        _id: ObjectId('5f3de6cca4ad0b0031bd4be0')
        reportApiStatus: 'ON'
    }
},
// AdProviders
{
	'$lookup': {
		from: 'AdProviders',
		foreignField: '_id',
		localField: 'adProvider_id',
		as: 'ap'
	}
},
{'$unwind': '$ap'},
// AdProviders
{
	'$lookup': {
		from: 'Publishers',
		foreignField: '_id',
		localField: 'publisher_id',
		as: 'pub'
	}
},
{'$unwind': '$pub'},
{
    $match: {
       'ap.reportApi.period': {$exists: false}
    }
},
{
    $project: {
        pub_id: '$pub._id',
        pub_name: '$pub.name',
        ap_id: '$ap._id',
        ap_name: '$ap.name',
        ap_reportApi: '$ap.reportApi'
    }
},
{
    $sort: { pub_name:1, ap_name: 1}
}
]);
 */
