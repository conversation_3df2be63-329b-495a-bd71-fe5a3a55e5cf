'use strict';

import _ from 'lodash';
import moment from 'moment';

import { BusinessError } from '../../common/error';

import * as logger from '../../utils/logger.util';

import * as reportApiService from '../reportapi/report-api.service';

const NON_RK_LOGGER = 'report_api_non_rk';


/**
 * [PROCESS 3:: TASK 2] processReportApi : ALGORIX_S2S 리포트 API 연동 및 리포트 파일 다운로드
 *  SUBTASK 1. initCsvReport : csv 파일 초기화
 *  SUBTASK 2. 리포트 데이터 생성 요청 및 다운로드
 *        2-1. _requestReport : 리포트 데이터 생성 요청하기
 *        2-2. _getDataList : 리포트 데이터 가공하기
 *        2-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
 * 
 * @RequestMapping(value='/batch/day/outside/algorix_s2s')
 *
 * @param {Object} adProviderMetaInfo 
 * {
		reportInfos: { USERID, AUTHORIZATION },
		reportInfoExt: { apiResultPath, fields, dimensions, metrics, report_request_api },
		period: { startDate, endDate },
		fda, countryMappings
 * }
 */
module.exports.processReportApi = async ({ reportInfos: { USERID, AUTHORIZATION }, reportInfoExt: { apiResultPath, fields, dimensions, metrics, report_request_api }, period: { startDate, endDate }, fda, countryMappings }) => {
	logger.debug(NON_RK_LOGGER, '[algorix-s2s.service :: processReportApi] 호출됨');


	/* SUBTASK 1. initCsvReport : csv 파일 초기화 */
	// 필드 순서 : ymd, app_id, country, imp, net_revenue
	await reportApiService.initCsvReport({ fda, fields });


	/*
		SUBTASK 2. 리포트 데이터 생성 요청 및 다운로드
	*/

	// SUBTASK 2-1. _requestReport : 리포트 데이터 생성 요청하기
	const rawDataList = await _requestReport({ report_request_api, dimensions, metrics, period: { startDate, endDate }, reportInfos: { USERID, AUTHORIZATION } });

	// SUBTASK 2-2. _getDataList : 리포트 데이터 가공하기
	const dataList = await _getDataList({ fields, rawDataList, countryMappings });

	// SUBTASK 2-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
	await reportApiService.writeCsvReport({ fda, apiResultPath, fields, dataList });
};


/**
 * SUBTASK 2-1. _requestReport : 리포트 데이터 생성 요청하기
 *
 * @param {Object} 리포트 조회 조건 { report_request_api, dimensions, metrics, period: { startDate, endDate }, reportInfos: { USERID, AUTHORIZATION } }
 * @return {Array} rawDataList
 */
const _requestReport = async ({ report_request_api, dimensions, metrics, period: { startDate, endDate }, reportInfos: { USERID, AUTHORIZATION } }) => {
	logger.debug(NON_RK_LOGGER, `[algorix-s2s.service :: _requestReport] 리포트 데이터 생성 요청`);

	const headers = {
		'x-userid': USERID,
		'x-authorization': AUTHORIZATION,
		'Content-Type': 'application/json'
	};

	const body = {
		start: moment(startDate).format('YYYY-MM-DDT00:00:00'),
		end: moment(endDate).add(1, 'days').format('YYYY-MM-DDT00:00:00'),
		dimensions,
		metrics,
		limit: 50000 // max 50000
	};

	// error res
	// 	- { status: { code: -2, msg: 'invalid params.' }, timestamp: '', data:[] }
	// res : { status: { code: 0, msg: 'success' }, data: { total: 0, data: [ ] } }
	const res = await reportApiService.requestApi({ url: report_request_api, method: 'POST', headers, body });

	// [ERROR] 응답 결과에 err가 있는 경우, 에러 처리
	if (!_.isNil(res.err)) {
		throw new BusinessError({ message: `[algorix-s2s.service :: _requestReport] API 요청 실패 ::: statusCode(${res.status}), url(${res.url})` }, { err: res });
	}

	// [ERROR] 응답 결과에 status, data 에 값이 없는 경우, 에러 처리
	if (_.isNil(res.status) || _.isNil(res.data) || _.isNil(res.data.data)) {
		throw new BusinessError({ message: `[algorix-s2s.service :: _requestReport] 리포트 데이터 생성 요청 실패 ::: ${JSON.stringify(res, null, 2)}` });
	}

	// [ERROR] 응답 결과에 status.code 가 0 이 아닌 경우, 에러 처리
	// Status Code
	// 	- 0: success / -1: system error / -2: invalid params. / -3: token authorized failed. / -4: date range more than one month. / -5: the old account request start date must be after March 1st, 2020.
	if (!_.isEmpty(res.status) && !_.isEqual(res.status.code, 0)) {
		throw new BusinessError({ message: `[algorix-s2s.service :: _requestReport] 리포트 데이터 생성 요청 실패 ::: ${JSON.stringify(res, null, 2)}` });
	}

	const rawDataList = res.data.data;

	logger.debug(NON_RK_LOGGER, `[algorix-s2s.service :: _requestReport] 리포트 데이터 생성 요청 완료`);

	return rawDataList;
};


/**
 * SUBTASK 2-2. _getDataList : 리포트 데이터 가공하기
 *
 * @param {Object} { fields: { country, imp, net_revenue }, rawDataList, countryMappings }
 * @return {Array} dataList
 */
const _getDataList = async ({ fields: { country, imp, net_revenue }, rawDataList, countryMappings }) => {
	logger.debug(NON_RK_LOGGER, `[algorix-s2s.service :: _getDataList] 리포트 데이터 가공`);

	let dataList = new Array();

	// [{ date: '2023-01-18', app_bundle_id: '174766', country: 'KOR', impression: 3, net_revenue: 0 } ]
	rawDataList.map(rawData => {
		// imp, net_revenue 가 모두 0인 건들은 걸러낸다.
		if (_.isEqual(rawData[imp], 0) && _.isEqual(rawData[net_revenue], 0)) {
			return;
		}

		let data = Object.assign({}, rawData);

		// GFP에서 지원하는 타겟 국가인 경우, 국가 코드로 변환
		data[country] = (!_.isNil(countryMappings[data[country]])) ? countryMappings[data[country]] : data[country];

		dataList.push(data);
	});

	return dataList;
};
