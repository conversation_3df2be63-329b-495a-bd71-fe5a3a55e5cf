'use strict';

import _ from 'lodash';

import { BusinessError } from '../../common/error';

import * as logger from '../../utils/logger.util';

import * as reportApiService from '../reportapi/report-api.service';

const NON_RK_LOGGER = 'report_api_non_rk';


/**
 * [PROCESS 3:: TASK 2] processReportApi : CRITEO 리포트 API 연동 및 리포트 파일 다운로드
 * 
 *  SUBTASK 1. initCsvReport : csv 파일 초기화
 *  SUBTASK 2. 리포트 데이터 생성 요청 및 다운로드
 * 		2-1. _requestReport : 리포트 데이터 생성 요청하기
 * 		2-2. _getDataList : 리포트 데이터 가공하기
 * 		2-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
 * 
 * @RequestMapping(value='/batch/day/outside/criteo')
 * 
 * @param {Object} adProviderMetaInfo 
 * { 
		reportInfos: { API_TOKEN },
		reportInfoExt: { apiResultPath, fields, dimensions, metrics, report_request_api },
		period: { startDate, endDate },
		fda
 * }
 */
module.exports.processReportApi = async ({ reportInfos: { API_TOKEN }, reportInfoExt: { apiResultPath, fields, dimensions, metrics, report_request_api }, period: { startDate, endDate }, fda }) => {
	logger.debug(NON_RK_LOGGER, '[criteo.service :: processReportApi] 호출됨');


	/* SUBTASK 1. initCsvReport : csv 파일 초기화 */
	// 필드 순서 : ymd, subid, country, imp, clk, net_revenue
	await reportApiService.initCsvReport({ fda, fields });


	/*
		SUBTASK 2. 리포트 데이터 생성 요청 및 다운로드
	*/

	// SUBTASK 2-1. _requestReport : 리포트 데이터 생성 요청하기
	const rawDataList = await _requestReport({ API_TOKEN, dimensions, metrics, report_request_api, startDate, endDate, fields });

	// SUBTASK 2-2. _getDataList : 리포트 데이터 가공하기
	const dataList = await _getDataList({ fields, rawDataList });

	// SUBTASK 2-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
	await reportApiService.writeCsvReport({ fda, apiResultPath, fields, dataList });
};


/**
 * SUBTASK 2-1. _requestReport : 리포트 데이터 생성 요청하기
 *
 * @param {Object} 리포트 조회 조건 { API_TOKEN, dimensions, metrics, report_request_api, startDate, endDate}
 * @return {Array} rawDataList
 */
const _requestReport = async ({ API_TOKEN, dimensions, metrics, report_request_api, startDate, endDate }) => {
	logger.debug(NON_RK_LOGGER, `[criteo.service :: _requestReport] 리포트 데이터 생성 요청`);

	const requestApiUrl = `${report_request_api}?apitoken=${API_TOKEN}&dimensions=${dimensions.join(',')}&metrics=${metrics.join(',')}&begindate=${startDate.format('YYYY-MM-DD')}&enddate=${endDate.format('YYYY-MM-DD')}`;

	// res : [{ TimeId, Subid, AdvertiserCountry, CriteoDisplays, Clicks, Revenue }]
	const res = await reportApiService.requestApi({ url: requestApiUrl });

	// [ERROR] 응답 결과에 err가 있는 경우, 에러 처리
	if (!_.isNil(res.err)) {
		throw new BusinessError({ message: `[criteo.service :: _requestReport] 리포트 데이터 생성 요청 실패` }, { err: res, detail: JSON.stringify(res, null, 0) });
	}

	const rawDataList = res;

	logger.debug(NON_RK_LOGGER, `[criteo.service :: _requestReport] 리포트 데이터 생성 요청 완료`);

	return rawDataList;
};


/**
 * SUBTASK 2-2. _getDataList : 리포트 데이터 가공하기
 *
 * @param {Object} { fields: { imp, clk, net_revenue }, rawDataList }
 * @return {Array} dataList
 */
const _getDataList = async ({ fields: { imp, clk, net_revenue }, rawDataList }) => {
	logger.debug(NON_RK_LOGGER, `[criteo.service :: _getDataList] 리포트 데이터 가공`);

	let dataList = new Array();

	rawDataList.map(rawData => {
		// imp, clk, net_revenue 가 모두 0인 건들은 걸러낸다.
		if (_.isEqual(rawData[imp], 0) && _.isEqual(rawData[clk], 0) && _.isEqual(rawData[net_revenue], 0)) {
			return;
		}

		let data = Object.assign({}, rawData);

		// 리포트 파일(csv) 생성 시, clk 은 optional 이라 0인 경우 공백 처리되므로, 스트링으로 변환해준다.
		data[clk] = data[clk] + '';

		dataList.push(data);
	});

	return dataList;
};
