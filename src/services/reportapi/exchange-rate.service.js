'use strict';


import _ from 'lodash';
import moment from 'moment';
import rp from 'request-promise';

import * as logger from '../../utils/logger.util';

import { BusinessError } from '../../common/error';

import { ExchangeRate } from '../../models/exchange-rates.schema';
import { ExchangeRateAverage } from '../../models/exchange-rate-averages.schema';


/**
 * PROCESS 2. processExchangeRateApi : NEON 환율 API 연동
 * 	USD <=> KRW
 *
 * @RequestMapping(value='/batch/day/exchange')
 * @param {String} neonUrl NEON API URL (http://neon.navercorp.com:5001)
 * @param {String} date YYYYMMDD
 * @param {Object} info 환율 정보 { from:'USD', to:'KRW' }
 * @return {Object} data { yyyymmdd, currencyCdFrom, currencyCdTo, exchangeRate }
 */
module.exports.processExchangeRateApi = async (neonUrl, date, { from, to }) => {
	logger.debug(`[exchange-rate.service :: processExchangeRateApi] 호출됨 date(${date}) from(${from}) to(${to})`);

	// NEON 환율 API URL
	// required : EMPLOYEE_NO, exchangeType, exchangeDateStr
	const url = `${neonUrl}/nfi/InterfaceInfoBO/getExchangeRateList?EMPLOYEE_NO=AD_REQUEST_USER&exchangeType=COMPANY&exchangeDateStr="${date}"&currencyCdFrom=${from}&currencyCdTo=${to}`;

	const response = await rp({
		uri: url,
		json: true,
		timeout: 60000, 
	});

	// [ERROR] 응답 에러 처리
	if (_.isNil(response) || response.SUCCESS !== 'Y') {
		throw new BusinessError({ message: `[exchange-rate.service :: processExchangeRateApi] NEON Response Error` }, { err: response, detail: JSON.stringify(response, null, 2)});
	}

	// [ERROR] 응답 결과 없는 경우 처리 : 미래 날짜 or 은행과 데이터 싱크가 안 된 경우
	if(!_.isNil(response.RESULT) && response.RESULT.length < 1) {
		throw new BusinessError({ message: `[exchange-rate.service :: processExchangeRateApi] No Result Data` }, { err: response, detail: JSON.stringify(response, null, 2)});
	}

	const data = { 
		yyyymmdd: date,
		currencyCdFrom: response.RESULT[0].currencyCdFrom,
		currencyCdTo: response.RESULT[0].currencyCdTo,
		exchangeRate: response.RESULT[0].exchangeRate['json.expansion@value'],
	};

	return data;
};


/**
 * PROCESS 3. upsertExchangeRates : ExchangeRates DB 저장
 *
 * @RequestMapping(value='/batch/day/exchange')
 * @param {Object} exchangeRates { yyyymmdd, currencyCdFrom, currencyCdTo, exchangeRate }
 */
module.exports.upsertExchangeRates = async ({ yyyymmdd, currencyCdFrom, currencyCdTo, exchangeRate }) => {
	// logger.debug('[exchange-rate.service :: upsertExchangeRates] 호출됨');

	// 환율 정보 추가
	await ExchangeRate.findOneAndUpdate(
		{ yyyymmdd, currencyCdFrom, currencyCdTo },
		{ 
			$set: { 
				yyyymmdd, currencyCdFrom, currencyCdTo, exchangeRate,
				createdAt: new Date()
			}
		},
		{ new: true, runValidators: true, upsert: true }
	).exec();
};


/**
 * PROCESS 4. upsertExchangeRateAverages : ExchangeRateAverages DB 저장
 * 시작일, 종료일 사이의 평균 환율 구하기
 *
 * @RequestMapping(value='/batch/day/exchange')
 * @param {String} startDate
 * @param {String} endDate
 * @param {String} currencyCdFrom
 * @param {String} currencyCdTo
 */
module.exports.upsertExchangeRateAverages = async (startDate, endDate, currencyCdFrom, currencyCdTo) => {
	// logger.debug('[exchange-rate.service :: upsertExchangeRateAverages] 호출됨');

	const exchangeRates = await ExchangeRate.aggregate()
		.match({
			yyyymmdd: { $gte: startDate, $lte: endDate }, 
			currencyCdFrom: { $eq: currencyCdFrom },
			currencyCdTo: { $eq: currencyCdTo },
		})	
		.sort({ yyyymmdd: 1 }) // yyyymmdd로 정렬
		.project({
			_id : 0,
			yyyymmdd:1, 
			currencyCdFrom:1,
			currencyCdTo:1,
			rate:'$exchangeRate',
		})
		.exec();

	// [ERROR] 환율 정보가 없는 경우, 에러 처리 
	if (_.isEmpty(exchangeRates)) {
		throw new BusinessError({ message: `[exchange-rate.service :: upsertExchangeRateAverages] 일자별 환율 정보 없음` });
	}

	// 평균 계산에서 제외된 날짜 정보 (환율 정보에 없음)
	const excludedDate = await _getExcludedDate(startDate, endDate, exchangeRates);

	// Decimal128 타입인 환율을 float 타입으로 변환
	const rates = exchangeRates.map(exchangeRate => parseFloat(exchangeRate.rate));

	// 평균 환율 정보 추가
	await ExchangeRateAverage.findOneAndUpdate(
		{ startDate, endDate, currencyCdFrom, currencyCdTo },
		{ 
			$set: { 
				startDate, endDate, currencyCdFrom, currencyCdTo, exchangeRate: _.mean(rates), excludedDate, 
				createdAt: new Date()
			}
		},
		{ new: true, runValidators: true, upsert: true }
	).exec();
};


/**
 * _getExcludedDate : 환율 정보 중, 시작일 ~ 종료일 사이에 없는 날짜 구하기
 *
 * @param {String} startDate
 * @param {String} endDate
 * @param {Array} exchangeRates
 * @return {String} excludedDate
 */
const _getExcludedDate = async (startDate, endDate, exchangeRates) => {
	let excludedDate = '';

	let tempDate = moment(startDate);
	while (tempDate.isSameOrBefore(endDate)) {
		let date = tempDate.format('YYYYMMDD');
		if (exchangeRates.filter(rate => _.isEqual(rate.yyyymmdd, date)).length < 1) {
			excludedDate += date + ',';
		}

		tempDate.add(1, 'days');
	}

	return excludedDate;
};


/**
 * getExchangeRates : 시작일 ~ 종료일 환율 가져오기
 *
 * @param {String} startDate
 * @param {String} endDate
 * @param {String} currencyCdFrom
 * @param {String} currencyCdTo
 * @return {Object} exchangeRates
	{ 
		yyyymmdd: '20181015',
		currencyCdFrom: 'USD',
		currencyCdTo: 'KRW',
		exchangeRate: 1131.54
	}
 */
module.exports.getExchangeRates = async (startDate, endDate, currencyCdFrom, currencyCdTo) => {
	logger.debug('[exchange-rate.service :: getExchangeRates] 호출됨');

	let exchangeRates = await ExchangeRate.aggregate()
		.match({ 
			yyyymmdd: { $gte: startDate, $lte: endDate }, 
			currencyCdFrom,
			currencyCdTo, 
		})
		.project({
			yyyymmdd:1, 
			currencyCdFrom:1,
			currencyCdTo:1,
			exchangeRate:1,
		})
		.exec();

	// [ERROR] 환율 정보가 없는 경우, 에러 처리
	if (_.isEmpty(exchangeRates)) {
		throw new BusinessError({ message: `[exchange-rate.service :: getExchangeRates] 환율 정보 없음` });
	}

	// Decimal128 타입인 환율을 float 타입으로 변환
	exchangeRates = exchangeRates.map(data => {
		data.exchangeRate = parseFloat(data.exchangeRate);
		return data;
	});

	return exchangeRates;
};


/**
 * getExchangeRateAverage : 시작일 ~ 종료일 평균 환율 가져오기
 *
 * @param {String} startDate
 * @param {String} endDate
 * @param {String} currencyCdFrom
 * @param {String} currencyCdTo
 * @return {Object} exchangeRates
	{ 
		startDate: '20181015',
		endDate: '20181113',
		from: 'USD',
		to: 'KRW',
		rate: 1131.54,
		excludedDate: ''
	}
 */
module.exports.getExchangeRateAverage = async (startDate, endDate, currencyCdFrom, currencyCdTo) => {
	logger.debug('[exchange-rate.service :: getExchangeRateAverage] 호출됨');

	let exchangeRates = await ExchangeRateAverage.aggregate()
		.match({ startDate, endDate, currencyCdFrom, currencyCdTo })
		.project({
			_id : 0,
			startDate:1, endDate:1, 
			currencyCdFrom:1, currencyCdTo:1,
			exchangeRate:1,
			excludedDate:1
		})
		.project({
			_id : 0,
			startDate:1, endDate:1, 
			from:'$currencyCdFrom',
			to:'$currencyCdTo',
			rate:'$exchangeRate',
			excludedDate:1
		})
		.exec();

	// [ERROR] 환율 정보가 없는 경우, 에러 처리
	if (_.isEmpty(exchangeRates)) {
		throw new BusinessError({ message: `[exchange-rate.service :: getExchangeRateAverage] 평균 환율 정보 없음` });
	}

	// Decimal128 타입인 환율을 float 타입으로 변환
	exchangeRates = exchangeRates.map(data => {
		data.rate = parseFloat(data.rate);
		return data;
	});

	exchangeRates = exchangeRates.pop();

	return exchangeRates;
};