'use strict';

import _ from 'lodash';
import https from 'https';
import moment from 'moment';
import stream from 'stream';
import csvParse from 'csv-parse';

import { BusinessError } from '../../common/error';

import * as logger from '../../utils/logger.util';

import * as reportApiService from '../reportapi/report-api.service';

const NON_RK_LOGGER = 'report_api_non_rk';

// MOPUB FADE OUT - 히스토리를 위해 service 만 남겨둠

/**
 * [PROCESS 3:: TASK 3-2] processReportApi : MOPUB 리포트 API 연동 및 리포트 파일 다운로드
 *  SUBTASK 1. initCsvReport : csv 파일 초기화
 *  SUBTASK 2. keepAlive https agent 설정
 *  SUBTASK 3. 93일치 기간을 7일 단위로 나눠서 처리
 * 		3-1. _getDateList : 시작일-종료일 간 날짜 리스트 가져오기
 * 		3-2. _requestReport : 날짜별 리포트 데이터 요청
 * 		3-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
 *  SUBTASK 4. https agent destroy
 * 
 * @RequestMapping(value='/batch/day/outside/mopub')
 * 
 * @param {Object} adProviderMetaInfo 
 * { 
		reportInfos: { API_KEY, REPORT_KEY },
		reportInfoExt: { apiResultPath, separator, fields, report_request_api },
		period: { startDate, endDate },
		fda, countryMappings
 * }
 */
module.exports.processReportApi = async ({ reportInfos: { API_KEY, REPORT_KEY }, reportInfoExt: { apiResultPath, separator, fields, report_request_api }, period: { startDate, endDate }, fda, countryMappings }) => {
	logger.debug(NON_RK_LOGGER, `[mopub.service :: processReportApi] 호출됨 ( 총 ${startDate.format('YYYY-MM-DD')} ~ ${endDate.format('YYYY-MM-DD')} )`);


	/* SUBTASK 1. initCsvReport : csv 파일 초기화 */
	// 필드 순서 : ymd, AD_UNIT_ID, country, os, adSource, imp, clk, net_revenue
	await reportApiService.initCsvReport({ fda, fields });


	/* SUBTASK 2. keepAlive https agent 설정 */
	const agent = new https.Agent({ keepAlive: true, maxSockets: 1 });


	/* SUBTASK 3. 30일치 기간을 2일 단위로 나눠서 처리 */
	let startDt = moment(startDate);
	let endDt = moment(startDate).add(6, 'days');

	// 7일씩 데이터를 받아서, 하나의 파일로 통합한다. 
	while (startDt.isSameOrBefore(endDate)) {
		logger.debug(NON_RK_LOGGER, `[mopub.service :: processReportApi] START ::: ( ${startDt.format('YYYY-MM-DD')} ~ ${endDt.format('YYYY-MM-DD')} )`);


		// SUBTASK 3-1. _getDateList : 시작일-종료일 간 날짜 리스트 가져오기
		const dateList = await _getDateList(startDt, endDt);


		// SUBTASK 3-2. _requestReport : 날짜별 리포트 데이터 요청
		const dataList = await _requestReport({ API_KEY, REPORT_KEY, separator, fields, report_request_api, dateList, countryMappings, agent });


		// SUBTASK 3-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
		await reportApiService.writeCsvReport({ fda, apiResultPath, fields, dataList });


		logger.debug(NON_RK_LOGGER, `[mopub.service :: processReportApi] END ::: ( ${startDt.format('YYYY-MM-DD')} ~ ${endDt.format('YYYY-MM-DD')} )`);


		// 시작일, 종료일을 2일 후로 셋팅
		startDt.add(7, 'days');
		endDt.add(7, 'days');

		// 마지막 날짜 셋팅
		if (endDate.isSameOrBefore(endDt)) {
			endDt = moment(endDate);
		}
	}


	/* SUBTASK 4. https agent destroy */
	destroyAgent(agent);


	logger.debug(NON_RK_LOGGER, `[mopub.service :: processReportApi] 리포트 파일 생성 완료 ( 총 ${startDate.format('YYYY-MM-DD')} ~ ${ endDate.format('YYYY-MM-DD')} )`);
};


/**
 * SUBTASK 3-1. _getDateList : 시작일-종료일 간 날짜 리스트 가져오기
 *
 * @param {Date} startDate 
 * @param {Date} endDate
 * @return {Array} dateList 날짜 리스트 ['2019-01-01', '2019-01-02', ... ]
 */ 
const _getDateList = async (startDate, endDate) => {
	// [ERROR] 날짜 값이 없을 경우, 에러 처리
	if(_.isNil(startDate) || _.isEmpty(startDate) || _.isNil(endDate) || _.isEmpty(endDate)) {
		throw new BusinessError({ message: `[mopub.service :: _getDateList] 날짜 정보 없음` });
	}

	// 날짜 리스트
	let dateList = new Array();

	// 날짜 리스트 생성(시작일 ~ 종료일)
	let tempDate = moment(startDate);
	while (tempDate.isSameOrBefore(endDate)) {
		dateList.push(tempDate.format('YYYY-MM-DD'));
		tempDate.add(1, 'days');
	}

	 return dateList;
};


/**
 * SUBTASK 3-2. _requestReport : 날짜별 리포트 데이터 요청
 *
 * @param {Object} reportQueryInfo 리포트 조회 조건 
	{ 
		API_KEY, REPORT_KEY,
		separator, fields, 
		report_request_api: 'https://app.mopub.com/reports/custom/api/download_report',
		dateList: ['2019-01-01', '2019-01-02', ... ],
		countryMappings, 
		agent
	}
 * @return {Array} dataList
 */
const _requestReport = async ({ API_KEY, REPORT_KEY, separator, fields, report_request_api, dateList, countryMappings, agent }) => {
	logger.debug(NON_RK_LOGGER, `[mopub.service :: _requestReport] 날짜별 리포트 데이터 요청 시작`);

	// [ERROR] 날짜 정보가 없을 경우, 에러 처리
	if(_.isNil(dateList) || _.isEmpty(dateList)) {
		throw new BusinessError({ message: `[mopub.service :: _requestReport] 날짜 정보 없음` });
	}

	// 리포트 데이터 리스트
	let dataList = new Array();

	// 날짜별 리포트 데이터 요청
	for(const date of dateList) {
		// 리포트 데이터 요청 URL
		const requestApiUrl = `${report_request_api}?api_key=${API_KEY}&report_key=${REPORT_KEY}&date=${date}`;

		// 리포트 데이터 요청
		let result = await _requestReportData({ separator, fields, requestApiUrl, date, countryMappings, agent });

		// 재요청 처리 (최대 15번까지 재요청히며, 그이상 실패할 경우 에러 처리)
		// 파일 사이즈가 매우 큰 상황에서, 로드된 파일 사이즈가 예상 파일 사이즈보다 작으면 로드가 덜 된 상황이므로 재요청 로직을 적용한다.
		if(result.retry) {
			const TIMEOUT = 15;
			let count = 0;

			while(count < TIMEOUT) {
				logger.debug(NON_RK_LOGGER, `[mopub.service :: _requestReport] 리포트 데이터 재요청중... ( date= ${date} )`);

				count++;
				result = await _requestReportData({ separator, fields, requestApiUrl, date, countryMappings });

				if(result.retry !== true) {
					break;
				}
			}
		}

		// [ERROR] 에러 메시지가 있을 경우, 에러 처리
		if(!_.isNil(result.message) && !_.isEmpty(result.message)) {
			// 정상 종료가 아닌 경우, agent socket destroy 처리
			destroyAgent(agent);

			throw new BusinessError({ message: `[mopub.service :: _requestReport] 날짜별 리포트 데이터 요청 실패` }, { err: result, detail: JSON.stringify(result, null, 0) });
		}

		// 해당 날짜에 데이터 저장
		dataList = _.concat(dataList, result);
	};

	logger.debug(NON_RK_LOGGER, `[mopub.service :: _requestReport] 날짜별 리포트 데이터 요청 완료`);

	return dataList;
};


/**
 * [Promise]
 * SUBTASK 3-3-1. _requestReportData : 리포트 데이터 요청
 *
 * @param {Object} { separator, fields: { ymd, place_keys: { AD_UNIT_ID }, country, os, adSource, imp, clk, net_revenue }, requestApiUrl, date, countryMappings, agent }
 * @return {Array} data 
	[
		{ 
			Day: '2019-01-29',
			Country: 'JP',
			OS: 'iPhone OS',
			'AdUnit ID': 'dee72cad3581492baa0bed4d02613b02',
			Impressions: '0',
			Clicks: '0',
			Revenue: '0'
		}, 
		... 
	]
 */
const _requestReportData = ({ fields: { ymd, place_keys: { AD_UNIT_ID }, country, os, adSource, imp, clk, net_revenue }, requestApiUrl, date, countryMappings, agent }) => {
	logger.debug(NON_RK_LOGGER, `[mopub.service :: _requestReportData] 리포트 데이터 요청 시작 ( date= ${date} )`);

	return new Promise(async (resolve, reject) => {
		try{
			// [ERROR] 요청 URL이 없을 경우, 에러 처리
			if(_.isNil(requestApiUrl) || _.isEmpty(requestApiUrl)) {
				resolve({ message: `[mopub.service :: _requestReportData] requestApiUrl 정보 없음` });
				return;
			}

			// 리포트 데이터
			let data = new Array();

			// csv Stream 생성
			const csvStream = csvParse({ columns:true });

			/*
				dt : { 
					Day: '2019-11-28', 'Adunit ID': 'xxxx', Country: 'KOR', OS: 'Android', 'Adgroup Type': 'Marketplace', 'Adserver Impressions': '4', 'Adserver Clicks': '0', 'Adserver Revenue': '0.00034680001', 

					'SDK Version', 'Adgroup Network Type', 'App Name', 'App ID', 'Adunit Name', 'Adunit Format', 'Device Model', 'Adgroup Priority', 'Order Name', 'Order ID', 'Adgroup Name', 'Adgroup ID', 'Creative Name', 'Creative ID', 'Adserver Requests', 'Adserver Attempts', CTR, 'Fill Rate (Inventory)', 'Fill Rate (Demand)', 'Show Rate (Inventory)', 'Show Rate (Demand)', Fills 
				}
			*/
			const onData = dt => {
				// imp, clk, net_revenue 가 모두 0인 건들은 걸러낸다.
				if (_.isEqual(dt[imp], '0') && _.isEqual(dt[clk], '0') && _.isEqual(dt[net_revenue], '0.0')) {
					return;
				}

				// if(_.isNil(dt[net_revenue])) {
				// 	logger.debug(NON_RK_LOGGER, `[mopub.service :: _requestReportData] dt = ${JSON.stringify(dt, null, 2)}`);
				// 	resolve({ message: `[mopub.service :: _requestReportData] dt= ${JSON.stringify(dt, null, 2)} `, err: dt });
				// 	return;
				// }

				let raw = {};
				raw[ymd] = dt[ymd];
				raw[AD_UNIT_ID] = dt[AD_UNIT_ID];

				raw[os] = dt[os];

				// GFP에서 지원하는 타겟 국가인 경우, 국가 코드로 변환
				raw[country] = (!_.isNil(countryMappings[dt[country]])) ? countryMappings[dt[country]] : dt[country];

				// Inventory report id 로 리포트 데이터를 가져온 경우, adSource(Line Item Type) 정보가 없음
				raw[adSource] = dt[adSource] || '';

				raw[imp] = dt[imp];
				raw[clk] = dt[clk];
				raw[net_revenue] = dt[net_revenue];

				data.push(raw);
			};

			csvStream.on('data', onData);

			csvStream.on('end', () => {
				logger.debug(NON_RK_LOGGER, `[mopub.service :: _requestReportData] 리포트 데이터 읽기 완료 ( date= ${date} )`);

				// 데이터 읽기 완료 되면 종료
				resolve(data);
			});

			csvStream.on('error', err => {
				// [ERROR] csvStream 에러 처리
				resolve({ message: `[mopub.service :: _requestReportData] csvStream 에러`, err });
			});


			// 리포트 데이터 요청 결과
			// 	- 성공 : { size:0, timeout:0 } 
			// 	- 실패 : { message, err }
			let res = await reportApiService.requestApi({ url: requestApiUrl, raw: true, agent });

			// Status가 422 이고 응답 결과에 INVALID가 포함 되어 있지 않은 경우, 
			// Data is not available 이므로 로깅만하고 별도 에러처리 없이 빈 배열을 반환한다. (리포트 데이터가 없다는 의미임)
			if(_.isEqual(res.status, 422) && !_.includes(_.upperCase(res.err), 'INVALID')) {
				// 1. Data not available before {START_DATE}.
				// 2. Data is not available for that date yet. Data is available after 2PM noon UTC on the following day.
				// 3. Data is not available for that date.
				logger.debug(NON_RK_LOGGER, `[mopub.service :: _requestApi] Status ${res.status} ::: ${res.err}`);

				resolve(data);
				return;
			}

			// [ERROR] 응답 결과에 err가 있는 경우, 에러 처리
			if (!_.isNil(res.err)) {
				resolve({ message: `[mopub.service :: _requestApi] API 요청 실패 ::: statusCode(${res.status}), url(${requestApiUrl})`, err: res.err });
				return;
			}

			res.body.on('end', () => {
				// logger.debug(NON_RK_LOGGER, `[mopub.service :: _requestReportData] 리포트 데이터 요청 종료`);
			});

			res.body.on('error', err => {
				// [ERROR] 리포트 데이터 요청 에러 처리
				resolve({ message: `[mopub.service :: _requestReportData] 리포트 데이터 요청 에러 ( date= ${date} )`, err });
			});

			// 파일 사이즈가 너무 큰 경우, 모든 데이터를 제대로 로드하지 못하는 경우가 있다. 
			// 모펍의 경우, x-ton-expected-size 응답 헤더에 예상 파일 사이즈 정보를 내려준다.
			// 로드된 파일 사이즈가 예상 파일 사이즈보다 작은 경우, 로드가 덜 된 상황이므로 재요청 로직을 적용한다.
			if(!_.isNil(res.headers.get('x-ton-expected-size'))) {
				const expectedSize = parseInt(res.headers.get('x-ton-expected-size'));
				let loadedSize = 0;

				// highWaterMark default: 16 * 1024 (16KB)
				const patchedStream = new stream.PassThrough({ highWaterMark: 192 * 1024 });
				patchedStream.on('data', chunk => {
					loadedSize += (Buffer.from(chunk).byteLength);
				});
				patchedStream.on('end', () => {
					if(!_.isNil(expectedSize) && loadedSize < expectedSize) {
						logger.debug(NON_RK_LOGGER, `[mopub.service :: _requestReportData] 로드된 파일 사이즈(${loadedSize}) / 예상 파일 사이즈(${expectedSize}) ( date= ${date} )`);

						patchedStream.unpipe(csvStream);
						patchedStream.destroy();
						csvStream.destroy();

						// [ERROR] 예상 파일 사이즈 미달로 인한 에러 처리
						resolve({ message: `[mopub.service :: _requestReportData] 로드된 파일 사이즈(${loadedSize}) / 예상 파일 사이즈(${expectedSize}) ( date= ${date} )`, err: { date, loadedSize, expectedSize }, retry: true });
					}
				});

				res.body.pipe(patchedStream).pipe(csvStream);
			} else {
				// 혹시 x-ton-expected-size 정보가 응답 헤더가 없는 경우를 대비함
				res.body.pipe(csvStream);
			}
		} catch(err) {
			resolve({ message: `[mopub.service :: _requestReportData] Error ${JSON.stringify(err, null, 2)}`, err });
			return;
		}
	});
};


/**
 * SUBTASK 4. _requestReport : agent 에서 사용 중인 socket destroy 처리
 *
 * @param {Object} agent
 */
const destroyAgent = agent => {
	// socket destroy를 해서 자원 낭비를 막는다.
	if (!_.isNil(agent)) {
		agent.destroy();
		agent = null;
	}
};