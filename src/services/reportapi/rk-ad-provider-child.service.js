'use strict';


import fs from 'fs';
import _ from 'lodash';
import moment from 'moment';
import csv from 'csv-parser';
import mongoose from 'mongoose';
import Decimal from 'decimal.js';
import { Readable } from 'stream';

import config from '../../config/config';
import cLogger from '../../common/logger';
import mongooseForChild from '../../common/mongooseForChildProcess';

import * as logger from '../../utils/logger.util';

import { BusinessError } from '../../common/error';

import { AdProviderRkStat } from '../../models/ad-provider-rk-stats.schema';

const ObjectId = mongoose.Types.ObjectId;
const Decimal128 = mongoose.Types.Decimal128;

const RK_LOGGER = 'report_api_rk';

// RK ( 6개 항목 )
// - 0: adUnitId, 1: place_id, 2: place<PERSON><PERSON>, 3: rsKeyValue, 4: abt, 5: version
// - adUnitId, adProviderPlace_id, version 을 제외한 나머지 값은 모두 인코딩 처리 되어있으므로 디코딩 처리 필요함
const RK = { AD_UNIT_ID: 0, PLACE_ID: 1, PLACE_KEY: 2, RS_KEY_VALUE: 3, ABT: 4, VERSION: 5 };
const RK_COUNT = Object.keys(RK).length;


process.on('message', async ({ reportProcessingResult_id, chunkInfo, localFilePath, exchangeRate, adProviderInfos, gfpFeeRate, separator, fields }) => {
	let database = null;

	try{
		// 로거 설정
		cLogger(config);


		// DB 연결 시작
		database = await mongooseForChild(config).catch(err => {
			throw new BusinessError({ message: `[Rk Child Process] DB 연결 에러` }, { err, detail: JSON.stringify(err, null, 2) });
		});
		logger.debug(RK_LOGGER, `[Rk Child Process] chunkNumber #${chunkInfo.chunkNumber} 디비 연결중 ..`);


		// ChunkInfo 처리하기
		const { dataCount, filteredCount } = await _processChunkInfo({ reportProcessingResult_id, chunkInfo, localFilePath, exchangeRate, adProviderInfos, gfpFeeRate, separator, fields });


		// DB 연결 종료
		logger.debug(RK_LOGGER, `[Rk Child Process] chunkNumber #${chunkInfo.chunkNumber} 디비 종료중 ..`);
		await database.db.close();


		// 부모 프로세스에 처리 결과 전달
		process.send({ state: 'COMPLETE', dataCount, filteredCount });
	} catch (err) {
		logger.error(`[Rk Child Process] Error :: \n`, err);

		if (!_.isNil(database)) await database.db.close();

		process.send({ state: 'FAILED', err });
	}
});


/**
 * _processChunkInfo : ChunkInfo 처리하기
 * 
 * @param {Object} metaInfo { reportProcessingResult_id, chunkInfo, localFilePath, exchangeRate, adProviderInfos, gfpFeeRate, separator, fields }
 * @return {Object} { dataCount, filteredCount }
 */
const _processChunkInfo = async ({ reportProcessingResult_id, chunkInfo, localFilePath, exchangeRate, adProviderInfos, gfpFeeRate, separator, fields }) => {
	logger.debug(RK_LOGGER, `[rk-ad-provider-child.service :: _processChunkInfo][reportProcessingResult=${reportProcessingResult_id}] localFilePath= ${localFilePath}, chunkNumber= ${chunkInfo.chunkNumber} 처리 시작`);

	// [PROCESS 1] 리포트 파일에서 chunkData 추출, 가공 및 DB 저장
	const { dataCount, filteredCount } = await _processChunkData({ reportProcessingResult_id, localFilePath, exchangeRate, adProviderInfos, gfpFeeRate, separator, fields }, chunkInfo);

	return { dataCount, filteredCount };
};


/**
 * [Promise]
 * [PROCESS 1] _processChunkData : 리포트 파일에서 chunkData 추출, 가공 및 DB 저장
 * 	- 원본 데이터 : ymd, rk, country, os, imp, clk, netRevenue, revenue, serviceAmount
 * 	- 가공 데이터 :
 * 		rk => adUnitId, adProviderPlace_id, adProviderPlaceKey, rsKeyValue, abt
 * 		adProviderPlace_id => adProvider_id, publisher_id
 * 		netRevenue => netRevenueUSD/netRevenueKRW/gfpNetRevenueUSD/gfpNetRevenueKRW/usdSales/krwSales
 * 		revenue => revenueUSD/revenueKRW
 * 		serviceAmount => serviceAmountUSD/serviceAmountKRW
 *
 * @param {Object} metaInfo
 * {
		reportProcessingResult_id,
		localFilePath,
		exchangeRate : { from: 'KRW', to: 'USD', rate: '0.0008837155955739682' },
		adProviderInfos: [{ adProvider_id, publisher_id, adProviderInfo_id, place_ids }],
		gfpFeeRate,
		separator,
		fields: { ymd, rk, country, os, imp, clk, netRevenue, revenue, serviceAmount },
 * }
 * @param {Object} chunkInfo { chunkNumber, startLine, endLine }
 * @return {Object} { dataCount, filteredCount }
 */
const _processChunkData = ({ reportProcessingResult_id, localFilePath, exchangeRate, adProviderInfos, gfpFeeRate, separator, fields }, { chunkNumber, startLine, endLine }) => {
	logger.debug(RK_LOGGER, `[rk-ad-provider-child.service :: _processChunkData][reportProcessingResult=${reportProcessingResult_id}] chunkNumber #${chunkNumber} 처리 시작`);

	return new Promise((resolve, reject) => {
		// 케이스별 필터된 건수
		const filteredCount = {
			notExistRk : 0, // rk 가 없는 경우
			invalidEncodedRk : 0, // rk 가 인코딩 되지 않았거나, 잘못 인코딩된 경우
			invalidEncodedRsKeyValue : 0, // rsKeyValue 가 인코딩 되지 않은 경우
			notExistRequired : 0, // rk 에 adUnitId, adProviderPlace_id 가 없는 경우
			notExistMeta : 0, // adProviderPlace_id 에 해당하는 adProvider_id, publisher_id 가 없는 경우
		};

		// AP 원본 로컬 파일 읽기 스트림
		const fileStream = fs.createReadStream(localFilePath);

		/* DB 저장 */
		const today = new Date();
		const BULK_SIZE = config.report_api_bulk_size || 20000;

		const dbStream = new Readable({ objectMode: true });
		dbStream._read = () => {};

		let dataCount = 0;
		let bulk = AdProviderRkStat.collection.initializeOrderedBulkOp();

		dbStream.on('data', async data => {
			try {
				dataCount++;

				data.createdAt = today;
				bulk.insert(data);

				if (dataCount % BULK_SIZE === 0) {
					dbStream.pause();

					await bulk.execute();
					bulk = AdProviderRkStat.collection.initializeOrderedBulkOp();
					dbStream.resume();
				}
			} catch (err) {
				reject({ message: `[rk-ad-provider-child.service :: _processChunkData][reportProcessingResult=${reportProcessingResult_id}] chunkNumber #${chunkNumber} dbStream data 에러`, err });

				closeStreams();
			}
		}).on('end', async () => {
			try {
				if (dataCount % BULK_SIZE !== 0) {
					await bulk.execute();
				}

				resolve({ dataCount, filteredCount });

				dbStream.destroy();

				logger.debug(RK_LOGGER, `[rk-ad-provider-child.service :: _processChunkData][reportProcessingResult=${reportProcessingResult_id}] chunkNumber #${chunkNumber} dbStream end 완료`);
			} catch (err) {
				reject({ message: `[rk-ad-provider-child.service :: _processChunkData][reportProcessingResult=${reportProcessingResult_id}] chunkNumber #${chunkNumber} dbStream end 에러`, err });

				closeStreams();
			}
		}).on('error', err => {
			reject({ message: `[rk-ad-provider-child.service :: _processChunkData][reportProcessingResult=${reportProcessingResult_id}] chunkNumber #${chunkNumber} dbStream 에러`, err });

			closeStreams();
		});


		/* csv 읽기 */
		const csvStream = csv({ separator });
		let lineCount = 0; // 라인 카운팅 용도

		const onData = (data) => {
			try {
				lineCount++;

				if (startLine <= lineCount && lineCount <= endLine) {
					// chunk 만들기
					const chunk = _makeChunk({ exchangeRate, adProviderInfos, gfpFeeRate, fields }, data, filteredCount);

					// chunk가 없다면, 데이터 처리하지 않는다.
					if (_.isNil(chunk)) {
						return;
					}

					// dbStream에 chunk 전달
					dbStream.push(chunk);
				}

				if (endLine < lineCount) {
					csvStream.removeListener('data', onData);
					csvStream.end();
				}
			} catch(err) {
				reject({ message: `[rk-ad-provider-child.service :: _processChunkData][reportProcessingResult=${reportProcessingResult_id}] chunkNumber #${chunkNumber} csvStream data 에러`, err });

				closeStreams();
			}
		};

		csvStream.on('data', onData);

		csvStream.on('end', async () => {
			try {
				logger.debug(RK_LOGGER, `[rk-ad-provider-child.service :: _processChunkData][reportProcessingResult=${reportProcessingResult_id}] chunkNumber #${chunkNumber} csvStream end 완료`);

				dbStream.push(null);
				csvStream.removeListener('data', onData);

				if (fileStream && _.isFunction(fileStream.close)) fileStream.close();
			} catch (err) {
				reject({ message: `[rk-ad-provider-child.service :: _processChunkData][reportProcessingResult=${reportProcessingResult_id}] chunkNumber #${chunkNumber} csvStream end 에러`, err });

				closeStreams();
			}
		});

		csvStream.on('error', err => {
			reject({ message: `[rk-ad-provider-child.service :: _processChunkData][reportProcessingResult=${reportProcessingResult_id}] chunkNumber #${chunkNumber} csvStream 에러`, err });

			closeStreams();
		});

		fileStream.on('end', () => {
			logger.debug(RK_LOGGER, `[rk-ad-provider-child.service :: _processChunkData][reportProcessingResult=${reportProcessingResult_id}] chunkNumber #${chunkNumber} fileStream end 완료`);
		});

		fileStream.on('error', err => {
			reject({ message: `[rk-ad-provider-child.service :: _processChunkData][reportProcessingResult=${reportProcessingResult_id}] chunkNumber #${chunkNumber} fileStream 에러`, err });

			closeStreams();
		});

		fileStream.pipe(csvStream);

		// 모든 stream 종료 처리
		const closeStreams = () => {
			logger.debug(RK_LOGGER, `[rk-ad-provider-child.service :: closeStreams]`);

			if (dbStream) dbStream.destroy();

			if (csvStream) {
				csvStream.removeListener('data', onData);
				csvStream.end();
			}

			if (fileStream && _.isFunction(fileStream.close)) fileStream.close();
		};
	});
};


/**
 * _makeChunk : Chunk 가공하기
 * 	_exportChunkDataFromLocal, _exportChunkDataFromHdfs
 * 
 * @param {Object} metaInfo
 * { 
		exchangeRate : { from: 'KRW', to: 'USD', rate: 0.0008837155955739682 },
		adProviderInfos: [{ adProvider_id, publisher_id, adProviderInfo_id, place_ids }],
		gfpFeeRate,
		fields: { ymd, rk, country, os, imp, clk, netRevenue, revenue, serviceAmount },
 * }
 * @param {Object} data
 * {
		ymd: '20181117',
		adProvider_id: 5bebc62b77bd856e48ae2111,
		publisher_id: 5bcd6979868338bd69d9df9f,
		adProviderPlaceKey: 'adUnitId:AOS_VIDEO_AD', // 디코딩 처리됨
		adProviderPlace_id: '5bebe3041f2ff071a14e0227',
		adUnitId: 'gfd_adunit_web1',
		rsKeyValue: { key1: '\'key1_value22\'', key2: '(key2_value221)' },
		abt: '',
		country: 'Korea',
		os: 'Android',
		imp: 916,
		clk: 31,
		gfpNetRevenueKRW: 61.990267,
		gfpNetRevenueUSD: 0.0547,
		netRevenueKRW: 61.990267,
		netRevenueUSD: 0.0547,
		revenueKRW: 61.990267,
		revenueUSD: 0.0547,
		serviceAmountKRW: 61.990267, // NDP only
		serviceAmountUSD: 0.0547, // NDP only
		krwSales: 61.990267,
		usdSales: 0.0547,
 * }
 * @param {Object} filteredCount { notExistRk, invalidEncodedRk, invalidEncodedRsKeyValue, notExistRequired, notExistMeta }
*/
const _makeChunk = ({ exchangeRate, adProviderInfos, gfpFeeRate, fields: { ymd, rk, country, os, imp, clk, netRevenue, revenue, serviceAmount } }, data, filteredCount) => {
	// RK ( https://jira.navercorp.com/browse/GFP-1166 )
	// - 0: adUnitId, 1: place_id, 2: placeKey, 3: rsKeyValue, 4: abt, 5: version
	// - adUnitId, adProviderPlace_id, version 을 제외한 나머지 값은 모두 인코딩 처리 되어있으므로 디코딩 처리 필요함
	// - Sample = 'gfd_adunit_web1@5bebe3041f2ff071a14e0227@adUnitId%3aAOS_VIDEO_AD@key1:%27key1_value1%27,key2:%28key2_value11%29@abt@v2'

	// rk 가 없는 경우, 건수 로깅 및 필터 처리
	if (_.isEmpty(data[rk])) {
		filteredCount.notExistRk++;

		return;
	}

	const splitRk = data[rk].split('@');

	const rkVersion = splitRk[RK.VERSION];
	const isRkV2 = !_.isEmpty(rkVersion) && rkVersion === 'v2';
	const checkInvalidEncodedRk = isRkV2 ? _checkInvalidEncodedRkV2 : _checkInvalidEncodedRkV1;
	const checkRkCount = isRkV2 ? RK_COUNT : RK_COUNT - 1;
	const _parseRk = isRkV2 ? _parseRkV2 : _parseRkV1;

	// rk 인코딩 안 된 경우, 건수 로깅 및 필터 처리
	if (splitRk.length > checkRkCount || checkInvalidEncodedRk(data[rk])) {
		filteredCount.invalidEncodedRk++;

		return;
	}

	// rsKeyValue 인코딩 안 된 경우, 건수 로깅 및 필터 처리
	if (isRkV2 && !_.isEmpty(splitRk[RK.RS_KEY_VALUE]) && _checkInvalidEncodedRsKeyValue(splitRk[RK.RS_KEY_VALUE])) {
		filteredCount.invalidEncodedRsKeyValue++;

		return;
	}


	// rk 파싱하기
	let { adUnitId, adProviderPlace_id, adProviderPlaceKey, rsKeyValue, abt } = _parseRk(splitRk);

	// rk 에 adUnitId, adProviderPlace_id 가 없는 경우, 건수 로깅 및 필터 처리 (adProviderPlaceKey, rsKeyValue, abt 는 없을 수도 있음)
	if (_.isEmpty(adUnitId) || _.isEmpty(adProviderPlace_id)) {
		filteredCount.notExistRequired++;

		return;
	}


	// adProviderPlace_id가 등록 되어 있는 adProvider_id, publisher_id 찾기
	let adProvider_id = null;
	let publisher_id = null;
	adProviderInfos.forEach(info => {
		let place_id = info.place_ids.filter(place_id => place_id === adProviderPlace_id);

		if (place_id.length > 0) {
			adProvider_id = info.adProvider_id;
			publisher_id = info.publisher_id;
		}
	});

	// adProviderPlace_id 가 속해 있는 adProvider_id, publisher_id 가 없는 경우, 건수 로깅 및 필터 처리
	if (_.isNil(adProvider_id) || _.isNil(publisher_id)) {
		filteredCount.notExistMeta++;

		return;
	}


	// 데이터 가공 결과
	let chunk = {
		ymd : moment(data[ymd]).format('YYYYMMDD'),

		adProvider_id: ObjectId(adProvider_id),
		publisher_id: ObjectId(publisher_id),

		/* rk 파싱 */
		adProviderPlaceKey,
		adProviderPlace_id: ObjectId(adProviderPlace_id),
		adUnitId,
		rsKeyValue,
		abt,

		country : data[country],
		os : data[os],

		imp : parseInt(data[imp]),
		clk : parseInt(data[clk]),
	};


	/* GFP 수수료 조회 */
	const fr = _.filter(gfpFeeRate, fr => _.isEqual(fr.date, chunk.ymd) && _.isEqual(fr.adProvider_id, adProvider_id) && _.isEqual(fr.publisher_id, publisher_id)).pop();

	if (_.isNil(fr)) {
		throw new BusinessError({ message: `[rk-ad-provider-child.service :: _makeChunk] GFP 수수료 정보 없음 (date=${chunk.ymd}, adProvider_id=${adProvider_id}, publisher_id=${publisher_id})` });
	}

	const feeRate = new Decimal(1).minus(fr.feeRate); // 1 - feeRate


	/* 환율 적용 및 GFP 수수료율 제외 */
	// netRevenueUSD / netRevenueKRW
	chunk['netRevenue' + exchangeRate.from] = Decimal128.fromString(data[netRevenue]);
	chunk['netRevenue' + exchangeRate.to] = Decimal128.fromString(new Decimal(chunk['netRevenue' + exchangeRate.from].toString()).mul(exchangeRate.rate).toFixed());

	// gfpNetRevenueUSD / gfpNetRevenueKRW (GFP 수수료 제외)
	chunk['gfpNetRevenueUSD'] = Decimal128.fromString(new Decimal(chunk['netRevenueUSD'].toString()).mul(feeRate).toFixed());
	chunk['gfpNetRevenueKRW'] = Decimal128.fromString(new Decimal(chunk['netRevenueKRW'].toString()).mul(feeRate).toFixed());

	// revenueUSD / revenueKRW
	if (!_.isNil(data[revenue])) {
		chunk['revenue' + exchangeRate.from] = Decimal128.fromString(data[revenue]);
		chunk['revenue' + exchangeRate.to] = Decimal128.fromString(new Decimal(chunk['revenue' + exchangeRate.from].toString()).mul(exchangeRate.rate).toFixed());
	}

	// serviceAmountUSD / serviceAmountKRW
	if (!_.isNil(data[serviceAmount])) {
		chunk['serviceAmount' + exchangeRate.from] = Decimal128.fromString(data[serviceAmount]);
		chunk['serviceAmount' + exchangeRate.to] = Decimal128.fromString(new Decimal(chunk['serviceAmount' + exchangeRate.from].toString()).mul(exchangeRate.rate).toFixed());
	}

	/*
		BigDecimal.ROUND_UP : 올림
		BigDecimal.ROUND_DOWN : 내림
		BigDecimal.ROUND_HALF_UP : 반올림 (5<=x<9 ↑)
 		BigDecimal.ROUND_HALF_DOWN : 반내림 (5<x<9 ↑, 5가 포함 되지 않음)
	 */

	// usdSales / krwSales (소수점 6째 자리 = 7째 자리에서 반올림)
	chunk['usdSales'] = Decimal128.fromString(new Decimal(chunk['netRevenueUSD'].toString()).toFixed(6, Decimal.ROUND_HALF_UP));
	chunk['krwSales'] = Decimal128.fromString(new Decimal(chunk['netRevenueKRW'].toString()).toFixed(6, Decimal.ROUND_HALF_UP));


	return chunk;
};


// [FADEOUT!!!!] _checkInvalidEncodedRkV1 : rk 인코딩 안 된 경우, 필터 처리 (application/x-www-form-urlencoded)
const _checkInvalidEncodedRkV1 = rk => {
	// 인코딩 여부 : 영어 숫자 % _ - . ~ @ : , () ! * ' ; 외의 문자를 포함 하고 있는지
	const validEncoded = /^[a-zA-Z0-9%_\-\.~@\:,()!*';]+$/;

	return validEncoded.test(rk) === false;
};

/**
 * _checkInvalidEncodedRkV2 : rk 인코딩 안 된 경우, 필터 처리 (RFC-3986)
 *  - 허용한 문자 외의 값을 포함하거나 인코딩 값이 정상이 아닌 경우
 *  - 서버에서 adUnitId, adProviderPlace_id, version 을 제외한 나머지 항목은 모두 인코딩 처리
 *
 * @param rk
 * @return {boolean}
 */
const _checkInvalidEncodedRkV2 = rk => {
	// 인코딩 여부 : 영어 숫자 % _ - . ~ @ 외의 문자를 포함 하고 있는지
	const validEncoded = /^[a-zA-Z0-9%_\-\.~@]+$/;

	return validEncoded.test(rk) === false || _isValidEncodedRk(rk) === false;
};

/**
 * _isValidEncodedRk : rk 인코딩 값이 유효한지 체크
 *  _parseRk 에서 decodeURIComponent 사용 시, 에러가 발생 할 수 있으므로 사전 체크하는 용도
 *
 * @param rk
 * @return {boolean}
 */
const _isValidEncodedRk = rk => {
	try {
		decodeURIComponent(rk);
		return true;
	} catch (e) {
		return false;
	}
}

/**
 * _checkInvalidEncodedRsKeyValue : rsKeyValue 인코딩 안 된 경우, 필터 처리 (application/x-www-form-urlencoded)
 *
 * @param rsKeyValue
 * @return {boolean}
 */
const _checkInvalidEncodedRsKeyValue = rsKeyValue => {
	// 인코딩 여부 : 영어 숫자 % _ - . ~ : , () ! * ' 외의 문자를 포함 하고 있는지
	const validEncoded = /^[a-zA-Z0-9%_\-\.~\:,()!*']+$/;

	return validEncoded.test(decodeURIComponent(rsKeyValue)) === false;
};


/**
 * _parseRkV2 : rk 파싱하기
 *  - adUnitId, adProviderPlace_id, version 을 제외한 나머지 값은 모두 인코딩 처리 되어있으므로 디코딩 처리 필요함
 *
 * @params {Array} splitRk
 *          ['adUnitId', 'adProviderPlace_id', 'encodedAdProviderPlaceKey', 'encodedRsKeyValue', 'encodedAbt', 'version' ]
 * @return {Object} rkInfo { adUnitId, adProviderPlace_id, adProviderPlaceKey, rsKeyValue, abt }
 */
const _parseRkV2 = splitRk => {
	// 0: adUnitId, 1: place_id, 2: placeKey, 3: rsKeyValue, 4: abt, 5: version

	let rkInfo = {
		adUnitId: splitRk[RK.AD_UNIT_ID] || '',
		adProviderPlace_id: splitRk[RK.PLACE_ID] || '',
		adProviderPlaceKey: '',
		rsKeyValue: {},
		abt: '',
	};

	// placeKey 디코딩
	if (!_.isEmpty(splitRk[RK.PLACE_KEY])) {
		rkInfo.adProviderPlaceKey = decodeURIComponent(splitRk[RK.PLACE_KEY]);
	}

	// rsKeyValue 디코딩 & 파싱
	if (!_.isEmpty(splitRk[RK.RS_KEY_VALUE])) {
		const splitRsKeyValue = decodeURIComponent(splitRk[RK.RS_KEY_VALUE]).split(',');

		splitRsKeyValue.forEach(keyValue => {
			const splitKeyValue = keyValue.split(':');

			// 키가 있는 경우
			if (!_.isNil(splitKeyValue[0]) && !_.isEmpty(splitKeyValue[0])) {
				const key = splitKeyValue[0];
				const value = splitKeyValue[1];

				rkInfo.rsKeyValue[key] = value;
			}
		});
	}

	// abt 디코딩
	if (!_.isEmpty(splitRk[RK.ABT])) {
		rkInfo.abt = decodeURIComponent(splitRk[RK.ABT]);
	}

	return rkInfo;
};

// [FADEOUT!!!!]
const _parseRkV1 = splitRk => {
	// 0: adUnitId, 1: adProviderPlace_id, 2: adProviderPlaceKey, 3: rsKeyValue, 4: abt

	let rsKeyValue = {};

	// rsKeyValue 파싱
	if (!_.isNil(splitRk[3]) && !_.isEmpty(splitRk[3])) {
		const splitRsKeyValue = splitRk[3].split(',');

		splitRsKeyValue.forEach(keyValue => {
			const splitKeyValue = keyValue.split(':');

			// 키가 있는 경우
			if (!_.isNil(splitKeyValue[0]) && !_.isEmpty(splitKeyValue[0])) {
				const key = splitKeyValue[0];
				const value = splitKeyValue[1];

				rsKeyValue[key] = value;
			}
		});
	}

	let adProviderPlaceKey = '';
	try {
		if (!_.isNil(splitRk[2])) {
			adProviderPlaceKey = decodeURIComponent(splitRk[2]);
		}
	} catch (ex) {
	} finally {
		let rkInfo = {
			adUnitId: splitRk[0] || '',
			adProviderPlace_id: splitRk[1] || '',
			adProviderPlaceKey,
			rsKeyValue,
			abt: splitRk[4] || '',
		};

		return rkInfo;
	}
};
