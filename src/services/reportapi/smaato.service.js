'use strict';

import _ from 'lodash';
import moment from 'moment';
import FormData from 'form-data';

import { BusinessError } from '../../common/error';

import * as logger from '../../utils/logger.util';

import * as reportApiService from '../reportapi/report-api.service';

const NON_RK_LOGGER = 'report_api_non_rk';

// 재요청 횟수
const RETRY_MAX_COUNT = 10;


/**
 * [PROCESS 3:: TASK 2] processReportApi : SMAATO 리포트 API 연동 및 리포트 파일 다운로드
 *  SUBTASK 1. initCsvReport : csv 파일 초기화
 *  SUBTASK 2. _getAccessToken : 토큰 생성하기
 *  SUBTASK 3. 리포트 데이터 생성 요청 및 다운로드
 * 		3-1. _requestReport : 리포트 데이터 생성 요청하기
 * 		3-2. _getDataList : 리포트 데이터 가공하기
 * 		3-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
 * 
 * @RequestMapping(value='/batch/day/outside/smaato')
 * 
 * @param {Object} adProviderMetaInfo 
 * { 
		reportInfos: { CLIENT_ID, CLIENT_SECRET },
		reportInfoExt: { apiResultPath, fields, dimensions, kpis, token_request_api, report_request_api },
		period: { startDate, endDate },
		fda
 * }
 */
module.exports.processReportApi = async ({ reportInfos: { CLIENT_ID, CLIENT_SECRET }, reportInfoExt: { apiResultPath, fields, dimensions, kpis, token_request_api, report_request_api }, period: { startDate, endDate }, fda }) => {
	logger.debug(NON_RK_LOGGER, '[smaato.service :: processReportApi] 호출됨');


	/* SUBTASK 1. initCsvReport : csv 파일 초기화 */
	// 필드 순서 : ymd, adspaceid, country, imp, clk, net_revenue
	await reportApiService.initCsvReport({ fda, fields });


	/* SUBTASK 2. _getAccessToken : 토큰 생성하기 */
	const accessToken = await _getAccessToken({ CLIENT_ID, CLIENT_SECRET, token_request_api });


	/*
		SUBTASK 3. 리포트 데이터 생성 요청 및 다운로드
	*/

	// SUBTASK 3-1. _requestReport : 리포트 데이터 생성 요청하기
	const rawDataList = await _requestReport({ report_request_api, dimensions, kpis, period: { startDate, endDate }, accessToken });

	// SUBTASK 3-2. _getDataList : 리포트 데이터 가공하기
	const dataList = await _getDataList({ fields, rawDataList });

	// SUBTASK 3-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
	await reportApiService.writeCsvReport({ fda, apiResultPath, fields, dataList });
};


/**
 * SUBTASK 2. _getAccessToken : 토큰 요청하기
 *
 * @param {Object} { CLIENT_ID, CLIENT_SECRET, token_request_api }
 * @return {String} accessToken
 */
const _getAccessToken = async ({ CLIENT_ID, CLIENT_SECRET, token_request_api }) => {
	logger.debug(NON_RK_LOGGER, `[smaato.service :: _getAccessToken] 토큰 요청하기`);

	// formData 설정
	// smaato에서 accessToken 요청 시 json 방식을 지원하지 않아, form data 방식을 사용함
	const form = new FormData();
	form.append('client_id', CLIENT_ID);
	form.append('client_secret', CLIENT_SECRET);
	form.append('grant_type', 'client_credentials');

	// res : { access_token: "xxxxxxx", expires_in: 36000, token_type: "Bearer", scope: "read" }
	const res = await reportApiService.requestApi({ url: token_request_api, method: 'POST', body: form });

	// [ERROR] 응답 결과에 에러가 있는 경우, 에러 처리
	if (!_.isNil(res) && !_.isNil(res.err)) {
		throw new BusinessError({ message: `[smaato.service :: _getAccessToken] 토큰 요청 에러`}, { err: res, detail: JSON.stringify(res, null, 0) });
	}

	const accessToken = res.access_token;

	// [ERROR] accessToken가 없는 경우, 에러 처리
	if (_.isNil(accessToken) || _.isEmpty(accessToken)) {
		throw new BusinessError({ message: `[smaato.service :: _getAccessToken] 토큰 정보 없음` });
	}

	logger.debug(NON_RK_LOGGER, `[smaato.service :: _getAccessToken] 토큰 요청 완료 ( accessToken= ${accessToken} )`);

	return accessToken;
};


/**
 * SUBTASK 3-1. _requestReport : 리포트 데이터 생성 요청하기
 *
 * @param {Object} { report_request_api, dimensions, kpis, period: { startDate, endDate }, accessToken }
 * @return {Array} rawDataList
 */
const _requestReport = async ({ report_request_api, dimensions, kpis, period: { startDate, endDate }, accessToken }) => {
	logger.debug(NON_RK_LOGGER, `[smaato.service :: _requestReport] 리포트 데이터 생성 요청하기`);

	const headers = { 'Content-Type': 'application/json', 'Authorization': `Bearer ${accessToken}` };

	let criteria = null;
	_.map(_.reverse(dimensions), d => {
		const child = {
			dimension: d,
			child: criteria,
		};

		criteria = child;
	});

	let kpi = {};
	_.map(kpis, k => {
		kpi[k] = true;
	});

	// time-range : startDate <= REPORT <= endDate
	const body = {
		criteria, kpi,
		period: {
			period_type: 'fixed',
			start_date: moment(startDate).format('YYYY-MM-DD'),
			end_date: moment(endDate).format('YYYY-MM-DD'),
		}
	};


	// res : [{ criteria: [{ meta: {}, name: 'Date', value: [2020, 11, 3] }, { meta: {}, name: 'AdspaceId', value: 131180346 }, { meta: {}, name: 'CountryCode', value: 'KR' }], kpi: { clicks: 0, incomingAdRequests: 3, netRevenue: 0.0 } }]
	let reqCount = 1;
	let res = await reportApiService.requestApi({ url: report_request_api, method: 'POST', headers, body });

	// 재요청
	// - res.status = 502 Bad Gateway 인 경우
	// - res.status = 500 Some went wrong 인 경우
	while (!_.isNil(res) && !_.isNil(res.err) && _.includes([502, 500], res.status)) {
		logger.debug(NON_RK_LOGGER, `[smaato.service :: _requestReport] smaato 재요청 ( reqCount : ${reqCount} )`);

		// 1분 sleep
		await reportApiService.sleep(60000);

		res = await reportApiService.requestApi({ url: report_request_api, method: 'POST', headers, body });
		reqCount++;

		// error 가 계속 발생하는 경우, 에러 처리
		// 최대 재요청 횟수는 10번
		if (RETRY_MAX_COUNT < reqCount) {
			throw new BusinessError({ message: `[smaato.service :: _requestReport] smaato 재요청 실패 ::: ${JSON.stringify(res, null, 2)}` }, { err: res, detail: JSON.stringify(res, null, 2)});
		}
	}

	// [ERROR] 응답 결과에 err가 있는 경우, 에러 처리
	if (!_.isNil(res) && !_.isNil(res.err)) {
		throw new BusinessError({ message: `[smaato.service :: _requestReport] 리포트 데이터 생성 요청 실패 ::: ${JSON.stringify(res, null, 2)}` });
	}

	const rawDataList = res;

	logger.debug(NON_RK_LOGGER, `[smaato.service :: _requestReport] 리포트 데이터 생성 요청 완료 ( startDate= ${moment(startDate).format('YYYY-MM-DDTHH:mm:ssZ')}, endDt= ${moment(endDate).format('YYYY-MM-DDTHH:mm:ssZ')} )`);

	return rawDataList;
};


/**
 * SUBTASK 3-2. _getDataList : 리포트 데이터 가공하기
 *
 * @param {Object} { fields: { ymd, imp, clk, net_revenue }, rawDataList }
 * @return {Array} dataList
 */
const _getDataList = async ({ fields: { ymd, imp, clk, net_revenue }, rawDataList }) => {
	logger.debug(NON_RK_LOGGER, `[smaato.service :: _getDataList] 리포트 데이터 가공하기`);

	let dataList = new Array();

	rawDataList.map(({ criteria, kpi }) => {
		// imp, clk, net_revenue 가 모두 0인 건들은 걸러낸다.
		if (_.isEqual(kpi[imp], 0) && _.isEqual(kpi[clk], 0) && _.isEqual(kpi[net_revenue], 0.0)) {
			return;
		}

		let data = Object.assign({}, kpi);

		criteria.map(({ name, value }) => {
			if (_.isEqual(name, ymd)) {
				// 날짜 생성 시, 월은 1 빼줘야 원래 값이 나옴
				value[1] = value[1] - 1;
				data[name] = moment(value).format('YYYY-MM-DD');
			} else {
				data[name] = value;
			}
		});

		// 리포트 파일(csv) 생성 시, clk 은 optional 이라 0인 경우 공백 처리되므로, 스트링으로 변환해준다.
		data[clk] = data[clk] + '';

		dataList.push(data);
	});

	return dataList;
};
