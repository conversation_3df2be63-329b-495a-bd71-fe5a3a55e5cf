'use strict';

import _ from 'lodash';

import * as logger from '../utils/logger.util';

import SBE from '../common/error-code';
import { BusinessError } from '../common/error';

import * as dataService from './data.service';
import * as reportApiService from '../services/reportapi/report-api.service';


/**
 * sourcePath 스케쥴 설정 및 AP 원본 파일 HDFS 백업 (source=AP & OUTSIDE 대상)
 */
module.exports.backupSilvergreySource = async (ctx) => {
	logger.debug(`[data.controller :: backupSilvergreySource] /onetime/backupSilvergreySource 호출됨`);

	const startDate = ctx.request.query.startDate;
	const endDate = ctx.request.query.endDate;
	const reportApiType = ctx.request.query.reportApiType;

	try {
		if (_.isEmpty(startDate) || _.isEmpty(endDate)) {
			throw new BusinessError(SBE.Common.MissingRequiredParam, { field: 'startDate | endDate' });
		}

		await dataService.backupSilvergreySource(startDate, endDate, reportApiType);

		ctx.body = {
			code: 200,
			message: `실버그레이 AP 원본 파일 백업 성공 (${startDate} ~ ${endDate})`
		};
	} catch(e) {
		logger.error(`[data.controller :: backupSilvergreySource] Error :: \n ${e.stack}\n\n`, e);

		ctx.body = {
			code: 400,
			message: `실버그레이 AP 원본 파일 백업 실패 (${startDate} ~ ${endDate})`
		};
	}
};


/**
 * DATA/CMS DB - AP Summary 리포트 데이터 1차 검증 (원본 csv 와 재처리 csv 데이터 비교)
 */
module.exports.compareCsvFiles = async (ctx) => {
	const logging_prefix = '[data.controller :: compareCsvFiles]';

	logger.debug(`${logging_prefix} /onetime/compareCsvFiles 호출됨`);

	// CMS or DATA
	let targetDb = ctx.request.query.targetDb;

	// AP or PK
	let summaryType = ctx.request.query.summaryType;

	let yyyymm = ctx.request.query.yyyymm;

	if (_.isEmpty(targetDb) || _.isEmpty(summaryType) || _.isEmpty(yyyymm)) {
		throw new BusinessError(SBE.Common.MissingRequiredParam, {field: 'targetDb | summaryType | yyyymm'});
	}

	if (!_.includes(['CMS', 'DATA'], targetDb) || !_.includes(['AP', 'PK'], summaryType)) {
		throw new BusinessError({ message: `targetDb(CMS/DATA)= ${targetDb}, summaryType(AP/PK)= ${summaryType} 값이 유효하지 않음` });
	}

	try {
		logger.debug(`${logging_prefix} targetDb=${targetDb}, summaryType=${summaryType}, yyyymm=${yyyymm}`);

		await dataService.compareCsvFiles(targetDb, summaryType, yyyymm)

		ctx.body = {
			code: 200,
			message: `${targetDb} ${summaryType} - 데이터 비교 성공`
		};
	} catch(e) {
		logger.error(`[data.controller :: compareCsvFiles] Error :: \n ${e.stack}\n\n`, e);

		ctx.body = {
			code: 400,
			message: `${targetDb} ${summaryType} - 데이터 비교 실패`
		};
	}
};


/**
 * DATA/CMS DB - AP Summary 리포트 데이터 2차 검증 (재처리 csv GFP 수수료 검증)
 */
module.exports.validateGfpFeeReprocess = async (ctx) => {
	const logging_prefix = '[data.controller :: validateGfpFeeReprocess]';

	logger.debug(`${logging_prefix} /onetime/validateGfpFeeReprocess 호출됨`);

	// CMS or DATA
	let targetDb = ctx.request.query.targetDb;

	// AP or PK
	let summaryType = ctx.request.query.summaryType;

	let yyyymm = ctx.request.query.yyyymm;

	if (_.isEmpty(targetDb) || _.isEmpty(summaryType) || _.isEmpty(yyyymm)) {
		throw new BusinessError(SBE.Common.MissingRequiredParam, {field: 'targetDb | summaryType | yyyymm'});
	}

	if (!_.includes(['CMS', 'DATA'], targetDb) || !_.includes(['AP', 'PK'], summaryType)) {
		throw new BusinessError({ message: `targetDb(CMS/DATA)= ${targetDb}, summaryType(AP/PK)= ${summaryType} 값이 유효하지 않음` });
	}

	try {
		logger.debug(`${logging_prefix} targetDb=${targetDb}, summaryType=${summaryType}, yyyymm=${yyyymm}`);

		await dataService.validateGfpFeeReprocess(targetDb, summaryType, yyyymm)

		ctx.body = {
			code: 200,
			message: `${targetDb} ${summaryType} - 데이터 검증 성공`
		};
	} catch(e) {
		logger.error(`[data.controller :: validateGfpFeeReprocess] Error :: \n ${e.stack}\n\n`, e);

		ctx.body = {
			code: 400,
			message: `${targetDb} ${summaryType} - 데이터 검증 실패`
		};
	}
};



/**
 * HDFS/CMS DB - AP 원본 파일 재처리
 */
module.exports.reprocessSilvergrey = async (ctx) => {
	const logging_prefix = '[data.controller :: reprocessSilvergrey]';

	logger.debug(`${logging_prefix} /onetime/reprocessSilvergrey 호출됨`);

	const schedule_id = ctx.request.query.schedule_id;
	const type = ctx.request.query.type || null;

	try {
		// 리포트 연동 스케쥴 정보 조회
		const schedule = await reportApiService.getReportJobScheduleById(schedule_id);

		if (schedule.adProviderType !== 'OUTSIDE') {
			throw new BusinessError({ message: `adProviderType=OUTSIDE 만 재처리 가능함. (schedule_id= ${schedule_id})` });
		}

		if (schedule.source !== 'AP') {
			throw new BusinessError({ message: `source=AP 만 재처리 가능함. (schedule_id= ${schedule_id})` });
		}

		logger.debug(`${logging_prefix} schedule_id=${schedule_id}, schedule=${JSON.stringify(schedule, null, 2)}`);

		await dataService.reprocessSilvergrey(schedule, type);

		ctx.body = {
			code: 200,
			message: `${schedule_id} - 재처리 성공`
		};
	} catch(e) {
		logger.error(`[data.controller :: reprocessSilvergrey] Error :: \n ${e.stack}\n\n`, e);

		ctx.body = {
			code: 400,
			message: `${schedule_id} - 재처리 실패`
		};
	}
};

