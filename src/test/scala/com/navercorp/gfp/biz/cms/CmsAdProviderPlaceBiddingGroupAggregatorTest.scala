package com.navercorp.gfp.biz.cms

import com.navercorp.gfp.core.base.model.{ClientLogWithBidPrice, Response, ServerLog}
import com.navercorp.gfp.core.conf.Conf
import com.navercorp.gfp.helper.FunSuiteWithSparkSession
import com.navercorp.gfp.meta.adprovider.AdProviderDao
import com.navercorp.gfp.meta.publisher.PublisherDao
import com.typesafe.config.Config
import org.apache.log4j.{Level, Logger}
import org.junit.runner.RunWith
import org.scalatest.BeforeAndAfterAll
import org.scalatestplus.junit.JUnitRunner

@RunWith(classOf[JUnitRunner])
class CmsAdProviderPlaceBiddingGroupAggregatorTest extends FunSuiteWithSparkSession with BeforeAndAfterAll {
    val pubDao = new PublisherDao
    val apDao = new AdProviderDao()
    val conf:Config = Conf.getConf()

    override def beforeAll(): Unit = {
        Logger.getLogger("org.apache.spark").setLevel(Level.ERROR)
        Logger.getLogger("org.mongodb").setLevel(Level.ERROR)
    }

    test("check aggregate result") {
        implicit val spark = createSparkSession(this.getClass.getName)

        import spark.implicits._

        val serverDf = Seq(
            ServerLog(0, "req1", "pub1", "svc1", "au1", Seq(Response("5c29a7ba77bd856e4889d8db", "place1", "bg1", 0.1f, 1, "S2S"), Response("5d6773e077bd856e48f98c72", "place2", "bg1", 0.2f, 1, "S2S"))),
            ServerLog(0, "req2", "pub1", "svc1", "au1", Seq(Response("5c29a7ba77bd856e4889d8db", "place1", "bg1", 0.1f, 1, "S2S"), Response("5d6773e077bd856e48f98c72", "place2", "bg1", 0.2f, 1, "S2S"))),
            ServerLog(0, "req3", "pub1", "svc1", "au2", Seq(Response("5c29a7ba77bd856e4889d8db", "place1", "bg1", 0.1f, 1, "S2S"), Response("5d6773e077bd856e48f98c72", "place2", "bg1", 0.2f, 1, "S2S"))),
            ServerLog(0, "req4", "pub1", "svc3", "au3", Seq(Response("5c29a7ba77bd856e4889d8db", "place1", "bg1", 0.1f, 1, "S2S"), Response("5d6773e077bd856e48f98c72", "place2", "bg1", 0.2f, 1, "S2S"))),
            ServerLog(0, "req5", "pub1", "svc1", "au1", Seq(Response("5c29a7ba77bd856e4889d8db", "place1", "bg1", 0.1f, 1, "S2S"), Response("5d6773e077bd856e48f98c72", "place2", "bg1", 0.2f, 1, "S2S"))),
            ServerLog(0, "req6", "pub1", "svc1", "au1", Seq(Response("5c29a7ba77bd856e4889d8db", "place1", "bg1", 0.1f, 1, "S2S"), Response("5d6773e077bd856e48f98c72", "place2", "bg1", 0.2f, 1, "S2S"))),
            ServerLog(0, "req7", "pub1", "svc1", "au1", Seq(Response("5c29a7ba77bd856e4889d8db", "place1", "bg1", 0.1f, 1, "S2S"), Response("5d6773e077bd856e48f98c72", "place2", "bg1", 0.2f, 1, "S2S"))),
            ServerLog(0, "req8", "pub1", "svc1", "au1", Seq(Response("5c29a7ba77bd856e4889d8db", "place1", "bg1", 0.1f, 1, "S2S"), Response("5d6773e077bd856e48f98c72", "place2", "bg1", 0.2f, 1, "S2S"))),
        ).toDF()
//        println("---------------------------------------- server df schema")
//        serverDf.printSchema()
//        serverDf.show()

        val clientDf = Seq(
            ClientLogWithBidPrice(0, "1", 1, "req1", "pub1", "svc1", "au1", "5c29a7ba77bd856e4889d8db", "place1", "bg1", 0.3f, 0.1f, 0.2f, 1, "C2S"),
            ClientLogWithBidPrice(0, "1", 1, "req2", "pub1", "svc1", "au1", "5d6773e077bd856e48f98c72", "place1", "bg2", 0.3f, 0.1f, 0.2f, 1, "C2S"),
            ClientLogWithBidPrice(0, "1", 1, "req3", "pub1", "svc1", "au2", "5d6773e077bd856e48f98c72", "place1", "bg1", 0.3f, 0.1f, 0.2f, 1, "C2S"),
            ClientLogWithBidPrice(0, "1", 1, "req4", "pub1", "svc3", "au3", "5d6773e077bd856e48f98c72", "place1", "bg1", 0.3f, 0.1f, 0.2f, 1, "C2S"),
            ClientLogWithBidPrice(0, "1", 11, "req4", "pub1", "svc3", "au3", "5d6773e077bd856e48f98c72", "place1", "bg1", 0.3f, 0.1f, 0.2f, 1, "C2S"),
        ).toDF()
        //        println("---------------------------------------- client df schema")
        //        clientDf.printSchema()
        //        clientDf.show()

        //        val aggregator = new CmsAdProviderPlaceBiddingGroupAggregator("20230101", serverDf, clientDf)
        //
        //        // ==================== step by step check
        //
        //        val aggDf = aggregator.aggregate()
        //        aggDf.printSchema()
        //        aggDf.show(truncate = false)
    }
}
