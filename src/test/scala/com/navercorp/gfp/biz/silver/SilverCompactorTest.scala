package com.navercorp.gfp.biz.silver

import com.navercorp.gfp.helper.FunSuiteWithSparkSession
import org.junit.runner.RunWith
import org.scalatest.BeforeAndAfterAll
import org.scalatestplus.junit.JUnitRunner

@RunWith(classOf[JUnitRunner])
class SilverCompactorTest extends FunSuiteWithSparkSession with BeforeAndAfterAll {
    test("silver compactor") {
        withSparkSession(this.getClass.getSimpleName) { implicit spark =>
            SilverCompactor.main(Array())
            assert(1==1)
        }
    }
}
