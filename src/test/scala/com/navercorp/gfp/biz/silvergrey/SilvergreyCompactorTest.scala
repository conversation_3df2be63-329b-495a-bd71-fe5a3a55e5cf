package com.navercorp.gfp.biz.silvergrey

import org.apache.logging.log4j.{LogManager, Logger}
import org.junit.runner.RunWith
import org.scalatest.BeforeAndAfterAll
import org.scalatestplus.junit.JUnitRunner

import com.navercorp.gfp.helper.FunSuiteWithSparkSession

@RunWith(classOf[JUnitRunner])
class SilvergreyCompactorTest extends FunSuiteWithSparkSession with BeforeAndAfterAll {
    protected val logger: Logger = LogManager.getLogger(this.getClass)

    val silvergreyDao = new SilvergreyDao

    test("check SilvergreySchedule Complete") {
        val result = silvergreyDao.findSilvergreyScheduleNotComplete("20240101")

        logger.debug(s"result= $result")

        result match {
            case Some(doc) if !doc.isEmpty => logger.debug(">>>> not complete")
            case None => logger.debug(">>>> all complete")
            case _ =>
        }
    }
}
