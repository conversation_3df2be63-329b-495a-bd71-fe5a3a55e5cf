package com.navercorp.gfp.helper

import org.apache.spark.sql.SparkSession

trait SparkTestHelper {
    def createSparkSession(appName: String): SparkSession = {
        val spark = SparkSession
            .builder()
            .appName(appName)
            .master("local[*]")
            .getOrCreate()
        spark
    }

    def withSparkSession(appName: String)(fn: SparkSession => Unit) {
        val spark = createSparkSession(appName)
        fn(spark)
    }
}
