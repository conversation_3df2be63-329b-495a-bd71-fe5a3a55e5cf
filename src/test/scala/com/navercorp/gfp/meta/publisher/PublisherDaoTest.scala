package com.navercorp.gfp.meta.publisher

import com.mongodb.client.model.Filters
import org.bson.conversions.Bson

import com.navercorp.gfp.helper.FunSuiteWithSparkSession
import org.junit.runner.RunWith
import org.scalamock.scalatest.MockFactory
import org.scalatest.BeforeAndAfter
import org.scalatestplus.junit.JUnitRunner

/**
 *
 * BeforeAndAfterAll: 처음에 한 번, 마지막에 한번, 그래서 stackable
 * BeforeAndAfterEach: 매 테스트를 할 때 마다 수행
 */
@RunWith(classOf[JUnitRunner])
class PublisherDaoTest extends FunSuiteWithSparkSession with BeforeAndAfter with MockFactory {
	val publisherDao = new PublisherDao

	before {
		//        val mockClient = stub[MongoClient]
		//        val mockDB = stub[MongoDatabase]
		//        val mockCollection = stub[MongoCollection[Publisher]]
		//
		//
		//        (mockClient.getDatabase _).when("ssp-test").returns(mockDB)
		//        (mockDB.getCollection[Publisher] _).when("Publishers", classOf[Publisher]).returns(mockCollection)
		//
		//
		//        val iterable = stub[FindIterable[Publisher]]
		//        val cursor = stub[MongoCursor[_]]
		//        val pub1 = Publisher(new ObjectId("579397d20c2dd41b9a8a09eb"), "pub1")
		//
		//        val pubs: FindIterable[Publisher] = stub[FindIterable[Publisher]]
		//
		//        (mockCollection.find() _).
		//        (iterable.iterator _).when().returns(cursor)
		//        (cursor.hasNext _).when().returns(true).returns(false)
		//        (cursor.next _).when().returns(pub1)
		//
		//        val found = wrapper.findByLastName("pub1")
		//
		//        assert(pub1 == found.get(0))
		//
		//        val pubs: FindIterable[Publisher] = stub[FindIterable[Publisher]]
		//        val pubDaodStub = stub[PublisherDao]
		//        (pubDaodStub.getAllPublishers _).when().returns()
	}

	after {
	}

	test("get all publishers") {
		val filter: Bson = Filters.and(Filters.eq("status", "ON"), Filters.eq("keyGroups.types", "REVENUE_SHARE"))
		val pubIds = publisherDao.getPublisherIds(filter)
		pubIds.foreach(pubId => println(pubId))
	}

	test("get publisher by id") {
		val pub = publisherDao.getPublisher("5be0f4c7ad288d002bf54dc2", Seq("name", "corporationType", "doohUse"))
		println(s"pub.doohUse=${pub.doohUse.getOrElse(0)}")
	}
}
