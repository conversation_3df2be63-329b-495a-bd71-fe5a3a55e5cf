package com.navercorp.gfp.scalatestattempt

import org.junit.runner.RunWith
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.{BeforeAndAfter, BeforeAndAfterEach, Suite}
import org.scalatestplus.junit.JUnitRunner

trait User1 extends BeforeAndAfterEach { this: Suite =>

    override def beforeEach() {
        println("User1 beforeEach start")
        super.beforeEach() // To be stackable, must call super.beforeEach
        println("User1 beforeEach end")
    }

    override def afterEach() {
        println("User1 afterEach start")
        try {
            super.afterEach() // To be stackable, must call super.afterEach
        }
        finally {
            println("User1 afterEach end")
        }
    }
}

trait User2 extends BeforeAndAfterEach { this: Suite =>

    override def beforeEach() {
        println("User2 beforeEach start")
        super.beforeEach() // To be stackable, must call super.beforeEach
        println("User2 beforeEach end")
    }

    override def afterEach() {
        println("User2 afterEach start")
        try {
            super.afterEach() // To be stackable, must call super.afterEach
        }
        finally {
            println("User2 afterEach end")
        }
    }
}

@RunWith(classOf[JUnitRunner])
class ExampleSpec extends AnyFunSuite with User1 with User2 with BeforeAndAfter {

    before {
        println("ExampleSpec's before clause")
    }

    after {
        println("ExampleSpec's after clause")
    }

    test("can be stacked") {
        println("IN TEST: Traits in Scala can be stacked")
    }

    test("can be understood, hopefully") {
        println("IN TEST: Traits in Scala can be understood, hopefully")
    }
}

@RunWith(classOf[JUnitRunner])
class ExampleSpec2 extends AnyFunSpec with BeforeAndAfter with User1 with User2 {

    before {
        println("ExampleSpec's before clause")
    }

    after {
        println("ExampleSpec's after clause")
    }

    describe("Traits in Scala") {

        it("can be stacked") {
            println("IN TEST: Traits in Scala can be stacked")
        }
        it("can be understood, hopefully") {
            println("IN TEST: Traits in Scala can be understood, hopefully")
        }
    }
}
