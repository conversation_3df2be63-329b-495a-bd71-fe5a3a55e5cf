package com.navercorp.gfp.util

import com.navercorp.gfp.helper.FunSuiteWithSparkSession
import org.apache.http.client.methods.{CloseableHttpResponse, HttpPost}
import org.apache.http.entity.StringEntity
import org.apache.http.util.EntityUtils
import org.junit.runner.RunWith
import org.scalatest.BeforeAndAfterAll
import org.scalatestplus.junit.JUnitRunner

@RunWith(classOf[JUnitRunner])
class HttpClientTest extends FunSuiteWithSparkSession with BeforeAndAfterAll {
    test("httpRequest") {
        SimpleHttpClient().withHttpClient { httpClient =>
            val url = "https://dev-apis.nclavis.navercorp.com/kms/consumer/jF7QhngykYISI1fkV30aLao0JNk=/decrypt/inline"

            val map = Map("inlineCipherText" -> "1:etLBx5-vr_CAF39i:6jPamcZVBYq2hnoZuJ5Ai1iaN9RBMgS7pN3H3kmL2XL3HXOO66Jn_IAeDBZVJ4PnOOvX51qDPTbOfr9m0zuzxHFWP_uJUTVPnpof_E9GKD-BDIdF2DOvguoQrQliJrOriJiIaMPMZf5yuAA6hosq6qhQHVcDA9aAxrP9cKE=")
            val body = JsonUtil.toJson(map)
            println(s"[NCLAVIS_REQ]: body=$body")


            val httpPost = new HttpPost(url)
            val requestEntity = new StringEntity(body, "utf-8")
            httpPost.setEntity(requestEntity)
            httpPost.addHeader("Accept", "application/json;charset=UTF-8")
            httpPost.addHeader("Content-Type", "application/json")

            // Token-Based 인증 설정 https://yobi.navercorp.com/nClavis_guide/posts/4#yb-header--token-based-%EC%9D%B8%EC%A6%9D-%EC%84%A4%EC%A0%95
            // NEOID USER TOKEN은 유효기간이 있으므로 만료되면 다시 발급해야 함.
            httpPost.addHeader("nclavis-access-token", "rjn3/dDiryNDXU+q53miIiOijQ4aLfh2k7sD/zSw0+YVpn7KaoJmASr0vQBrnoxhMF0tpTMVC8w9ocNFW5PCu+LLlEmnDeSDzvjK+nq1ScBmg9dTzugkCjN54aFCRknZA3fQgKlAIwBRHeEncqon9FzRpUeVYr6w3Aits//OswiT62+JkD9fOIXCgqxlcOhyeZ/4ts46lEMvHJ4GPDa1UD/a0TPTUdfSDCeNkoSrGUi+bSghnAonK6HKT4y9gdmMXwY+GmpjnR0tuva+IiNe48IePFAT5DfNHrwKcwEjJ0oBoDxqjkHHAABmtYkH0JTxCyNO7AvGhZuNt7Dn0ny23kHLW8e9NFCNhkCt2A+5ORODkTFnwCFCnr0/IbLK/RzcDwXTCn1BufvoQ1IswLATHA==")

            val httpResponse: CloseableHttpResponse = httpClient.execute(httpPost)
            if (httpResponse != null) {
                val statusCode = httpResponse.getStatusLine.getStatusCode
                println(s"[NCLAVIS_REQ] status=$statusCode")

                val json = EntityUtils.toString(httpResponse.getEntity, "UTF-8")
                println(json)

                httpClient.close()
                statusCode
            } else {
                println(s"[NCLAVIS_REQ] ERROR_RES_IS_NULL]")
                SimpleHttpClientStatus.STATUS_ERROR
            }
        }
    }

}
