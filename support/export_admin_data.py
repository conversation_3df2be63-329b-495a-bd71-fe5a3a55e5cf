import argparse
import logging
import subprocess
from typing import Iterable, List

_DATA_TYPE_VARIABLE = 'variables'
_DATA_TYPE_CONNECTION = 'connections'
_DATA_TYPE_POOL = 'pools'


def normalize_args(args: Iterable[str]) -> List[str]:
    """
    command 로 입력된 resource type 정보를 정규화

    :param args:
    :return:
    """
    target_resources = []
    for resource in args:
        if _DATA_TYPE_CONNECTION.startswith(resource.lower()):
            target_resources.append(_DATA_TYPE_CONNECTION)
        elif _DATA_TYPE_VARIABLE.startswith(resource.lower()):
            target_resources.append(_DATA_TYPE_VARIABLE)
        elif _DATA_TYPE_POOL.startswith(resource.lower()):
            target_resources.append(_DATA_TYPE_POOL)
        else:
            raise Exception(f'잘못된 resource 타입입니다. {resource}')

    return target_resources


if __name__ == '__main__':
    logging.basicConfig(format='%(asctime)s [%(levelname)s] %(message)s', level=logging.INFO)

    parser = argparse.ArgumentParser()
    parser.add_argument('--profile', required=True, help='environment to export')
    parser.add_argument('--pod', required=True, help='pod name for airflow scheduler (recommend)')
    parser.add_argument('resource_type', nargs='+', help='resource types to migrate, "variables|connections|pools"')

    args = parser.parse_args()
    profile = args.profile
    pod_name = args.pod
    resource_types = normalize_args(args.resource_type)

    for resource_type in resource_types:
        # profile 에 따라 local 인 경우 docker-compose, 그 외엔 kubectl 을 이용하여
        # pod 내부에서 airflow 명령어를 실행해 resource 를 기재한 pod 내의 container local 에 저장
        proc = subprocess.run(
            ["bash", "-c", f'{"docker/docker-compose" if profile == "local" else "kubectl"} exec -it {pod_name} '
                           f'{"" if profile == "local" else "--"} airflow {resource_type} export {resource_type}.json'],
            capture_output=True,
            text=True,
            timeout=600
        )
        logging.info(f'STDOUT:\n{proc.stdout}')
        logging.error(f'STDERR:\n{proc.stderr}')

        # 위와 같이 profile 에 따라 명령어가 다르며
        # container 내부에 저장된 json 파일을 본 스크립트를 실행한 local 환경으로 복사
        proc = subprocess.run(
            ["bash", "-c", f'{"docker/docker-compose" if profile == "local" else "kubectl"} exec -it {pod_name} '
                           f'{"" if profile == "local" else "--"} cat /opt/airflow/{resource_type}.json '
                           f'> admin-data/{profile}/{resource_type}.json'],
            capture_output=True,
            text=True,
            timeout=600
        )
        logging.info(f'STDOUT:\n{proc.stdout}')
        logging.error(f'STDERR:\n{proc.stderr}')
