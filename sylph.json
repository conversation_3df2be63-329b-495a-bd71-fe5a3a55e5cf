[{"$match": {"publisher_id": {"$oid": "5d81f8d435c2b0001d48166a"}, "reportApiKeyGroup_id": {"$exists": true}}}, {"$project": {"publisher_id": "$publisher_id", "adProvider_id": "$adProvider_id", "reportApiKeyGroup_id": "$reportApiKeyGroup_i"}}, {"$lookup": {"from": "SyncPublishers", "localField": "publisher_id", "foreignField": "_id", "as": "pub"}}, {"$unwind": "$pub"}, {"$project": {"publisher_id": "$publisher_id", "adProvider_id": "$adProvider_id", "reportApiKeyGroup_id": "$reportApiKeyGroup_id", "keyGroups": {"$filter": {"input": "$pub.keyGroups", "as": "kg", "cond": {"$eq": ["$reportApiKeyGroup_id", "$$kg._id"]}}}}}, {"$unwind": "$keyGroups"}, {"$project": {"publisherId": "'$publisher_id'", "adProviderId": "'$adProvider_id'", "key": {"$arrayElemAt": ["$keyGroups.keys", 0]}}}]